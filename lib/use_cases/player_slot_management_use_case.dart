import 'package:get_it/get_it.dart';
import 'package:common/models/player_slot.dart';
import 'package:dauntless/api/dtos/create_match_dto.dart';
import 'package:common/models/player_type.dart';
import 'package:common/models/game_match.dart';
import 'package:common/models/converters/generate_id_if_needed_converter.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/use_cases/interfaces/player_slot_management_use_case_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';

/// Concrete implementation of player slot management operations
/// Handles player slot CRUD operations, validation, and constraints
class PlayerSlotManagementUseCase implements IPlayerSlotManagementUseCase {
  final Map<String, MatchRepositoryInterface> _repositories;
  final UserManager _userManager;
  final RemoteLogger _logger;

  PlayerSlotManagementUseCase(
    this._repositories,
    this._userManager,
    this._logger,
  );

  @override
  Result<List<PlayerSlot>> addPlayerSlot(List<PlayerSlot> currentSlots, GameConfig config) {
    try {
      // Check if we can add more slots
      if (!canAddPlayerSlot(currentSlots, config)) {
        return Result.failure('Maximum number of player slots reached');
      }

      // Get available player classes
      final availableClasses = config.playerClasses;
      if (availableClasses.isEmpty) {
        return Result.failure('No player classes available');
      }

      // Create new slot with first available player class
      final newSlot = PlayerSlot(
        id: GenerateIdIfNeededConverter().fromJson(null),
        type: PlayerType.humanLocal,
        playerClassId: availableClasses.first.id,
        name: 'Player ${currentSlots.length + 1}',
      );

      final updatedSlots = List<PlayerSlot>.from(currentSlots)..add(newSlot);
      
      _logger.info('Added new player slot: ${newSlot.id}');
      return Result.success(updatedSlots);

    } catch (e) {
      _logger.error('Error adding player slot: $e');
      return Result.failure('Error adding player slot: $e');
    }
  }

  @override
  Result<List<PlayerSlot>> removePlayerSlot(List<PlayerSlot> currentSlots, int index) {
    try {
      // Validate index
      if (index < 0 || index >= currentSlots.length) {
        return Result.failure('Invalid slot index');
      }

      // Check if we can remove this slot
      if (!canRemovePlayerSlot(currentSlots, index, null)) {
        return Result.failure('Cannot remove slot - minimum slots required');
      }

      final updatedSlots = List<PlayerSlot>.from(currentSlots)..removeAt(index);
      
      _logger.info('Removed player slot at index: $index');
      return Result.success(updatedSlots);

    } catch (e) {
      _logger.error('Error removing player slot: $e');
      return Result.failure('Error removing player slot: $e');
    }
  }

  @override
  Future<Result<PlayerSlotUpdateResult>> updatePlayerType(UpdatePlayerTypeRequest request) async {
    try {
      // Validate request
      if (request.slotIndex < 0 || request.slotIndex >= request.currentSlots.length) {
        return Result.failure('Invalid slot index');
      }

      // Create updated slots
      final updatedSlots = List<PlayerSlot>.from(request.currentSlots);
      final currentSlot = updatedSlots[request.slotIndex];
      
      updatedSlots[request.slotIndex] = currentSlot.copyWith(type: request.newType);

      // Determine if network match is required
      final requiresNetworkMatch = updatedSlots.any((slot) =>
          slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);

      final repositoryType = requiresNetworkMatch ? 'network' : 'local';

      // Handle server operations for network matches
      GameMatch? updatedMatch;
      if (repositoryType == 'network') {
        try {
          final serverRepository = GetIt.I<ServerRepository>();
          final user = _userManager.state.user;

          if (user != null) {
            if (request.isSelectedMatch && request.matchId != null) {
              // Update existing match on server
              final success = await serverRepository.updateMatchPlayerSlots(
                request.matchId!,
                updatedSlots,
                user.id,
              );
              if (!success) {
                _logger.warn('Failed to update match player slots on server');
              }
            } else if (!request.isSelectedMatch && request.matchId == null) {
              // Create new match on server for network play
              _logger.info('Creating new network match with updated player slots');
              final createDto = CreateMatchDto(
                gameTypeId: request.gameConfig.id,
                creatorId: user.id,
                gameName: 'New Match', // Default name
                playerSlots: updatedSlots,
              );
              final newMatch = await serverRepository.createMatch(createDto);
              if (newMatch != null) {
                updatedMatch = newMatch;
                _logger.info('Created new network match: ${newMatch.id}');
              } else {
                _logger.warn('Failed to create new network match');
              }
            }
          }
        } catch (e) {
          _logger.error('Error handling server operations: $e');
          // Continue with local update even if server operations fail
        }
      }

      final result = PlayerSlotUpdateResult(
        updatedSlots: updatedSlots,
        requiresNetworkMatch: requiresNetworkMatch,
        repositoryType: repositoryType,
        updatedMatch: updatedMatch,
      );

      _logger.info('Updated player type for slot ${request.slotIndex} to ${request.newType}');
      return Result.success(result);

    } catch (e) {
      _logger.error('Error updating player type: $e');
      return Result.failure('Error updating player type: $e');
    }
  }

  @override
  Future<Result<bool>> joinPlayerSlot(JoinSlotRequest request) async {
    try {
      // Validate request
      if (request.slotIndex < 0 || request.slotIndex >= request.currentSlots.length) {
        return Result.failure('Invalid slot index');
      }

      // Get current user
      final user = _userManager.state.user;
      if (user == null) {
        return Result.failure('No user logged in');
      }

      // Update the slot with user information
      final updatedSlots = List<PlayerSlot>.from(request.currentSlots);
      final currentSlot = updatedSlots[request.slotIndex];
      
      updatedSlots[request.slotIndex] = currentSlot.copyWith(
        playerId: user.id,
        name: 'Player ${request.slotIndex + 1}', // User model only has id, not name
      );

      // If this is for a match, update it
      if (request.matchId != null) {
        // Determine repository type
        final repositoryType = updatedSlots.any((slot) =>
            slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork) ? 'network' : 'local';
        
        final repository = _repositories[repositoryType];
        if (repository != null && repositoryType == 'network') {
          try {
            final serverRepository = GetIt.I<ServerRepository>();
            final success = await serverRepository.updateMatchPlayerSlots(
              request.matchId!,
              updatedSlots,
              user.id,
            );
            if (!success) {
              return Result.failure('Failed to join player slot');
            }
          } catch (e) {
            return Result.failure('Failed to update server: $e');
          }
        }
      }

      _logger.info('User ${user.id} joined slot ${request.slotIndex}');
      return Result.success(true);

    } catch (e) {
      _logger.error('Error joining player slot: $e');
      return Result.failure('Error joining player slot: $e');
    }
  }

  @override
  List<PlayerSlot> createDefaultSlots(GameConfig config) {
    final availableClasses = config.playerClasses;
    if (availableClasses.isEmpty) {
      _logger.warn('No player classes available for creating default slots');
      return [];
    }

    // Create minimum required slots (usually 2 for most games)
    final minSlots = 2; // Default to 2 since GameConfig doesn't have minPlayers
    final slots = <PlayerSlot>[];

    for (int i = 0; i < minSlots; i++) {
      final playerClass = availableClasses[i % availableClasses.length];
      slots.add(PlayerSlot(
        id: GenerateIdIfNeededConverter().fromJson(null),
        type: PlayerType.humanLocal,
        playerClassId: playerClass.id,
        name: 'Player ${i + 1}',
      ));
    }

    _logger.info('Created ${slots.length} default player slots');
    return slots;
  }

  @override
  Result<void> validatePlayerSlots(List<PlayerSlot> slots, GameConfig config) {
    if (slots.isEmpty) {
      return Result.failure('At least one player slot is required');
    }

    final minPlayers = 1; // Default since GameConfig doesn't have minPlayers
    final maxPlayers = 8; // Default since GameConfig doesn't have maxPlayers

    if (slots.length < minPlayers) {
      return Result.failure('Minimum $minPlayers players required');
    }

    if (slots.length > maxPlayers) {
      return Result.failure('Maximum $maxPlayers players allowed');
    }

    return Result.success(null);
  }

  @override
  bool canAddPlayerSlot(List<PlayerSlot> currentSlots, GameConfig config) {
    final maxPlayers = 8; // Default since GameConfig doesn't have maxPlayers
    return currentSlots.length < maxPlayers;
  }

  @override
  bool canRemovePlayerSlot(List<PlayerSlot> currentSlots, int index, GameConfig? config) {
    if (index < 0 || index >= currentSlots.length) return false;

    final minPlayers = 1; // Default since GameConfig doesn't have minPlayers
    return currentSlots.length > minPlayers;
  }

  @override
  Result<List<PlayerSlot>> updatePlayerName(List<PlayerSlot> currentSlots, int slotIndex, String name) {
    try {
      if (slotIndex < 0 || slotIndex >= currentSlots.length) {
        return Result.failure('Invalid slot index');
      }

      if (name.trim().isEmpty) {
        return Result.failure('Player name cannot be empty');
      }

      final updatedSlots = List<PlayerSlot>.from(currentSlots);
      final currentSlot = updatedSlots[slotIndex];
      
      updatedSlots[slotIndex] = currentSlot.copyWith(name: name.trim());

      _logger.info('Updated player name for slot $slotIndex to: $name');
      return Result.success(updatedSlots);

    } catch (e) {
      _logger.error('Error updating player name: $e');
      return Result.failure('Error updating player name: $e');
    }
  }

  @override
  Result<List<PlayerSlot>> setHostPlayer(List<PlayerSlot> currentSlots, String slotId) {
    try {
      final slotIndex = currentSlots.indexWhere((slot) => slot.id == slotId);
      if (slotIndex == -1) {
        return Result.failure('Player slot not found');
      }

      // For now, just return the same slots since host designation might be handled differently
      // This could be extended to mark a slot as host if needed
      _logger.info('Set host player for slot: $slotId');
      return Result.success(currentSlots);

    } catch (e) {
      _logger.error('Error setting host player: $e');
      return Result.failure('Error setting host player: $e');
    }
  }
}
