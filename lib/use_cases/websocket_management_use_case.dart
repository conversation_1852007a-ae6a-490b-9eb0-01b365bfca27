import 'dart:async';
import 'package:common/models/game_match.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/results/match_management_results.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/use_cases/interfaces/websocket_management_use_case_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/server_notifications_use_case.dart';
import 'package:injectable/injectable.dart';

/// Concrete implementation of WebSocket subscription management and real-time update processing
/// Extracted from MatchManagementBloc to improve testability and separation of concerns
@singleton
class WebSocketManagementUseCase implements WebSocketManagementUseCaseInterface {
  final ServerNotificationsUseCase _serverNotificationsUseCase;
  final RemoteLogger _logger;

  // Active WebSocket subscription
  StreamSubscription<List<GameMatch>>? _webSocketSubscription;

  WebSocketManagementUseCase(
    this._serverNotificationsUseCase,
    this._logger,
  );

  @override
  Future<Result<StreamSubscription<List<GameMatch>>>> setupWebSocketListener() async {
    try {
      _logger.info('Setting up WebSocket listener for real-time match updates');

      // Cancel any existing subscription first
      await teardownWebSocketListener();

      // Listen for open matches updates from the WebSocket
      _webSocketSubscription = _serverNotificationsUseCase.openMatchesUpdates.listen(
        (matches) {
          _logger.info('🔄 WEBSOCKET: Received ${matches.length} matches for processing');
          // The subscription itself doesn't process the matches - that's handled by the caller
          // This allows the BLoC to receive the updates and process them through events
        },
        onError: (error) {
          _logger.error('Error in WebSocket listener: $error');
        },
        onDone: () {
          _logger.info('WebSocket listener stream closed');
          _webSocketSubscription = null;
        },
      );

      _logger.info('WebSocket listener set up successfully');
      return Result.success(_webSocketSubscription!);
    } catch (e) {
      _logger.error('Failed to set up WebSocket listener: $e');
      return Result.failure('Failed to set up WebSocket listener: $e');
    }
  }

  @override
  Future<Result<bool>> teardownWebSocketListener() async {
    try {
      _logger.info('Tearing down WebSocket listener');

      // Cancel the BLoC-level WebSocket subscription
      _webSocketSubscription?.cancel();
      _webSocketSubscription = null;

      // Unsubscribe from server notifications
      _serverNotificationsUseCase.unsubscribeFromOpenMatches();

      _logger.info('WebSocket listener torn down successfully');
      return Result.success(true);
    } catch (e) {
      _logger.error('Failed to tear down WebSocket listener: $e');
      return Result.failure('Failed to tear down WebSocket listener: $e');
    }
  }

  @override
  Result<MatchUpdateResult> processWebSocketUpdate(
    List<GameMatch> matches,
    Map<String, List<GameMatch>> currentMatchesBySource,
    GameMatch? currentSelectedMatch,
  ) {
    try {
      _logger.info('Processing WebSocket update with ${matches.length} matches');

      // Validate the update first
      if (!validateWebSocketUpdate(matches)) {
        return Result.failure('Invalid WebSocket update data');
      }

      // Find the network source name
      final networkSourceName = getNetworkSourceName(currentMatchesBySource);
      if (networkSourceName == null) {
        _logger.warn('No network source found for WebSocket updates');
        return Result.failure('No network source available');
      }

      // Update the matches by source
      final updatedMatchesBySource = Map<String, List<GameMatch>>.from(currentMatchesBySource);
      updatedMatchesBySource[networkSourceName] = matches;

      // Rebuild the complete matches list
      final allMatches = rebuildCompleteMatchesList(updatedMatchesBySource);

      // Update selected match if needed
      updateSelectedMatchFromWebSocket(matches, currentSelectedMatch);

      _logger.info('WebSocket update processed: ${matches.length} matches from $networkSourceName');

      return Result.success(MatchUpdateResult(
        updatedMatches: allMatches,
        sourceName: networkSourceName,
        requiresStateUpdate: true,
      ));
    } catch (e) {
      _logger.error('Error processing WebSocket update: $e');
      return Result.failure('Error processing WebSocket update: $e');
    }
  }

  @override
  Result<GameMatch> synchronizeMatchState(GameMatch localMatch, GameMatch serverMatch) {
    try {
      // For now, server version takes precedence
      // In the future, we could implement more sophisticated conflict resolution
      if (serverMatch.updatedAt >= localMatch.updatedAt) {
        _logger.info('Using server version of match ${serverMatch.id} (server: ${serverMatch.updatedAt}, local: ${localMatch.updatedAt})');
        return Result.success(serverMatch);
      } else {
        _logger.info('Using local version of match ${localMatch.id} (local: ${localMatch.updatedAt}, server: ${serverMatch.updatedAt})');
        return Result.success(localMatch);
      }
    } catch (e) {
      _logger.error('Error synchronizing match state: $e');
      return Result.failure('Error synchronizing match state: $e');
    }
  }

  @override
  bool validateWebSocketUpdate(List<GameMatch> matches) {
    try {
      // Basic validation - ensure all matches have required fields
      for (final match in matches) {
        if (match.id.isEmpty) {
          _logger.warn('WebSocket update contains match with empty ID');
          return false;
        }
        if (match.gameTypeId.isEmpty) {
          _logger.warn('WebSocket update contains match with empty gameTypeId');
          return false;
        }
      }
      return true;
    } catch (e) {
      _logger.error('Error validating WebSocket update: $e');
      return false;
    }
  }

  @override
  String? getNetworkSourceName(Map<String, dynamic> repositories) {
    try {
      if (repositories is Map<String, MatchRepositoryInterface>) {
        // Find the first repository that supports real-time updates
        for (final entry in repositories.entries) {
          if (entry.value.supportsRealTimeUpdates) {
            return entry.key;
          }
        }
      } else if (repositories is Map<String, List<GameMatch>>) {
        // If this is matchesBySource, find network sources by name
        for (final sourceName in repositories.keys) {
          if (sourceName.toLowerCase().contains('network') ||
              sourceName.toLowerCase().contains('server')) {
            return sourceName;
          }
        }
      }
      return null;
    } catch (e) {
      _logger.error('Error getting network source name: $e');
      return null;
    }
  }

  @override
  GameMatch? updateSelectedMatchFromWebSocket(
    List<GameMatch> webSocketMatches,
    GameMatch? currentSelectedMatch,
  ) {
    if (currentSelectedMatch == null) {
      return null;
    }

    try {
      // Find the updated version of the selected match
      final matchingMatch = webSocketMatches.firstWhere(
        (match) => match.id == currentSelectedMatch.id,
        orElse: () => currentSelectedMatch,
      );

      if (matchingMatch.id == currentSelectedMatch.id && matchingMatch != currentSelectedMatch) {
        _logger.info('🔄 WEBSOCKET: Updated selectedMatch ${matchingMatch.id}');
        return matchingMatch;
      }

      return currentSelectedMatch;
    } catch (e) {
      _logger.error('Error updating selected match from WebSocket: $e');
      return currentSelectedMatch;
    }
  }

  @override
  List<GameMatch> rebuildCompleteMatchesList(Map<String, List<GameMatch>> matchesBySource) {
    try {
      return matchesBySource.values.expand((list) => list).toList();
    } catch (e) {
      _logger.error('Error rebuilding complete matches list: $e');
      return [];
    }
  }

  /// Dispose method to clean up resources
  Future<void> dispose() async {
    await teardownWebSocketListener();
  }
}
