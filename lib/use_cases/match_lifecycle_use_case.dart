import 'package:common/models/game_match.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart' as requests;
import 'package:dauntless/use_cases/interfaces/match_lifecycle_use_case_interface.dart';
import 'package:dauntless/use_cases/interfaces/match_management_use_case_interface.dart' as mgmt;
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:injectable/injectable.dart';
import 'package:uuid/uuid.dart';

/// Concrete implementation of match lifecycle management operations
/// Extracted from MatchManagementBloc to improve testability and separation of concerns
class MatchLifecycleUseCase implements MatchLifecycleUseCaseInterface {
  final mgmt.IMatchManagementUseCase _matchManagementUseCase;
  final RemoteLogger _logger;
  final Uuid _uuid = const Uuid();

  MatchLifecycleUseCase(
    this._matchManagementUseCase,
    this._logger,
  );

  @override
  Future<Result<MatchCreationResult>> createAndStartMatch(CreateAndStartMatchRequest request) async {
    try {
      _logger.info('Creating and starting match: ${request.gameName}');

      // Validate the request
      final validationResult = validateMatchCreationRequest(request);
      if (validationResult.isFailure) {
        return Result.failure(validationResult.error);
      }

      // Generate match ID if not provided
      final matchIdResult = request.customMatchId != null 
          ? Result.success(request.customMatchId!)
          : generateMatchId();
      
      if (matchIdResult.isFailure) {
        return Result.failure(matchIdResult.error);
      }

      // Create the match using the existing match management use case
      final createRequest = requests.CreateMatchRequest(
        gameConfig: request.gameConfig,
        playerSlots: request.playerSlots,
        gameName: request.gameName,
        openForJoining: request.openForJoining,
      );

      final createResult = await _matchManagementUseCase.createMatch(createRequest);
      if (createResult.isFailure) {
        return Result.failure(createResult.error);
      }

      final createdMatch = createResult.value;
      _logger.info('Successfully created match: ${createdMatch.id}');

      // Handle post-creation coordination
      final coordinationResult = handlePostCreationCoordination(createdMatch, request);
      if (coordinationResult.isFailure) {
        _logger.warn('Post-creation coordination failed: ${coordinationResult.error}');
        // Don't fail the entire operation, just log the warning
      }

      final actions = coordinationResult.isSuccess 
          ? coordinationResult.value 
          : PostCreationActions(
              shouldRefreshData: true,
              shouldSelectMatch: true,
              shouldEnterSelectionMode: false,
            );

      return Result.success(MatchCreationResult(
        createdMatch: createdMatch,
        requiresDataRefresh: actions.shouldRefreshData,
        shouldSelectMatch: actions.shouldSelectMatch,
        successMessage: 'Successfully created match: ${createdMatch.id}',
      ));
    } catch (e) {
      _logger.error('Error creating and starting match: $e');
      return Result.failure('Error creating match: $e');
    }
  }

  @override
  Future<Result<MatchDeletionResult>> deleteMatch(DeleteMatchRequest request) async {
    try {
      _logger.info('Deleting match: ${request.matchId}');

      // Validate the deletion request
      final validationResult = await validateMatchDeletionRequest(request);
      if (validationResult.isFailure) {
        return Result.failure(validationResult.error);
      }

      // Delete the match using the existing match management use case
      final deleteRequest = requests.DeleteMatchRequest(matchId: request.matchId);
      final deleteResult = await _matchManagementUseCase.deleteMatch(deleteRequest);
      
      if (deleteResult.isFailure) {
        return Result.failure(deleteResult.error);
      }

      _logger.info('Successfully deleted match: ${request.matchId}');

      return Result.success(MatchDeletionResult(
        deletedMatchId: request.matchId,
        requiresDataRefresh: true,
        shouldClearSelection: true, // Always clear selection when deleting
        successMessage: 'Successfully deleted match: ${request.matchId}',
      ));
    } catch (e) {
      _logger.error('Error deleting match: $e');
      return Result.failure('Error deleting match: $e');
    }
  }

  @override
  Result<bool> validateMatchCreationRequest(CreateAndStartMatchRequest request) {
    try {
      // Validate game config
      if (request.gameConfig.id.isEmpty) {
        return Result.failure('Game config ID cannot be empty');
      }

      // Validate game name
      if (request.gameName.isEmpty) {
        return Result.failure('Game name cannot be empty');
      }

      // Validate player slots
      if (request.playerSlots.isEmpty) {
        return Result.failure('At least one player slot is required');
      }

      // Validate player slots don't exceed reasonable limits
      if (request.playerSlots.length > 16) { // Reasonable upper limit
        return Result.failure('Too many player slots (max 16)');
      }

      _logger.info('Match creation request validation passed');
      return Result.success(true);
    } catch (e) {
      _logger.error('Error validating match creation request: $e');
      return Result.failure('Error validating request: $e');
    }
  }

  @override
  Result<PostCreationActions> handlePostCreationCoordination(
    GameMatch createdMatch,
    CreateAndStartMatchRequest originalRequest,
  ) {
    try {
      _logger.info('Handling post-creation coordination for match: ${createdMatch.id}');

      // Determine what actions should be taken after creation
      final shouldRefreshData = true; // Always refresh to show the new match
      final shouldSelectMatch = true; // Select the newly created match
      final shouldEnterSelectionMode = false; // Stay in current mode

      return Result.success(PostCreationActions(
        shouldRefreshData: shouldRefreshData,
        shouldSelectMatch: shouldSelectMatch,
        shouldEnterSelectionMode: shouldEnterSelectionMode,
        statusMessage: 'Match created successfully',
      ));
    } catch (e) {
      _logger.error('Error in post-creation coordination: $e');
      return Result.failure('Error in post-creation coordination: $e');
    }
  }

  @override
  Result<PostDeletionActions> handlePostDeletionCleanup(
    String deletedMatchId,
    GameMatch? selectedMatch,
  ) {
    try {
      _logger.info('Handling post-deletion cleanup for match: $deletedMatchId');

      // Determine if we need to clear the selection
      final shouldClearSelection = selectedMatch?.id == deletedMatchId;
      final shouldRefreshData = true; // Always refresh to remove the deleted match

      return Result.success(PostDeletionActions(
        shouldClearSelection: shouldClearSelection,
        shouldRefreshData: shouldRefreshData,
        statusMessage: shouldClearSelection 
            ? 'Match deleted and selection cleared'
            : 'Match deleted successfully',
      ));
    } catch (e) {
      _logger.error('Error in post-deletion cleanup: $e');
      return Result.failure('Error in post-deletion cleanup: $e');
    }
  }

  @override
  Result<String> generateMatchId() {
    try {
      final matchId = _uuid.v4();
      _logger.info('Generated match ID: $matchId');
      return Result.success(matchId);
    } catch (e) {
      _logger.error('Error generating match ID: $e');
      return Result.failure('Error generating match ID: $e');
    }
  }

  @override
  Future<Result<bool>> validateMatchDeletionRequest(DeleteMatchRequest request) async {
    try {
      // Basic validation
      if (request.matchId.isEmpty) {
        return Result.failure('Match ID cannot be empty');
      }

      // Additional validation could be added here:
      // - Check if match exists
      // - Check if user has permission to delete
      // - Check if match is in a deletable state

      _logger.info('Match deletion request validation passed for: ${request.matchId}');
      return Result.success(true);
    } catch (e) {
      _logger.error('Error validating match deletion request: $e');
      return Result.failure('Error validating deletion request: $e');
    }
  }
}
