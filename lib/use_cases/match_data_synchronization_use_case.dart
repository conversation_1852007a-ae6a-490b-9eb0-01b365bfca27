import 'dart:async';
import 'package:get_it/get_it.dart';
import 'package:common/models/game_match.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/repositories/websocket_repository.dart';
import 'package:dauntless/use_cases/interfaces/match_data_synchronization_use_case_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/server_notifications_use_case.dart';

/// Concrete implementation of match data synchronization operations
/// Handles match data loading, caching, real-time updates, and deduplication
class MatchDataSynchronizationUseCase implements IMatchDataSynchronizationUseCase {
  final RemoteLogger _logger;

  // WebSocket integration for real-time match updates
  StreamSubscription<List<GameMatch>>? _openMatchesSubscription;

  MatchDataSynchronizationUseCase(
    this._logger,
  );

  /// Get repositories dynamically based on current DI scope
  Map<String, MatchRepositoryInterface> _getRepositories() {
    final repositories = <String, MatchRepositoryInterface>{};

    // Always try to get local repository
    try {
      final localRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'local');
      repositories['local'] = localRepo;
    } catch (e) {
      _logger.warn('Local repository not available: $e');
    }

    // Try to get network repository if in server scope
    if (GetIt.I.currentScopeName == 'serverConnected') {
      try {
        final networkRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'network');
        repositories['network'] = networkRepo;
      } catch (e) {
        _logger.warn('Network repository not available: $e');
      }
    }

    return repositories;
  }

  @override
  Future<Result<MatchDataResult>> loadMatchData(LoadMatchDataRequest request) async {
    try {
      final gameName = request.gameName ?? 'default';
      _logger.info('Loading match data for game: $gameName');
      
      final allMatches = <GameMatch>[];
      final matchesBySource = <String, List<GameMatch>>{};
      
      // Load matches from all available repositories with deduplication
      final networkMatches = <GameMatch>[];

      // Get current repositories dynamically
      final repositoriesSnapshot = _getRepositories();

      for (final entry in repositoriesSnapshot.entries) {
        final sourceName = entry.key;
        final repository = entry.value;

        try {
          _logger.info('Loading matches from source: $sourceName');
          final matches = await repository.fetchOpenMatches(gameName);
          
          matchesBySource[sourceName] = matches;
          
          // Collect network matches separately for prioritization
          if (repository.supportsRealTimeUpdates) {
            networkMatches.addAll(matches);
          }
          
          _logger.info('Loaded ${matches.length} matches from $sourceName');
        } catch (e) {
          _logger.error('Failed to load matches from $sourceName: $e');
          matchesBySource[sourceName] = [];
        }
      }

      // Deduplicate matches with network priority
      final deduplicatedMatches = deduplicateMatches(matchesBySource);
      allMatches.addAll(deduplicatedMatches);

      final availableSources = repositoriesSnapshot.keys.toList();
      final hasNetworkCapability = repositoriesSnapshot.values.any((repo) => repo.supportsRealTimeUpdates);

      final result = MatchDataResult(
        allMatches: allMatches,
        matchesBySource: matchesBySource,
        availableSources: availableSources,
        hasNetworkCapability: hasNetworkCapability,
      );

      _logger.info('Loaded total of ${allMatches.length} matches from ${availableSources.length} sources');
      return Result.success(result);

    } catch (e) {
      _logger.error('Error loading match data: $e');
      return Result.failure('Error loading match data: $e');
    }
  }

  @override
  Future<Result<SourceRefreshResult>> refreshMatchesFromSource(RefreshMatchesRequest request) async {
    try {
      final repositories = _getRepositories();
      final repository = repositories[request.sourceName];
      if (repository == null) {
        return Result.failure('Source ${request.sourceName} not available');
      }

      final gameName = request.gameName ?? 'default';
      _logger.info('Refreshing matches from source: ${request.sourceName}');

      final matches = await repository.fetchOpenMatches(gameName);
      
      final result = SourceRefreshResult(
        sourceName: request.sourceName,
        matches: matches,
        isNetworkSource: repository.supportsRealTimeUpdates,
      );

      _logger.info('Refreshed ${matches.length} matches from ${request.sourceName}');
      return Result.success(result);

    } catch (e) {
      _logger.error('Error refreshing matches from ${request.sourceName}: $e');
      return Result.failure('Error refreshing matches from ${request.sourceName}: $e');
    }
  }

  @override
  Result<MatchUpdateResult> handleMatchUpdate(List<GameMatch> matches, String sourceName) {
    try {
      _logger.info('Handling match update from $sourceName with ${matches.length} matches');

      final result = MatchUpdateResult(
        updatedMatches: matches,
        sourceName: sourceName,
        requiresStateUpdate: true,
      );

      return Result.success(result);

    } catch (e) {
      _logger.error('Error handling match update from $sourceName: $e');
      return Result.failure('Error handling match update from $sourceName: $e');
    }
  }

  @override
  List<GameMatch> deduplicateMatches(Map<String, List<GameMatch>> matchesBySource) {
    final seenMatchIds = <String>{};
    final deduplicatedMatches = <GameMatch>[];

    // Prioritize network matches over local matches
    final prioritizedMatches = prioritizeSourceMatches(matchesBySource);
    
    for (final match in prioritizedMatches) {
      if (!seenMatchIds.contains(match.id)) {
        seenMatchIds.add(match.id);
        deduplicatedMatches.add(match);
      }
    }

    _logger.info('Deduplicated ${prioritizedMatches.length} matches to ${deduplicatedMatches.length} unique matches');
    return deduplicatedMatches;
  }

  @override
  Future<Result<void>> subscribeToMatchUpdates() async {
    try {
      // Check if we already have a subscription
      if (_openMatchesSubscription != null) {
        _logger.info('Already subscribed to match updates');
        return Result.success(null);
      }

      // Find a repository that supports real-time updates
      final repositories = _getRepositories();
      final networkRepository = repositories.values
          .where((repo) => repo.supportsRealTimeUpdates)
          .firstOrNull;

      if (networkRepository == null) {
        return Result.failure('No network repository available for real-time updates');
      }

      try {
        // For now, just log that we would subscribe to WebSocket updates
        // The actual WebSocket subscription will be handled by the BLoC directly
        _logger.info('WebSocket subscription would be set up here');
        return Result.success(null);

      } catch (e) {
        _logger.error('Failed to subscribe to match updates: $e');
        return Result.failure('Failed to subscribe to match updates: $e');
      }

    } catch (e) {
      _logger.error('Error subscribing to match updates: $e');
      return Result.failure('Error subscribing to match updates: $e');
    }
  }

  @override
  Future<Result<void>> unsubscribeFromMatchUpdates() async {
    try {
      if (_openMatchesSubscription != null) {
        await _openMatchesSubscription!.cancel();
        _openMatchesSubscription = null;
        _logger.info('Unsubscribed from match updates');
      }

      return Result.success(null);

    } catch (e) {
      _logger.error('Error unsubscribing from match updates: $e');
      return Result.failure('Error unsubscribing from match updates: $e');
    }
  }

  @override
  Result<MatchUpdateResult> handleWebSocketMatchUpdate(List<GameMatch> matches) {
    try {
      _logger.info('Handling WebSocket match update with ${matches.length} matches');

      final result = MatchUpdateResult(
        updatedMatches: matches,
        sourceName: 'network',
        requiresStateUpdate: true,
      );

      return Result.success(result);

    } catch (e) {
      _logger.error('Error handling WebSocket match update: $e');
      return Result.failure('Error handling WebSocket match update: $e');
    }
  }

  @override
  Result<MatchDataResult> clearNetworkMatches(MatchDataResult currentData) {
    try {
      // Remove network matches from the data
      final updatedMatchesBySource = Map<String, List<GameMatch>>.from(currentData.matchesBySource);
      updatedMatchesBySource.remove('network');

      // Recalculate all matches without network matches
      final allMatches = deduplicateMatches(updatedMatchesBySource);

      final result = MatchDataResult(
        allMatches: allMatches,
        matchesBySource: updatedMatchesBySource,
        availableSources: currentData.availableSources.where((source) => source != 'network').toList(),
        hasNetworkCapability: false,
      );

      _logger.info('Cleared network matches, remaining: ${allMatches.length} matches');
      return Result.success(result);

    } catch (e) {
      _logger.error('Error clearing network matches: $e');
      return Result.failure('Error clearing network matches: $e');
    }
  }

  @override
  List<GameMatch> prioritizeSourceMatches(Map<String, List<GameMatch>> matchesBySource) {
    final prioritizedMatches = <GameMatch>[];

    // First add network matches (higher priority)
    if (matchesBySource.containsKey('network')) {
      prioritizedMatches.addAll(matchesBySource['network']!);
    }

    // Then add local matches
    if (matchesBySource.containsKey('local')) {
      prioritizedMatches.addAll(matchesBySource['local']!);
    }

    // Add any other sources
    for (final entry in matchesBySource.entries) {
      if (entry.key != 'network' && entry.key != 'local') {
        prioritizedMatches.addAll(entry.value);
      }
    }

    return prioritizedMatches;
  }

  @override
  Future<Result<SubscriptionResult>> subscribeToRealTimeUpdates(SubscribeToRealTimeUpdatesRequest request) async {
    try {
      _logger.info('Subscribing to real-time match updates');

      // Get the server notifications use case
      final serverNotificationsUseCase = GetIt.I<ServerNotificationsUseCase>();

      // Check if WebSocket is connected before subscribing
      final websocketRepo = GetIt.I<WebSocketRepository>();
      if (!websocketRepo.isConnected) {
        _logger.info('WebSocket not connected, attempting to connect before subscribing');
        await websocketRepo.connect();
      }

      // Always attempt to subscribe to ensure we're properly subscribed
      // Even if we have a subscription, the WebSocket connection might have been reset
      if (_openMatchesSubscription != null) {
        _logger.info('Existing subscription found, canceling and re-subscribing to ensure connection');
        await _openMatchesSubscription?.cancel();
        _openMatchesSubscription = null;
      }

      // Subscribe to open matches updates
      await serverNotificationsUseCase.subscribeToOpenMatches();
      _logger.info('Subscribed to WebSocket open matches updates');

      // Listen for open matches updates from the WebSocket
      _openMatchesSubscription = serverNotificationsUseCase.openMatchesUpdates.listen(
        (matches) {
          _logger.info('🔄 WEBSOCKET UPDATE: Received ${matches.length} matches in use case');
          // Log match IDs and player info for debugging
          for (final match in matches) {
            final playerCount = match.playerSlots.where((slot) => slot.playerId != null && slot.playerId!.isNotEmpty).length;
            _logger.info('  📋 Match ${match.id}: $playerCount/${match.playerSlots.length} players');
          }
          // The use case maintains the subscription, but doesn't process the updates
          // The BLoC will listen to the same stream separately
        },
        onError: (error) {
          _logger.error('Error in WebSocket open matches stream: $error');
        },
        onDone: () {
          _logger.info('WebSocket open matches stream closed');
          _openMatchesSubscription = null;
        },
      );

      return Result.success(SubscriptionResult(subscribed: true, message: 'Successfully subscribed to real-time updates'));
    } catch (e) {
      _logger.error('Failed to subscribe to real-time updates: $e');
      return Result.failure('Failed to subscribe to real-time updates: $e');
    }
  }

  @override
  Future<Result<UnsubscriptionResult>> unsubscribeFromRealTimeUpdates(UnsubscribeFromRealTimeUpdatesRequest request) async {
    try {
      _logger.info('Unsubscribing from real-time match updates');

      // Cancel any existing subscriptions
      _openMatchesSubscription?.cancel();
      _openMatchesSubscription = null;

      return Result.success(UnsubscriptionResult(unsubscribed: true));
    } catch (e) {
      _logger.error('Failed to unsubscribe from real-time updates: $e');
      return Result.failure('Failed to unsubscribe from real-time updates: $e');
    }
  }

  /// Clean up subscriptions when the use case is disposed
  void dispose() {
    _openMatchesSubscription?.cancel();
    _openMatchesSubscription = null;
  }
}
