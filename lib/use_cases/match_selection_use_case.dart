import 'package:common/models/game_match.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';

// Type aliases to match the codebase conventions using modern Dart syntax
typedef GameMatchId = String;
typedef PlayerId = String;

/// UseCase that handles match selection operations
abstract class MatchSelectionUseCase {
  /// source name
  final String name = 'unknown';

  /// current implementation only supports one server connection at a time; could be refactored to support more -- requires managing multiple websocket connections and dio/api
  final bool isServerSource = false;
  Future<List<GameMatch>> fetchOpenMatches(String gameName);

  Future<GameMatch?> openNewMatch(GameMatch newMatch, GameConfig gameConfig);

  Future<bool> joinMatch(String matchId, {PlayerId? playerId});

  Future<bool> leaveMatch(String matchId, {PlayerId? playerId});

  Future<bool> deleteMatch(String matchId);
}

/// Repository-based implementation of MatchSelectionUseCase
/// This is the new Phase 1 implementation that uses repository interfaces
class RepositoryBasedMatchSelectionUseCase extends MatchSelectionUseCase {
  final MatchRepositoryInterface _matchRepository;
  final RemoteLogger? _logger;

  RepositoryBasedMatchSelectionUseCase(this._matchRepository, this._logger);

  @override
  String get name => _matchRepository.sourceName;

  @override
  bool get isServerSource => _matchRepository.supportsRealTimeUpdates;

  @override
  Future<List<GameMatch>> fetchOpenMatches(String gameName) async {
    try {
      _logger?.info('Fetching open matches for $gameName using ${_matchRepository.sourceName}');
      final matches = await _matchRepository.fetchOpenMatches(gameName);
      _logger?.info('Fetched ${matches.length} matches from ${_matchRepository.sourceName}');
      return matches;
    } catch (e) {
      _logger?.error('Error fetching open matches from ${_matchRepository.sourceName}: $e');
      return [];
    }
  }

  @override
  Future<GameMatch?> openNewMatch(GameMatch newMatch, GameConfig gameConfig) async {
    try {
      _logger?.info('Creating new match using ${_matchRepository.sourceName}');
      final createdMatch = await _matchRepository.createMatch(newMatch, gameConfig);
      if (createdMatch != null) {
        _logger?.info('Successfully created match: ${createdMatch.id}');
      } else {
        _logger?.error('Failed to create match using ${_matchRepository.sourceName}');
      }
      return createdMatch;
    } catch (e) {
      _logger?.error('Error creating match using ${_matchRepository.sourceName}: $e');
      return null;
    }
  }

  @override
  Future<bool> joinMatch(String matchId, {PlayerId? playerId}) async {
    try {
      _logger?.info('Joining match $matchId using ${_matchRepository.sourceName}');
      final success = await _matchRepository.joinMatch(matchId, playerId: playerId);
      if (success) {
        _logger?.info('Successfully joined match: $matchId');
      } else {
        _logger?.error('Failed to join match: $matchId');
      }
      return success;
    } catch (e) {
      _logger?.error('Error joining match using ${_matchRepository.sourceName}: $e');
      return false;
    }
  }

  @override
  Future<bool> leaveMatch(String matchId, {PlayerId? playerId}) async {
    try {
      _logger?.info('Leaving match $matchId using ${_matchRepository.sourceName}');
      final success = await _matchRepository.leaveMatch(matchId, playerId: playerId);
      if (success) {
        _logger?.info('Successfully left match: $matchId');
      } else {
        _logger?.error('Failed to leave match: $matchId');
      }
      return success;
    } catch (e) {
      _logger?.error('Error leaving match using ${_matchRepository.sourceName}: $e');
      return false;
    }
  }

  @override
  Future<bool> deleteMatch(String matchId) async {
    try {
      _logger?.info('Deleting match $matchId using ${_matchRepository.sourceName}');
      final success = await _matchRepository.deleteMatch(matchId);
      if (success) {
        _logger?.info('Successfully deleted match: $matchId');
      } else {
        _logger?.error('Failed to delete match: $matchId');
      }
      return success;
    } catch (e) {
      _logger?.error('Error deleting match using ${_matchRepository.sourceName}: $e');
      return false;
    }
  }
}



