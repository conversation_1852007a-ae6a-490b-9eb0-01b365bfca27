import 'package:common/models/game_match.dart';
import 'package:common/models/player_slot.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/use_cases/interfaces/match_joining_use_case_interface.dart';
import 'package:dauntless/use_cases/interfaces/player_slot_operations_use_case_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

/// Concrete implementation of complex player slot operations
/// Extracted from MatchManagementBloc to improve testability and separation of concerns
@singleton
class PlayerSlotOperationsUseCase implements PlayerSlotOperationsUseCaseInterface {
  final MatchJoiningUseCaseInterface _matchJoiningUseCase;
  final RemoteLogger _logger;

  PlayerSlotOperationsUseCase(
    this._matchJoiningUseCase,
    this._logger,
  );

  @override
  Future<Result<PlayerSlotUpdateResult>> updateSelectedMatchPlayerType(
    UpdateSelectedMatchPlayerTypeRequest request,
  ) async {
    try {
      _logger.info('Updating player type for selected match slot ${request.slotIndex}: ${request.playerType}');

      // Validate slot index
      final validationResult = validateSlotIndex(request.slotIndex, request.selectedMatch.playerSlots);
      if (validationResult.isFailure) {
        return Result.failure(validationResult.error);
      }

      // Create updated player slots for the selected match
      final updatedSlots = [...request.selectedMatch.playerSlots];
      final oldSlot = updatedSlots[request.slotIndex];
      updatedSlots[request.slotIndex] = oldSlot.copyWith(
        type: request.playerType,
      );

      // Create updated match with new player slots
      final updatedMatch = request.selectedMatch.copyWith(
        playerSlots: updatedSlots,
      );

      // Check if server update is required
      final serverUpdateRequired = requiresServerUpdate(request.selectedMatch, request.matchesBySource);
      if (serverUpdateRequired.isFailure) {
        return Result.failure(serverUpdateRequired.error);
      }

      bool serverUpdateSuccessful = true;
      String? errorMessage;

      if (serverUpdateRequired.value) {
        _logger.info('Server update required for match ${request.selectedMatch.id}');
        
        // Validate server connectivity
        final connectivityResult = validateServerConnectivity();
        if (connectivityResult.isFailure) {
          return Result.failure('Server not available: ${connectivityResult.error}');
        }

        // Get current user info
        final userInfoResult = getCurrentUserInfo();
        if (userInfoResult.isFailure) {
          return Result.failure('User not available: ${userInfoResult.error}');
        }

        // Coordinate server update
        final serverUpdateRequest = ServerUpdateRequest(
          matchId: request.selectedMatch.id,
          updatedSlots: updatedSlots,
          userId: userInfoResult.value.id,
        );

        final serverResult = await coordinateServerUpdate(serverUpdateRequest);
        if (serverResult.isFailure) {
          serverUpdateSuccessful = false;
          errorMessage = 'Failed to update server match: ${serverResult.error}';
          _logger.error('Failed to update player type on server: ${serverResult.error}');
        } else {
          _logger.info('Successfully updated player type on server for match ${request.selectedMatch.id}');
        }
      } else {
        _logger.info('Skipping server update: local match or server unavailable');
      }

      return Result.success(PlayerSlotUpdateResult(
        updatedMatch: updatedMatch,
        serverUpdateSuccessful: serverUpdateSuccessful,
        requiresServerUpdate: serverUpdateRequired.value,
        errorMessage: errorMessage,
      ));
    } catch (e) {
      _logger.error('Error updating selected match player type: $e');
      return Result.failure('Error updating player type: $e');
    }
  }

  @override
  Result<List<PlayerSlot>> updatePlayerName(UpdatePlayerNameRequest request) {
    try {
      // Validate slot index
      final validationResult = validateSlotIndex(request.slotIndex, request.currentSlots);
      if (validationResult.isFailure) {
        return Result.failure(validationResult.error);
      }

      final updatedSlots = [...request.currentSlots];
      updatedSlots[request.slotIndex] = updatedSlots[request.slotIndex].copyWith(
        name: request.name,
      );

      _logger.info('Updated player name for slot ${request.slotIndex}: ${request.name}');
      return Result.success(updatedSlots);
    } catch (e) {
      _logger.error('Error updating player name: $e');
      return Result.failure('Error updating player name: $e');
    }
  }

  @override
  Result<List<PlayerSlot>> joinPlayerSlot(JoinPlayerSlotRequest request) {
    try {
      // Validate slot index
      final validationResult = validateSlotIndex(request.slotIndex, request.currentSlots);
      if (validationResult.isFailure) {
        return Result.failure(validationResult.error);
      }

      final updatedSlots = [...request.currentSlots];
      updatedSlots[request.slotIndex] = updatedSlots[request.slotIndex].copyWith(
        playerId: request.userId,
        name: request.userName,
      );

      _logger.info('User ${request.userId} joined slot ${request.slotIndex}');
      return Result.success(updatedSlots);
    } catch (e) {
      _logger.error('Error joining player slot: $e');
      return Result.failure('Error joining player slot: $e');
    }
  }

  @override
  Result<bool> validateSlotIndex(int slotIndex, List<PlayerSlot> slots) {
    if (slotIndex < 0 || slotIndex >= slots.length) {
      _logger.warn('Invalid slot index: $slotIndex (valid range: 0-${slots.length - 1})');
      return Result.failure('Invalid slot index: $slotIndex');
    }
    return Result.success(true);
  }

  @override
  Result<bool> requiresServerUpdate(
    GameMatch match,
    Map<String, List<GameMatch>> matchesBySource,
  ) {
    try {
      final isNetworkMatch = _matchJoiningUseCase.isNetworkMatch(match, matchesBySource);
      _logger.info('Server update check: isNetworkMatch=$isNetworkMatch');
      return Result.success(isNetworkMatch);
    } catch (e) {
      _logger.error('Error checking if server update required: $e');
      return Result.failure('Error checking server update requirement: $e');
    }
  }

  @override
  Future<Result<bool>> coordinateServerUpdate(ServerUpdateRequest request) async {
    try {
      _logger.info('Attempting server update for match ${request.matchId}');
      
      // Get server repository
      final serverRepository = GetIt.I<ServerRepository>();
      
      // Update the server with the new player slots
      final success = await serverRepository.updateMatchPlayerSlots(
        request.matchId,
        request.updatedSlots,
        request.userId,
      );

      if (success) {
        _logger.info('Successfully updated player slots on server for match ${request.matchId}');
        return Result.success(true);
      } else {
        return Result.failure('Server rejected player slot update');
      }
    } catch (e) {
      _logger.error('Error coordinating server update: $e');
      return Result.failure('Error coordinating server update: $e');
    }
  }

  @override
  Result<bool> validateServerConnectivity() {
    try {
      // Check if we can access the server repository (actual server connectivity)
      GetIt.I<ServerRepository>(); // Check if available
      _logger.info('ServerRepository is available for updates');
      return Result.success(true);
    } catch (e) {
      _logger.warn('ServerRepository not available for updates: $e');
      return Result.failure('ServerRepository not available: $e');
    }
  }

  @override
  Result<UserInfo> getCurrentUserInfo() {
    try {
      final user = GetIt.I<UserManager>().state.user;
      if (user == null) {
        return Result.failure('No user logged in');
      }

      return Result.success(UserInfo(
        id: user.id,
        name: user.id, // User model doesn't have name property, using id for now
      ));
    } catch (e) {
      _logger.error('Error getting current user info: $e');
      return Result.failure('Error getting user info: $e');
    }
  }
}
