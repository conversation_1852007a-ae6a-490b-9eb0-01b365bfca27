import 'package:get_it/get_it.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/use_cases/interfaces/match_source_management_use_case_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';

/// Concrete implementation of match source management operations
/// Handles repository lifecycle, source availability, and network capability monitoring
class MatchSourceManagementUseCase implements IMatchSourceManagementUseCase {
  final Map<String, MatchRepositoryInterface> _repositories;
  final RemoteLogger _logger;

  MatchSourceManagementUseCase(
    this._repositories,
    this._logger,
  );

  @override
  Future<Result<RepositoryInitializationResult>> initializeRepositories() async {
    try {
      _logger.info('Initializing match repositories');

      // Clear existing repositories to prevent duplicates
      _repositories.clear();

      // Get local repository (should always be available)
      try {
        final localRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'local');
        _repositories['local'] = localRepo;
        _logger.info('Initialized local match repository');
      } catch (e) {
        _logger.error('Failed to get local repository: $e');
        // Don't throw - continue without local repository
      }

      // Try to get network repository (only available when server connected)
      _logger.info('Checking for network repository, current scope: ${GetIt.I.currentScopeName}');
      if (GetIt.I.currentScopeName == 'serverConnected') {
        try {
          final networkRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'network');
          _repositories['network'] = networkRepo;
          _logger.info('Initialized network match repository');
        } catch (e) {
          _logger.warn('Network match repository not available: $e');
        }
      } else {
        _logger.info('Network repository not available - not in server-connected scope');
      }

      final availableSources = _repositories.keys.toList();
      final sourceCapabilities = getSourceCapabilities();
      final networkCapability = hasNetworkCapability();

      final result = RepositoryInitializationResult(
        availableSources: availableSources,
        sourceCapabilities: sourceCapabilities,
        hasNetworkCapability: networkCapability,
      );

      _logger.info('Repository initialization completed. Available sources: $availableSources');
      return Result.success(result);

    } catch (e) {
      _logger.error('Failed to initialize repositories: $e');
      return Result.failure('Failed to initialize repositories: $e');
    }
  }

  @override
  void cleanupRepositories() {
    try {
      _logger.info('Cleaning up match repositories');
      
      // Clear all repositories
      _repositories.clear();
      
      _logger.info('Repository cleanup completed');
    } catch (e) {
      _logger.error('Error during repository cleanup: $e');
    }
  }

  @override
  bool hasNetworkCapability() {
    return _repositories.values.any((repo) => repo.supportsRealTimeUpdates);
  }

  @override
  List<String> getAvailableSources() {
    return _repositories.keys.toList();
  }

  @override
  Future<Result<void>> handleServerScopeAvailable() async {
    try {
      _logger.info('Server scope became available, adding network repository');

      // Try to add network repository
      try {
        final networkRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'network');
        _repositories['network'] = networkRepo;
        _logger.info('Added network match repository');
      } catch (e) {
        _logger.error('Failed to add network repository: $e');
        return Result.failure('Failed to add network repository: $e');
      }

      return Result.success(null);

    } catch (e) {
      _logger.error('Error handling server scope available: $e');
      return Result.failure('Error handling server scope available: $e');
    }
  }

  @override
  Future<Result<void>> handleServerScopeLost() async {
    try {
      _logger.info('Server scope lost, removing network repository');

      // Remove network repository
      final removed = _repositories.remove('network');
      if (removed != null) {
        _logger.info('Removed network match repository');
      }

      return Result.success(null);

    } catch (e) {
      _logger.error('Error handling server scope lost: $e');
      return Result.failure('Error handling server scope lost: $e');
    }
  }

  @override
  Future<bool> checkAndAddNetworkSources() async {
    try {
      // Check if we're in server-connected scope
      if (GetIt.I.currentScopeName == 'serverConnected') {
        try {
          final networkRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'network');
          _repositories['network'] = networkRepo;
          _logger.info('Added network repository during capability check');
          return true;
        } catch (e) {
          _logger.warn('Network repository not available during capability check: $e');
        }
      }

      return hasNetworkCapability();

    } catch (e) {
      _logger.error('Error checking and adding network sources: $e');
      return false;
    }
  }

  @override
  Map<String, bool> getSourceCapabilities() {
    final capabilities = <String, bool>{};
    
    for (final entry in _repositories.entries) {
      capabilities[entry.key] = entry.value.supportsRealTimeUpdates;
    }
    
    return capabilities;
  }

  @override
  bool isSourceAvailable(String sourceName) {
    return _repositories.containsKey(sourceName);
  }

  @override
  Future<Result<List<String>>> refreshAvailableSources() async {
    try {
      // Re-initialize repositories to get current state
      final initResult = await initializeRepositories();
      if (initResult.isFailure) {
        return Result.failure(initResult.error);
      }

      final availableSources = getAvailableSources();
      _logger.info('Refreshed available sources: $availableSources');
      
      return Result.success(availableSources);

    } catch (e) {
      _logger.error('Error refreshing available sources: $e');
      return Result.failure('Error refreshing available sources: $e');
    }
  }

  @override
  Future<Result<SourceRemovalResult>> removeMatchSource(RemoveMatchSourceRequest request) async {
    try {
      final sourceName = request.sourceName;
      _logger.info('Removing match source: $sourceName');

      // Remove from internal repositories map
      final removedRepository = _repositories.remove(sourceName);

      if (removedRepository != null) {
        _logger.info('Successfully removed match source: $sourceName');
        return Result.success(SourceRemovalResult(
          sourceRemoved: true,
          removedSourceName: sourceName,
          message: 'Source $sourceName removed successfully',
        ));
      } else {
        _logger.warn('Match source $sourceName was not found');
        // Still return success since the goal (source not present) is achieved
        return Result.success(SourceRemovalResult(
          sourceRemoved: true,
          removedSourceName: sourceName,
          message: 'Source $sourceName was not found (already removed)',
        ));
      }

    } catch (e) {
      _logger.error('Error removing match source ${request.sourceName}: $e');
      return Result.failure('Error removing match source: $e');
    }
  }
}
