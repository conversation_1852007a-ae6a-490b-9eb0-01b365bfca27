import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/results/match_management_results.dart' as results;
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/use_cases/interfaces/repository_management_use_case_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

/// Concrete implementation of repository initialization and management
/// Extracted from MatchManagementBloc to improve testability and separation of concerns
@singleton
class RepositoryManagementUseCase implements RepositoryManagementUseCaseInterface {
  final RemoteLogger _logger;
  final Map<String, MatchRepositoryInterface> _repositories = {};

  RepositoryManagementUseCase(this._logger);

  @override
  Future<Result<results.RepositoryInitializationResult>> initializeRepositories() async {
    try {
      _logger.info('Initializing repositories');
      
      // Clear existing repositories to prevent duplicates
      _repositories.clear();

      // Get local repository (should always be available)
      try {
        final localRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'local');
        _repositories['local'] = localRepo;
        _logger.info('Initialized local match repository');
      } catch (e) {
        _logger.error('Failed to get local repository: $e');
        // Don't throw - continue without local repository
      }

      // Try to get network repository (only available when server connected)
      _logger.info('Checking for network repository, current scope: ${getCurrentScope()}');
      if (getCurrentScope() == 'serverConnected') {
        try {
          final networkRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'network');
          _repositories['network'] = networkRepo;
          _logger.info('Initialized network match repository');
        } catch (e) {
          _logger.warn('Network match repository not available: $e');
        }
      } else {
        _logger.info('Network repository not available - not in server-connected scope');
      }

      // Build result
      final availableSources = getAvailableSources();
      final capabilities = getRepositoryCapabilities();
      final hasNetwork = hasNetworkCapability();

      _logger.info('Repository initialization completed: ${availableSources.length} sources, network: $hasNetwork');

      return Result.success(results.RepositoryInitializationResult(
        availableSources: availableSources,
        sourceCapabilities: capabilities.map((key, value) => MapEntry(key, value.supportsRealTimeUpdates)),
        hasNetworkCapability: hasNetwork,
      ));
    } catch (e) {
      _logger.error('Failed to initialize repositories: $e');
      return Result.failure('Failed to initialize repositories: $e');
    }
  }

  @override
  MatchRepositoryInterface? getRepository(String sourceName) {
    return _repositories[sourceName];
  }

  @override
  List<String> getAvailableSources() {
    return _repositories.keys.toList();
  }

  @override
  bool isLocalSource(String sourceName) {
    // Local sources typically have names like "local", "file", "cache"
    final localSourceNames = ['local', 'file', 'cache', 'offline'];
    return localSourceNames.any((name) => sourceName.toLowerCase().contains(name));
  }

  @override
  bool hasNetworkCapability() {
    return _repositories.values.any((repo) => repo.supportsRealTimeUpdates);
  }

  @override
  Map<String, RepositoryCapabilities> getRepositoryCapabilities() {
    final capabilities = <String, RepositoryCapabilities>{};
    
    for (final entry in _repositories.entries) {
      final sourceName = entry.key;
      final repository = entry.value;
      
      capabilities[sourceName] = RepositoryCapabilities(
        supportsRealTimeUpdates: repository.supportsRealTimeUpdates,
        supportsMatchCreation: true, // Most repositories support creation
        supportsMatchJoining: true, // Most repositories support joining
        isNetworkSource: !isLocalSource(sourceName),
        description: _getRepositoryDescription(sourceName),
      );
    }
    
    return capabilities;
  }

  @override
  Result<bool> validateRepository(String sourceName) {
    try {
      final repository = _repositories[sourceName];
      if (repository == null) {
        return Result.failure('Repository not found: $sourceName');
      }
      
      // Additional validation could be added here
      // For now, just check if it exists
      return Result.success(true);
    } catch (e) {
      _logger.error('Error validating repository $sourceName: $e');
      return Result.failure('Error validating repository: $e');
    }
  }

  @override
  void clearRepositories() {
    _logger.info('Clearing all repositories');
    _repositories.clear();
  }

  @override
  String getCurrentScope() {
    return GetIt.I.currentScopeName ?? 'default';
  }

  /// Get a human-readable description for a repository source
  String? _getRepositoryDescription(String sourceName) {
    switch (sourceName.toLowerCase()) {
      case 'local':
        return 'Local file-based match storage';
      case 'network':
        return 'Network server-based match storage';
      case 'cache':
        return 'Cached match data';
      default:
        return 'Match repository: $sourceName';
    }
  }

  /// Get repository count for debugging
  int get repositoryCount => _repositories.length;

  /// Get repository names for debugging
  List<String> get repositoryNames => _repositories.keys.toList();

  /// Check if a specific repository type is available
  bool hasRepositoryType(String type) {
    return _repositories.keys.any((name) => name.toLowerCase().contains(type.toLowerCase()));
  }

  /// Get network repository if available
  MatchRepositoryInterface? get networkRepository {
    return _repositories.entries
        .where((entry) => !isLocalSource(entry.key))
        .map((entry) => entry.value)
        .firstOrNull;
  }

  /// Get local repository if available
  MatchRepositoryInterface? get localRepository {
    return _repositories.entries
        .where((entry) => isLocalSource(entry.key))
        .map((entry) => entry.value)
        .firstOrNull;
  }
}
