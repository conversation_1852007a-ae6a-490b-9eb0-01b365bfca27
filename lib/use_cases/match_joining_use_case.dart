import 'package:common/models/game_match.dart';


import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/use_cases/interfaces/match_joining_use_case_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:injectable/injectable.dart';

/// Concrete implementation of match joining and leaving operations
/// Extracted from MatchManagementBloc to improve testability and separation of concerns
@singleton
class MatchJoiningUseCase implements MatchJoiningUseCaseInterface {
  final Map<String, MatchRepositoryInterface> _repositories;

  final RemoteLogger _logger;

  MatchJoiningUseCase(
    this._repositories,
    this._logger,
  );

  @override
  Future<Result<MatchJoinResult>> joinMatch(JoinMatchRequest request) async {
    try {
      _logger.info('Attempting to join match: ${request.matchId}');

      // Find the match in our available repositories
      final findRequest = FindMatchRequest(matchId: request.matchId);
      final findResult = findMatchById(findRequest);

      if (findResult.isFailure) {
        _logger.warn('Match ${request.matchId} not found in any source');
        return Result.failure('Match not found');
      }

      final findData = findResult.value;
      if (!findData.found) {
        _logger.warn('Match ${request.matchId} not found in any source');
        return Result.failure('Match not found');
      }

      final sourceName = findData.sourceName!;
      final repository = _repositories[sourceName];
      if (repository == null) {
        _logger.error('Repository not found for source: $sourceName');
        return Result.failure('Repository not available for source: $sourceName');
      }

      // Perform the join operation
      final success = await repository.joinMatch(request.matchId, playerId: request.playerId);

      if (success) {
        _logger.info('✅ JOIN SUCCESS: Joined match ${request.matchId} from source $sourceName');
        return Result.success(MatchJoinResult(
          success: true,
          matchId: request.matchId,
          sourceName: sourceName,
          message: 'Successfully joined match',
        ));
      } else {
        _logger.error('❌ JOIN FAILED: Could not join match ${request.matchId}');
        return Result.failure('Failed to join match');
      }
    } catch (e) {
      _logger.error('Error joining match ${request.matchId}: $e');
      return Result.failure('Error joining match: $e');
    }
  }

  @override
  Future<Result<MatchLeaveResult>> leaveMatch(LeaveMatchRequest request) async {
    try {
      _logger.info('Attempting to leave match: ${request.matchId}');

      // Try all repositories since we might not know which one has the match
      for (final entry in _repositories.entries) {
        final sourceName = entry.key;
        final repository = entry.value;

        try {
          final success = await repository.leaveMatch(request.matchId, playerId: request.playerId);
          if (success) {
            _logger.info('✅ LEAVE SUCCESS: Left match ${request.matchId} from source $sourceName');
            return Result.success(MatchLeaveResult(
              success: true,
              matchId: request.matchId,
              message: 'Successfully left match',
            ));
          }
        } catch (e) {
          _logger.warn('Failed to leave match ${request.matchId} from repository $sourceName: $e');
          continue;
        }
      }

      _logger.error('❌ LEAVE FAILED: Could not leave match ${request.matchId} from any repository');
      return Result.failure('Failed to leave match from any source');
    } catch (e) {
      _logger.error('Error leaving match ${request.matchId}: $e');
      return Result.failure('Error leaving match: $e');
    }
  }

  @override
  Future<Result<MatchJoinResult>> joinMatchSlot(JoinMatchSlotRequest request) async {
    try {
      _logger.info('Attempting to join match slot: match=${request.matchId}, slot=${request.slotIndex}');

      // Validate slot index
      if (request.slotIndex < 0 || request.slotIndex >= request.currentSlots.length) {
        return Result.failure('Invalid slot index: ${request.slotIndex}');
      }

      // Perform slot switching logic
      final switchResult = switchPlayerSlot(SwitchSlotRequest(
        matchId: request.matchId,
        playerId: request.playerId,
        fromSlotIndex: -1, // -1 indicates we're finding and removing from any existing slot
        toSlotIndex: request.slotIndex,
        currentSlots: request.currentSlots,
      ));

      if (switchResult.isFailure) {
        return Result.failure(switchResult.error);
      }

      // If this is a network match, update the server
      if (request.isNetworkMatch) {
        final joinRequest = JoinMatchRequest(
          matchId: request.matchId,
          playerId: request.playerId,
        );
        
        final joinResult = await joinMatch(joinRequest);
        if (joinResult.isFailure) {
          return Result.failure('Failed to update server: ${joinResult.error}');
        }

        _logger.info('✅ SLOT JOIN SUCCESS: Joined match ${request.matchId} slot ${request.slotIndex} with server update');
        return Result.success(MatchJoinResult(
          success: true,
          matchId: request.matchId,
          sourceName: joinResult.value.sourceName,
          message: 'Successfully joined match slot with server update',
        ));
      } else {
        // For local matches, just return success
        _logger.info('✅ SLOT JOIN SUCCESS: Joined local match ${request.matchId} slot ${request.slotIndex}');
        return Result.success(MatchJoinResult(
          success: true,
          matchId: request.matchId,
          sourceName: 'local',
          message: 'Successfully joined local match slot',
        ));
      }
    } catch (e) {
      _logger.error('Error joining match slot: $e');
      return Result.failure('Error joining match slot: $e');
    }
  }

  @override
  Result<FindMatchResult> findMatchById(FindMatchRequest request) {
    try {
      final matchesBySource = request.matchesBySource ?? <String, List<GameMatch>>{};

      for (final entry in matchesBySource.entries) {
        final sourceName = entry.key;
        final matches = entry.value;

        try {
          final match = matches.firstWhere((m) => m.id == request.matchId);
          _logger.info('Found match ${request.matchId} in source: $sourceName');
          return Result.success(FindMatchResult(
            match: match,
            sourceName: sourceName,
            found: true,
          ));
        } catch (e) {
          // Match not found in this source, continue to next
          continue;
        }
      }

      _logger.warn('Match ${request.matchId} not found in any source');
      return Result.success(FindMatchResult(found: false));
    } catch (e) {
      _logger.error('Error finding match ${request.matchId}: $e');
      return Result.failure('Error finding match: $e');
    }
  }

  @override
  String? determineMatchSource(String matchId, Map<String, List<GameMatch>> matchesBySource) {
    for (final entry in matchesBySource.entries) {
      final sourceName = entry.key;
      final matches = entry.value;

      if (matches.any((m) => m.id == matchId)) {
        return sourceName;
      }
    }
    return null;
  }

  @override
  Result<SlotSwitchResult> switchPlayerSlot(SwitchSlotRequest request) {
    try {
      final updatedSlots = [...request.currentSlots];

      // First, remove the user from any existing slots (slot switching)
      if (request.fromSlotIndex == -1) {
        // Find and remove from any slot containing this player
        for (int i = 0; i < updatedSlots.length; i++) {
          if (updatedSlots[i].playerId == request.playerId) {
            _logger.info('Removing player ${request.playerId} from slot $i before joining slot ${request.toSlotIndex}');
            updatedSlots[i] = updatedSlots[i].copyWith(playerId: null);
          }
        }
      } else {
        // Remove from specific slot
        if (request.fromSlotIndex >= 0 && request.fromSlotIndex < updatedSlots.length) {
          updatedSlots[request.fromSlotIndex] = updatedSlots[request.fromSlotIndex].copyWith(playerId: null);
        }
      }

      // Then, assign the user to the target slot
      if (request.toSlotIndex >= 0 && request.toSlotIndex < updatedSlots.length) {
        updatedSlots[request.toSlotIndex] = updatedSlots[request.toSlotIndex].copyWith(
          playerId: request.playerId,
        );

        _logger.info('Player ${request.playerId} switched to slot ${request.toSlotIndex}');
        return Result.success(SlotSwitchResult(
          updatedSlots: updatedSlots,
          success: true,
          message: 'Successfully switched player slot',
        ));
      } else {
        return Result.failure('Invalid target slot index: ${request.toSlotIndex}');
      }
    } catch (e) {
      _logger.error('Error switching player slot: $e');
      return Result.failure('Error switching player slot: $e');
    }
  }

  @override
  bool isNetworkMatch(GameMatch match, Map<String, List<GameMatch>> matchesBySource) {
    // Check if the match exists in any network source
    for (final entry in matchesBySource.entries) {
      final sourceName = entry.key;
      final matches = entry.value;

      // Network sources typically have names like "Network", "Server", etc.
      if (sourceName.toLowerCase().contains('network') ||
          sourceName.toLowerCase().contains('server')) {
        if (matches.any((m) => m.id == match.id)) {
          _logger.info('Match ${match.id} identified as network match from source: $sourceName');
          return true;
        }
      }
    }
    return false;
  }
}
