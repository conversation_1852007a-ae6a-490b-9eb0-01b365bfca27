import 'package:dauntless/models/base/game_config.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/use_cases/interfaces/config_management_use_case_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:injectable/injectable.dart';

/// Concrete implementation of configuration management operations
/// Extracted from MatchManagementBloc to improve testability and separation of concerns
class ConfigManagementUseCase implements ConfigManagementUseCaseInterface {
  final RemoteLogger _logger;

  ConfigManagementUseCase(this._logger);

  @override
  Result<DefaultConfigResult> getOrCreateDefaultConfig(List<GameConfig> availableConfigs) {
    try {
      _logger.info('Getting or creating default config from ${availableConfigs.length} available configs');

      GameConfig defaultConfig;
      bool isFallbackConfig = false;

      if (availableConfigs.isNotEmpty) {
        // Use the first available config as default
        defaultConfig = availableConfigs.first;
        _logger.info('Using first available config as default: ${defaultConfig.id}');
      } else {
        // Create fallback config
        final fallbackResult = createFallbackConfig();
        if (fallbackResult.isFailure) {
          return Result.failure(fallbackResult.error);
        }
        defaultConfig = fallbackResult.value;
        isFallbackConfig = true;
        _logger.info('Created fallback config: ${defaultConfig.id}');
      }

      // Create default slots for the config
      final slotsResult = createDefaultSlotsForConfig(defaultConfig);
      if (slotsResult.isFailure) {
        return Result.failure(slotsResult.error);
      }

      return Result.success(DefaultConfigResult(
        config: defaultConfig,
        defaultSlots: slotsResult.value,
        isFallbackConfig: isFallbackConfig,
        message: isFallbackConfig 
            ? 'Using fallback configuration'
            : 'Using default configuration: ${defaultConfig.name}',
      ));
    } catch (e) {
      _logger.error('Error getting or creating default config: $e');
      return Result.failure('Error getting default config: $e');
    }
  }

  @override
  Result<bool> validateConfigSelection(String configId, List<GameConfig> availableConfigs) {
    try {
      if (configId.isEmpty) {
        return Result.failure('Config ID cannot be empty');
      }

      final configExists = availableConfigs.any((config) => config.id == configId);
      if (!configExists) {
        return Result.failure('Configuration not found: $configId');
      }

      _logger.info('Config selection validation passed: $configId');
      return Result.success(true);
    } catch (e) {
      _logger.error('Error validating config selection: $e');
      return Result.failure('Error validating config selection: $e');
    }
  }

  @override
  Result<GameConfig?> getConfigById(String configId, List<GameConfig> availableConfigs) {
    try {
      final config = availableConfigs
          .where((config) => config.id == configId)
          .firstOrNull;

      if (config != null) {
        _logger.info('Found config: ${config.id}');
      } else {
        _logger.warn('Config not found: $configId');
      }

      return Result.success(config);
    } catch (e) {
      _logger.error('Error getting config by ID: $e');
      return Result.failure('Error getting config: $e');
    }
  }

  @override
  Result<GameConfig> createFallbackConfig() {
    try {
      final fallbackConfig = GameConfig(
        id: 'default',
        name: 'Default Game',
      );

      _logger.info('Created fallback config: ${fallbackConfig.id}');
      return Result.success(fallbackConfig);
    } catch (e) {
      _logger.error('Error creating fallback config: $e');
      return Result.failure('Error creating fallback config: $e');
    }
  }

  @override
  Result<List<PlayerSlot>> createDefaultSlotsForConfig(GameConfig config) {
    try {
      _logger.info('Creating default slots for config: ${config.id}');

      final availableClasses = config.playerClasses;
      if (availableClasses.isEmpty) {
        _logger.warn('No player classes available for creating default slots');
        return Result.success([]);
      }

      // Create minimum required slots (usually 2 for most games)
      final minSlots = 2; // Default to 2 since GameConfig doesn't have minPlayers
      final slots = <PlayerSlot>[];

      for (int i = 0; i < minSlots; i++) {
        final playerClass = availableClasses[i % availableClasses.length];
        slots.add(PlayerSlot(
          id: 'slot_$i',
          type: PlayerType.humanLocal,
          playerClassId: playerClass.id,
          name: 'Player ${i + 1}',
        ));
      }

      _logger.info('Created ${slots.length} default player slots');
      return Result.success(slots);
    } catch (e) {
      _logger.error('Error creating default slots: $e');
      return Result.failure('Error creating default slots: $e');
    }
  }

  @override
  Result<bool> validateConfigForMatchCreation(GameConfig config) {
    try {
      // Basic validation
      if (config.id.isEmpty) {
        return Result.failure('Config ID cannot be empty');
      }

      if (config.playerClasses.isEmpty) {
        return Result.failure('Config must have at least one player class');
      }

      if (config.playerClasses.length > 16) { // Reasonable upper limit
        return Result.failure('Config has too many player classes (max 16)');
      }

      _logger.info('Config validation passed for match creation: ${config.id}');
      return Result.success(true);
    } catch (e) {
      _logger.error('Error validating config for match creation: $e');
      return Result.failure('Error validating config: $e');
    }
  }

  @override
  Result<GameConfig> getRecommendedConfig(List<GameConfig> availableConfigs) {
    try {
      if (availableConfigs.isEmpty) {
        return createFallbackConfig();
      }

      // For now, just return the first config
      // In the future, this could implement more sophisticated logic
      // like user preferences, most popular configs, etc.
      final recommended = availableConfigs.first;
      
      _logger.info('Recommended config: ${recommended.id}');
      return Result.success(recommended);
    } catch (e) {
      _logger.error('Error getting recommended config: $e');
      return Result.failure('Error getting recommended config: $e');
    }
  }

  @override
  Result<ConfigChangeResult> handleConfigurationChange(
    String newConfigId,
    List<GameConfig> availableConfigs,
    List<PlayerSlot> currentSlots,
  ) {
    try {
      _logger.info('Handling configuration change to: $newConfigId');

      // Validate the new config
      final validationResult = validateConfigSelection(newConfigId, availableConfigs);
      if (validationResult.isFailure) {
        return Result.failure(validationResult.error);
      }

      // Get the new config
      final configResult = getConfigById(newConfigId, availableConfigs);
      if (configResult.isFailure || configResult.value == null) {
        return Result.failure('Configuration not found: $newConfigId');
      }

      final newConfig = configResult.value!;

      // Validate the new config for match creation
      final creationValidationResult = validateConfigForMatchCreation(newConfig);
      if (creationValidationResult.isFailure) {
        return Result.failure(creationValidationResult.error);
      }

      // Create new default slots for the new config
      final slotsResult = createDefaultSlotsForConfig(newConfig);
      if (slotsResult.isFailure) {
        return Result.failure(slotsResult.error);
      }

      // Determine if slots need to be reset (always reset for simplicity)
      final requiresSlotReset = true;

      _logger.info('Configuration change completed: ${newConfig.id}, slots reset: $requiresSlotReset');

      return Result.success(ConfigChangeResult(
        newConfig: newConfig,
        updatedSlots: slotsResult.value,
        requiresSlotReset: requiresSlotReset,
        message: 'Configuration changed to: ${newConfig.name}',
      ));
    } catch (e) {
      _logger.error('Error handling configuration change: $e');
      return Result.failure('Error changing configuration: $e');
    }
  }
}
