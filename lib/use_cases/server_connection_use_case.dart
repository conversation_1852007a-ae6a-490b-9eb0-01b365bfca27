import 'package:common/models/game_match.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/frameworks/environment/server_environment/server_environment_manager.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/results/match_management_results.dart' as results;
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/use_cases/interfaces/server_connection_use_case_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:get_it/get_it.dart';
import 'package:injectable/injectable.dart';

/// Concrete implementation of server connection state management and lifecycle
/// Extracted from MatchManagementBloc to improve testability and separation of concerns
@singleton
class ServerConnectionUseCase implements ServerConnectionUseCaseInterface {
  final Map<String, MatchRepositoryInterface> _repositories;
  final RemoteLogger _logger;

  ServerConnectionUseCase(
    this._repositories,
    this._logger,
  );

  @override
  Future<Result<results.RepositoryInitializationResult>> handleServerScopeAvailable() async {
    try {
      _logger.info('Server scope became available, re-initializing repositories');
      _logger.info('Current GetIt scope: ${getCurrentScopeName()}');

      // Try to get network repository (only available when server connected)
      if (getCurrentScopeName() == 'serverConnected') {
        try {
          final networkRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'network');
          _repositories['network'] = networkRepo;
          _logger.info('Initialized network match repository');
        } catch (e) {
          _logger.warn('Network match repository not available: $e');
          return Result.failure('Network repository not available: $e');
        }
      } else {
        _logger.info('Network repository not available - not in server-connected scope');
        return Result.failure('Not in server-connected scope');
      }

      // Build result
      final availableSources = _repositories.keys.toList();
      final sourceCapabilities = <String, bool>{};
      bool hasNetworkCapability = false;

      for (final entry in _repositories.entries) {
        final supportsRealTime = entry.value.supportsRealTimeUpdates;
        sourceCapabilities[entry.key] = supportsRealTime;
        if (supportsRealTime) {
          hasNetworkCapability = true;
        }
      }

      _logger.info('Updated match sources after server scope activation: $availableSources');

      return Result.success(results.RepositoryInitializationResult(
        availableSources: availableSources,
        sourceCapabilities: sourceCapabilities,
        hasNetworkCapability: hasNetworkCapability,
      ));
    } catch (e) {
      _logger.error('Error handling server scope available: $e');
      return Result.failure('Error handling server scope available: $e');
    }
  }

  @override
  Future<Result<results.RepositoryInitializationResult>> handleServerScopeLost() async {
    try {
      _logger.info('Server scope lost, cleaning up network resources');
      _logger.info('Current GetIt scope: ${getCurrentScopeName()}');

      // Remove network repository
      final removedRepo = _repositories.remove('network');
      if (removedRepo != null) {
        _logger.info('Removed network match repository');
      }

      // Build result
      final availableSources = _repositories.keys.toList();
      final sourceCapabilities = <String, bool>{};
      bool hasNetworkCapability = false;

      for (final entry in _repositories.entries) {
        final supportsRealTime = entry.value.supportsRealTimeUpdates;
        sourceCapabilities[entry.key] = supportsRealTime;
        if (supportsRealTime) {
          hasNetworkCapability = true;
        }
      }

      _logger.info('Server scope cleanup completed, remaining sources: $availableSources');

      return Result.success(results.RepositoryInitializationResult(
        availableSources: availableSources,
        sourceCapabilities: sourceCapabilities,
        hasNetworkCapability: hasNetworkCapability,
      ));
    } catch (e) {
      _logger.error('Error handling server scope lost: $e');
      return Result.failure('Error handling server scope lost: $e');
    }
  }

  @override
  Result<bool> detectNetworkCapability(Map<String, dynamic> repositories) {
    try {
      if (repositories is Map<String, MatchRepositoryInterface>) {
        final hasNetworkCapability = repositories.values.any((repo) => repo.supportsRealTimeUpdates);
        return Result.success(hasNetworkCapability);
      } else {
        return Result.failure('Invalid repositories type');
      }
    } catch (e) {
      _logger.error('Error detecting network capability: $e');
      return Result.failure('Error detecting network capability: $e');
    }
  }

  @override
  Future<Result<bool>> disconnectFromServer() async {
    try {
      _logger.info('Disconnecting from server');

      // Notify ServerEnvironmentManager to disconnect by setting profile to null
      try {
        GetIt.I<ServerEnvironmentManager>().add(SetSelectedServerProfileEvent(null));
        _logger.info('Server disconnection completed');
        return Result.success(true);
      } catch (e) {
        _logger.warn('ServerEnvironmentManager not available for disconnect: $e');
        return Result.failure('ServerEnvironmentManager not available: $e');
      }
    } catch (e) {
      _logger.error('Error during server disconnection: $e');
      return Result.failure('Error during server disconnection: $e');
    }
  }

  @override
  Result<NetworkStateResult> evaluateNetworkState(
    bool hasNetworkCapability,
    bool isCreatingMatch,
    List<PlayerSlot> playerSlots,
    GameMatch? selectedMatch,
  ) {
    try {
      bool shouldClearMatchCreation = false;
      bool shouldClearSelectedMatch = false;
      String? reason;

      // Check if we're losing network capability
      if (!hasNetworkCapability) {
        // Check if currently creating a network match
        if (isCreatingMatch && hasNetworkPlayers(playerSlots)) {
          _logger.info('Clearing network match creation due to network capability loss');
          shouldClearMatchCreation = true;
          reason = 'Network match creation cleared due to network capability loss';
        }

        // Check if we have a selected network match for joining
        if (selectedMatch != null && matchHasNetworkPlayers(selectedMatch)) {
          _logger.info('Clearing selected network match due to network capability loss');
          shouldClearSelectedMatch = true;
          reason = reason != null 
              ? '$reason; Selected network match cleared'
              : 'Selected network match cleared due to network capability loss';
        }
      }

      return Result.success(NetworkStateResult(
        shouldClearMatchCreation: shouldClearMatchCreation,
        shouldClearSelectedMatch: shouldClearSelectedMatch,
        reason: reason,
      ));
    } catch (e) {
      _logger.error('Error evaluating network state: $e');
      return Result.failure('Error evaluating network state: $e');
    }
  }

  @override
  Future<Result<bool>> cleanupNetworkMatches(
    Map<String, List<GameMatch>> matchesBySource,
  ) async {
    try {
      _logger.info('Cleaning up network matches');

      int removedCount = 0;
      final updatedMatchesBySource = Map<String, List<GameMatch>>.from(matchesBySource);

      // Remove network sources
      final networkSources = updatedMatchesBySource.keys
          .where((sourceName) => 
              sourceName.toLowerCase().contains('network') ||
              sourceName.toLowerCase().contains('server'))
          .toList();

      for (final sourceName in networkSources) {
        final removed = updatedMatchesBySource.remove(sourceName);
        if (removed != null) {
          removedCount += removed.length;
          _logger.info('Removed $sourceName source with ${removed.length} matches');
        }
      }

      _logger.info('Network cleanup completed: removed $removedCount matches from ${networkSources.length} sources');
      return Result.success(true);
    } catch (e) {
      _logger.error('Error cleaning up network matches: $e');
      return Result.failure('Error cleaning up network matches: $e');
    }
  }

  @override
  bool hasNetworkPlayers(List<PlayerSlot> playerSlots) {
    return playerSlots.any((slot) =>
        slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);
  }

  @override
  bool matchHasNetworkPlayers(GameMatch match) {
    return hasNetworkPlayers(match.playerSlots);
  }

  @override
  String getCurrentScopeName() {
    return GetIt.I.currentScopeName ?? 'default';
  }
}
