import 'package:common/models/game_match.dart';

import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';

/// Interface for match joining and leaving operations
/// <PERSON>les complex joining logic including slot switching, repository selection,
/// and server coordination for joins/leaves
abstract class MatchJoiningUseCaseInterface {
  /// Join a match by ID using the appropriate repository
  /// Automatically finds the match across available sources and uses the correct repository
  Future<Result<MatchJoinResult>> joinMatch(JoinMatchRequest request);

  /// Leave a match by ID using the appropriate repository
  /// Tries all repositories since we might not know which one has the match
  Future<Result<MatchLeaveResult>> leaveMatch(LeaveMatchRequest request);

  /// Join a specific slot in a match with slot switching logic
  /// <PERSON>les removing the user from existing slots before joining the target slot
  Future<Result<MatchJoinResult>> joinMatchSlot(JoinMatchSlotRequest request);

  /// Find a match by ID across all available sources
  /// Returns the match and the source name where it was found
  Result<FindMatchResult> findMatchById(FindMatchRequest request);

  /// Determine which source a match belongs to based on current state
  /// Used for repository selection when joining/leaving matches
  String? determineMatchSource(String matchId, Map<String, List<GameMatch>> matchesBySource);

  /// Switch a player from one slot to another within the same match
  /// Handles slot conflict resolution and validation
  Result<SlotSwitchResult> switchPlayerSlot(SwitchSlotRequest request);

  /// Check if a match is from a network source (server match)
  /// Used to determine if server updates are needed
  bool isNetworkMatch(GameMatch match, Map<String, List<GameMatch>> matchesBySource);
}
