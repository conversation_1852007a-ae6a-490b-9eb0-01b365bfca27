import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/results/match_management_results.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';

/// Interface for repository initialization and management
/// Handles repository lifecycle, source validation, and network capability detection
abstract class RepositoryManagementUseCaseInterface {
  /// Initialize all available repositories based on current GetIt scope
  /// Returns repository information and network capabilities
  Future<Result<RepositoryInitializationResult>> initializeRepositories();

  /// Get a repository by source name
  /// Returns null if repository not found or not available
  MatchRepositoryInterface? getRepository(String sourceName);

  /// Get all available repository sources
  /// Returns list of source names that have active repositories
  List<String> getAvailableSources();

  /// Check if a source is a local source (vs network source)
  /// Used for error handling and fallback logic
  bool isLocalSource(String sourceName);

  /// Check if network repositories are available
  /// Returns true if any repository supports real-time updates
  bool hasNetworkCapability();

  /// Get repository capabilities for all sources
  /// Returns map of source name to capability information
  Map<String, RepositoryCapabilities> getRepositoryCapabilities();

  /// Validate that a repository exists and is accessible
  /// Used before performing operations on specific repositories
  Result<bool> validateRepository(String sourceName);

  /// Clear all repositories (for cleanup/reset)
  /// Used during scope changes or shutdown
  void clearRepositories();

  /// Get current GetIt scope information
  /// Used for debugging and scope-aware repository management
  String getCurrentScope();
}

/// Repository capability information
class RepositoryCapabilities {
  final bool supportsRealTimeUpdates;
  final bool supportsMatchCreation;
  final bool supportsMatchJoining;
  final bool isNetworkSource;
  final String? description;

  const RepositoryCapabilities({
    required this.supportsRealTimeUpdates,
    required this.supportsMatchCreation,
    required this.supportsMatchJoining,
    required this.isNetworkSource,
    this.description,
  });
}
