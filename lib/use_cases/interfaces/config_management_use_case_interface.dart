import 'package:dauntless/models/base/game_config.dart';
import 'package:common/models/player_slot.dart';
import 'package:dauntless/models/common/result.dart';

/// Interface for configuration management operations
/// Handles default config creation, config selection validation, and fallback handling
abstract class ConfigManagementUseCaseInterface {
  /// Get or create a default configuration for match creation
  /// Provides fallback when no configs are available
  Result<DefaultConfigResult> getOrCreateDefaultConfig(List<GameConfig> availableConfigs);

  /// Validate a configuration selection
  /// Ensures the selected config is valid and available
  Result<bool> validateConfigSelection(String configId, List<GameConfig> availableConfigs);

  /// Get configuration by ID
  /// Returns the config if found, null otherwise
  Result<GameConfig?> getConfigById(String configId, List<GameConfig> availableConfigs);

  /// Create fallback configuration
  /// Creates a minimal default config when none are available
  Result<GameConfig> createFallbackConfig();

  /// Create default player slots for a configuration
  /// Generates appropriate slots based on config requirements
  Result<List<PlayerSlot>> createDefaultSlotsForConfig(GameConfig config);

  /// Validate configuration for match creation
  /// Ensures config is suitable for creating matches
  Result<bool> validateConfigForMatchCreation(GameConfig config);

  /// Get recommended configuration
  /// Returns the best config from available options
  Result<GameConfig> getRecommendedConfig(List<GameConfig> availableConfigs);

  /// Handle configuration change
  /// Manages state updates when configuration changes
  Result<ConfigChangeResult> handleConfigurationChange(
    String newConfigId,
    List<GameConfig> availableConfigs,
    List<PlayerSlot> currentSlots,
  );
}

/// Result model for default configuration operations
class DefaultConfigResult {
  final GameConfig config;
  final List<PlayerSlot> defaultSlots;
  final bool isFallbackConfig;
  final String? message;

  const DefaultConfigResult({
    required this.config,
    required this.defaultSlots,
    required this.isFallbackConfig,
    this.message,
  });
}

/// Result model for configuration change operations
class ConfigChangeResult {
  final GameConfig newConfig;
  final List<PlayerSlot> updatedSlots;
  final bool requiresSlotReset;
  final String? message;

  const ConfigChangeResult({
    required this.newConfig,
    required this.updatedSlots,
    required this.requiresSlotReset,
    this.message,
  });
}
