import 'package:common/models/game_match.dart';
import 'package:common/models/player_slot.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';

/// Interface for match management operations
/// Handles match creation, deletion, joining, and leaving
abstract class IMatchManagementUseCase {
  /// Create a new match with validation
  Future<Result<GameMatch>> createMatch(CreateMatchRequest request);

  /// Delete an existing match
  Future<Result<bool>> deleteMatch(DeleteMatchRequest request);

  /// Join a match
  Future<Result<bool>> joinMatch(JoinMatchRequest request);

  /// Leave a match
  Future<Result<bool>> leaveMatch(LeaveMatchRequest request);

  /// Validate if a match configuration is valid
  bool isMatchConfigurationValid(GameConfig? config, List<PlayerSlot> playerSlots, String? gameName);

  /// Determine which repository type should be used based on player slots
  String determineRepositoryType(List<PlayerSlot> playerSlots);

  /// Validate a create match request
  Result<void> validateCreateMatchRequest(CreateMatchRequest request);

  /// Check if the current state is ready to create a match
  bool isReadyToCreateMatch(GameConfig? config, List<PlayerSlot> playerSlots, String? gameName);
}
