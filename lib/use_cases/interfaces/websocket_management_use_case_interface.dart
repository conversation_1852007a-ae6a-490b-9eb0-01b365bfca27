import 'dart:async';
import 'package:common/models/game_match.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/results/match_management_results.dart';

/// Interface for WebSocket subscription management and real-time update processing
/// Handles WebSocket lifecycle, update processing, and match state synchronization
abstract class WebSocketManagementUseCaseInterface {
  /// Set up WebSocket listener for real-time match updates
  /// Returns a stream subscription that can be managed by the caller
  Future<Result<StreamSubscription<List<GameMatch>>>> setupWebSocketListener();

  /// Tear down WebSocket listener and clean up subscriptions
  /// Cancels all active subscriptions and unsubscribes from server notifications
  Future<Result<bool>> teardownWebSocketListener();

  /// Process WebSocket match updates and validate them
  /// Handles match state synchronization and determines if state updates are needed
  Result<MatchUpdateResult> processWebSocketUpdate(
    List<GameMatch> matches,
    Map<String, List<GameMatch>> currentMatchesBySource,
    GameMatch? currentSelectedMatch,
  );

  /// Synchronize match state between local and server versions
  /// Resolves conflicts and determines the authoritative version
  Result<GameMatch> synchronizeMatchState(GameMatch localMatch, GameMatch serverMatch);

  /// Validate WebSocket update data before processing
  /// Ensures the update is well-formed and contains valid match data
  bool validateWebSocketUpdate(List<GameMatch> matches);

  /// Get the network source name for WebSocket updates
  /// Identifies which source should receive the WebSocket updates
  String? getNetworkSourceName(Map<String, dynamic> repositories);

  /// Update selected match if it's included in the WebSocket update
  /// Returns the updated selected match or null if no update is needed
  GameMatch? updateSelectedMatchFromWebSocket(
    List<GameMatch> webSocketMatches,
    GameMatch? currentSelectedMatch,
  );

  /// Rebuild complete matches list from updated sources
  /// Combines all source matches into a single list for state updates
  List<GameMatch> rebuildCompleteMatchesList(Map<String, List<GameMatch>> matchesBySource);
}
