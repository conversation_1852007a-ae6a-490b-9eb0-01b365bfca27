import 'package:common/models/game_match.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/models/common/result.dart';


/// Interface for complex player slot operations
/// Handles server update coordination, slot validation, and local vs network update logic
abstract class PlayerSlotOperationsUseCaseInterface {
  /// Update player type for a selected match with server coordination
  /// Handles both local and network match updates with appropriate server synchronization
  Future<Result<PlayerSlotUpdateResult>> updateSelectedMatchPlayerType(
    UpdateSelectedMatchPlayerTypeRequest request,
  );

  /// Update player name for a slot in match creation mode
  /// Simple local update for match creation
  Result<List<PlayerSlot>> updatePlayerName(
    UpdatePlayerNameRequest request,
  );

  /// Join current user to a player slot in match creation mode
  /// Updates local slot with current user information
  Result<List<PlayerSlot>> joinPlayerSlot(
    JoinPlayerSlotRequest request,
  );

  /// Validate slot index for operations
  /// Ensures slot index is within valid range
  Result<bool> validateSlotIndex(int slotIndex, List<PlayerSlot> slots);

  /// Check if server update is required for a match
  /// Determines if match requires server synchronization
  Result<bool> requiresServerUpdate(
    GameMatch match,
    Map<String, List<GameMatch>> matchesBySource,
  );

  /// Coordinate server update for player slot changes
  /// Handles server repository access and error handling
  Future<Result<bool>> coordinateServerUpdate(
    ServerUpdateRequest request,
  );

  /// Validate server connectivity for updates
  /// Checks if server repository is available and accessible
  Result<bool> validateServerConnectivity();

  /// Get current user information for slot operations
  /// Retrieves user data needed for slot assignments
  Result<UserInfo> getCurrentUserInfo();
}

/// Request model for updating selected match player type
class UpdateSelectedMatchPlayerTypeRequest {
  final GameMatch selectedMatch;
  final int slotIndex;
  final PlayerType playerType;
  final Map<String, List<GameMatch>> matchesBySource;

  const UpdateSelectedMatchPlayerTypeRequest({
    required this.selectedMatch,
    required this.slotIndex,
    required this.playerType,
    required this.matchesBySource,
  });
}

/// Request model for updating player name
class UpdatePlayerNameRequest {
  final List<PlayerSlot> currentSlots;
  final int slotIndex;
  final String name;

  const UpdatePlayerNameRequest({
    required this.currentSlots,
    required this.slotIndex,
    required this.name,
  });
}

/// Request model for joining player slot
class JoinPlayerSlotRequest {
  final List<PlayerSlot> currentSlots;
  final int slotIndex;
  final String userId;
  final String userName;

  const JoinPlayerSlotRequest({
    required this.currentSlots,
    required this.slotIndex,
    required this.userId,
    required this.userName,
  });
}

/// Request model for server updates
class ServerUpdateRequest {
  final String matchId;
  final List<PlayerSlot> updatedSlots;
  final String userId;

  const ServerUpdateRequest({
    required this.matchId,
    required this.updatedSlots,
    required this.userId,
  });
}

/// Result model for player slot update operations
class PlayerSlotUpdateResult {
  final GameMatch updatedMatch;
  final bool serverUpdateSuccessful;
  final bool requiresServerUpdate;
  final String? errorMessage;

  const PlayerSlotUpdateResult({
    required this.updatedMatch,
    required this.serverUpdateSuccessful,
    required this.requiresServerUpdate,
    this.errorMessage,
  });
}

/// User information for slot operations
class UserInfo {
  final String id;
  final String name;

  const UserInfo({
    required this.id,
    required this.name,
  });
}
