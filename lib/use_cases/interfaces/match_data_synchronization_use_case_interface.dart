import 'package:common/models/game_match.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';

/// Interface for match data synchronization operations
/// Handles match data loading, caching, real-time updates, and deduplication
abstract class IMatchDataSynchronizationUseCase {
  /// Load match data from all available sources
  Future<Result<MatchDataResult>> loadMatchData(LoadMatchDataRequest request);

  /// Refresh matches from a specific source
  Future<Result<SourceRefreshResult>> refreshMatchesFromSource(RefreshMatchesRequest request);

  /// Handle match updates from a source (real-time or manual refresh)
  Result<MatchUpdateResult> handleMatchUpdate(List<GameMatch> matches, String sourceName);

  /// Deduplicate matches from multiple sources
  List<GameMatch> deduplicateMatches(Map<String, List<GameMatch>> matchesBySource);

  /// Subscribe to real-time match updates
  Future<Result<void>> subscribeToMatchUpdates();

  /// Unsubscribe from real-time match updates
  Future<Result<void>> unsubscribeFromMatchUpdates();

  /// Handle WebSocket match updates
  Result<MatchUpdateResult> handleWebSocketMatchUpdate(List<GameMatch> matches);

  /// Clear network matches when connection is lost
  Result<MatchDataResult> clearNetworkMatches(MatchDataResult currentData);

  /// Prioritize sources for conflict resolution
  List<GameMatch> prioritizeSourceMatches(Map<String, List<GameMatch>> matchesBySource);

  /// Subscribe to real-time updates with request parameters
  Future<Result<SubscriptionResult>> subscribeToRealTimeUpdates(SubscribeToRealTimeUpdatesRequest request);

  /// Unsubscribe from real-time updates with request parameters
  Future<Result<UnsubscriptionResult>> unsubscribeFromRealTimeUpdates(UnsubscribeFromRealTimeUpdatesRequest request);
}
