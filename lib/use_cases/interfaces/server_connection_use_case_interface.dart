import 'package:common/models/game_match.dart';
import 'package:common/models/player_slot.dart';

import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/results/match_management_results.dart';

/// Interface for server connection state management and lifecycle
/// Handles server scope management, network capability detection, and connection state handling
abstract class ServerConnectionUseCaseInterface {
  /// Handle server scope becoming available
  /// Re-initializes repositories and updates network capabilities
  Future<Result<RepositoryInitializationResult>> handleServerScopeAvailable();

  /// Handle server scope being lost
  /// Cleans up network resources and updates state accordingly
  Future<Result<RepositoryInitializationResult>> handleServerScopeLost();

  /// Detect current network capability based on available repositories
  /// Returns true if any repository supports real-time updates
  Result<bool> detectNetworkCapability(Map<String, dynamic> repositories);

  /// Disconnect from server and clean up network resources
  /// Notifies ServerEnvironmentManager to disconnect
  Future<Result<bool>> disconnectFromServer();

  /// Evaluate network state and determine cleanup actions
  /// Checks if match creation or selected matches need to be cleared
  Result<NetworkStateResult> evaluateNetworkState(
    bool hasNetworkCapability,
    bool isCreatingMatch,
    List<PlayerSlot> playerSlots,
    GameMatch? selectedMatch,
  );

  /// Clean up network matches when connection is lost
  /// Removes network matches from state and clears network-dependent UI state
  Future<Result<bool>> cleanupNetworkMatches(
    Map<String, List<GameMatch>> matchesBySource,
  );

  /// Check if player slots contain network players
  /// Used to determine if a match requires network connectivity
  bool hasNetworkPlayers(List<PlayerSlot> playerSlots);

  /// Check if a match has network players
  /// Used to determine if a selected match requires network connectivity
  bool matchHasNetworkPlayers(GameMatch match);

  /// Get current server scope name
  /// Returns the current GetIt scope name for debugging
  String getCurrentScopeName();
}

/// Result model for network state evaluation
class NetworkStateResult {
  final bool shouldClearMatchCreation;
  final bool shouldClearSelectedMatch;
  final String? reason;

  const NetworkStateResult({
    required this.shouldClearMatchCreation,
    required this.shouldClearSelectedMatch,
    this.reason,
  });
}


