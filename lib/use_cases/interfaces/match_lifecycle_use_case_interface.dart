import 'package:common/models/game_match.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:common/models/player_slot.dart';
import 'package:dauntless/models/common/result.dart';

/// Interface for match lifecycle management operations
/// Handles match creation, deletion, state transitions, and post-creation coordination
abstract class MatchLifecycleUseCaseInterface {
  /// Create and start a new match with the provided configuration
  /// Handles match creation, initial state setup, and post-creation coordination
  Future<Result<MatchCreationResult>> createAndStartMatch(CreateAndStartMatchRequest request);

  /// Delete an existing match
  /// Handles match deletion, cleanup, and state updates
  Future<Result<MatchDeletionResult>> deleteMatch(DeleteMatchRequest request);

  /// Validate match creation request
  /// Ensures all required data is present and valid
  Result<bool> validateMatchCreationRequest(CreateAndStartMatchRequest request);

  /// Handle post-creation coordination
  /// Manages state updates and event triggering after match creation
  Result<PostCreationActions> handlePostCreationCoordination(
    <PERSON>Match createdMatch,
    CreateAndStartMatchRequest originalRequest,
  );

  /// Handle post-deletion cleanup
  /// Manages state cleanup and updates after match deletion
  Result<PostDeletionActions> handlePostDeletionCleanup(
    String deletedMatchId,
    GameMatch? selectedMatch,
  );

  /// Generate unique match ID
  /// Creates a unique identifier for new matches
  Result<String> generateMatchId();

  /// Validate match deletion request
  /// Ensures match exists and can be deleted
  Future<Result<bool>> validateMatchDeletionRequest(DeleteMatchRequest request);
}

/// Request model for creating and starting a match
class CreateAndStartMatchRequest {
  final GameConfig gameConfig;
  final List<PlayerSlot> playerSlots;
  final String gameName;
  final bool openForJoining;
  final String? customMatchId;

  const CreateAndStartMatchRequest({
    required this.gameConfig,
    required this.playerSlots,
    required this.gameName,
    required this.openForJoining,
    this.customMatchId,
  });
}

/// Request model for deleting a match
class DeleteMatchRequest {
  final String matchId;
  final bool forceDelete;

  const DeleteMatchRequest({
    required this.matchId,
    this.forceDelete = false,
  });
}

/// Result model for match creation operations
class MatchCreationResult {
  final GameMatch createdMatch;
  final bool requiresDataRefresh;
  final bool shouldSelectMatch;
  final String? successMessage;

  const MatchCreationResult({
    required this.createdMatch,
    required this.requiresDataRefresh,
    required this.shouldSelectMatch,
    this.successMessage,
  });
}

/// Result model for match deletion operations
class MatchDeletionResult {
  final String deletedMatchId;
  final bool requiresDataRefresh;
  final bool shouldClearSelection;
  final String? successMessage;

  const MatchDeletionResult({
    required this.deletedMatchId,
    required this.requiresDataRefresh,
    required this.shouldClearSelection,
    this.successMessage,
  });
}

/// Actions to perform after match creation
class PostCreationActions {
  final bool shouldRefreshData;
  final bool shouldSelectMatch;
  final bool shouldEnterSelectionMode;
  final String? statusMessage;

  const PostCreationActions({
    required this.shouldRefreshData,
    required this.shouldSelectMatch,
    required this.shouldEnterSelectionMode,
    this.statusMessage,
  });
}

/// Actions to perform after match deletion
class PostDeletionActions {
  final bool shouldClearSelection;
  final bool shouldRefreshData;
  final String? statusMessage;

  const PostDeletionActions({
    required this.shouldClearSelection,
    required this.shouldRefreshData,
    this.statusMessage,
  });
}
