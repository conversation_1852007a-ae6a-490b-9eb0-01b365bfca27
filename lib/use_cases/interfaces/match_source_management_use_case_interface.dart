import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';

/// Interface for match source management operations
/// Handles repository lifecycle, source availability, and network capability monitoring
abstract class IMatchSourceManagementUseCase {
  /// Initialize all available repositories
  Future<Result<RepositoryInitializationResult>> initializeRepositories();

  /// Clean up all repositories and subscriptions
  void cleanupRepositories();

  /// Check if network capability is available
  bool hasNetworkCapability();

  /// Get list of available source names
  List<String> getAvailableSources();

  /// Handle server scope becoming available
  Future<Result<void>> handleServerScopeAvailable();

  /// Handle server scope being lost
  Future<Result<void>> handleServerScopeLost();

  /// Check and add network sources if available
  Future<bool> checkAndAddNetworkSources();

  /// Get repository capabilities for each source
  Map<String, bool> getSourceCapabilities();

  /// Check if a specific source is available
  bool isSourceAvailable(String sourceName);

  /// Refresh source availability
  Future<Result<List<String>>> refreshAvailableSources();

  /// Remove a match source
  Future<Result<SourceRemovalResult>> removeMatchSource(RemoveMatchSourceRequest request);
}
