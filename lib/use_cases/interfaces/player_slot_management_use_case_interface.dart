import 'package:common/models/player_slot.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';

/// Interface for player slot management operations
/// Handles player slot CRUD operations, validation, and constraints
abstract class IPlayerSlotManagementUseCase {
  /// Add a new player slot to the current slots
  Result<List<PlayerSlot>> addPlayerSlot(List<PlayerSlot> currentSlots, GameConfig config);

  /// Remove a player slot at the specified index
  Result<List<PlayerSlot>> removePlayerSlot(List<PlayerSlot> currentSlots, int index);

  /// Update the player type for a specific slot
  Future<Result<PlayerSlotUpdateResult>> updatePlayerType(UpdatePlayerTypeRequest request);

  /// Join the current user to a player slot
  Future<Result<bool>> joinPlayerSlot(JoinSlotRequest request);

  /// Create default player slots for a game configuration
  List<PlayerSlot> createDefaultSlots(GameConfig config);

  /// Validate player slot constraints
  Result<void> validatePlayerSlots(List<PlayerSlot> slots, GameConfig config);

  /// Check if adding a slot would exceed the maximum allowed
  bool canAddPlayerSlot(List<PlayerSlot> currentSlots, GameConfig config);

  /// Check if a slot can be removed (minimum constraints)
  bool canRemovePlayerSlot(List<PlayerSlot> currentSlots, int index, GameConfig config);

  /// Update player name in a slot
  Result<List<PlayerSlot>> updatePlayerName(List<PlayerSlot> currentSlots, int slotIndex, String name);

  /// Set a player slot as the host player
  Result<List<PlayerSlot>> setHostPlayer(List<PlayerSlot> currentSlots, String slotId);
}
