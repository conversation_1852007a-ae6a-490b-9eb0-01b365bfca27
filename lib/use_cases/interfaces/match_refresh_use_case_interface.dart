import 'package:common/models/game_match.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';


/// Interface for match refresh and source management operations
/// Handles match data refreshing, source error handling, and match list rebuilding
abstract class MatchRefreshUseCaseInterface {
  /// Refresh matches from a specific source
  /// Handles source validation, error handling, and match list rebuilding
  Future<Result<MatchRefreshResult>> refreshMatchesFromSource(RefreshMatchesFromSourceRequest request);

  /// Rebuild complete matches list from multiple sources
  /// Combines matches from all sources into a single list
  Result<List<GameMatch>> rebuildCompleteMatchesList(Map<String, List<GameMatch>> matchesBySource);

  /// Handle source refresh errors with appropriate fallback logic
  /// Different handling for local vs network sources
  Result<MatchRefreshErrorResult> handleSourceRefreshError(
    String sourceName,
    String error,
    Map<String, List<GameMatch>> currentMatchesBySource,
  );

  /// Validate source before refresh operation
  /// Checks if source exists and is accessible
  Result<bool> validateSourceForRefresh(String sourceName);

  /// Update matches for a specific source in the matches map
  /// Returns updated matches by source map
  Result<Map<String, List<GameMatch>>> updateSourceMatches(
    Map<String, List<GameMatch>> currentMatchesBySource,
    String sourceName,
    List<GameMatch> newMatches,
  );

  /// Check if a source should use graceful error handling
  /// Local sources typically show empty state, network sources show errors
  bool shouldUseGracefulErrorHandling(String sourceName);

  /// Get default game name for match fetching
  /// Provides fallback when no specific game context is available
  String getDefaultGameName();
}

/// Result model for match refresh operations
class MatchRefreshResult {
  final List<GameMatch> refreshedMatches;
  final List<GameMatch> allMatches;
  final Map<String, List<GameMatch>> updatedMatchesBySource;
  final String sourceName;
  final bool success;
  final String? message;

  const MatchRefreshResult({
    required this.refreshedMatches,
    required this.allMatches,
    required this.updatedMatchesBySource,
    required this.sourceName,
    required this.success,
    this.message,
  });
}

/// Result model for source refresh error handling
class MatchRefreshErrorResult {
  final bool shouldShowError;
  final bool shouldShowEmptyState;
  final List<GameMatch> fallbackMatches;
  final Map<String, List<GameMatch>> updatedMatchesBySource;
  final String? errorMessage;

  const MatchRefreshErrorResult({
    required this.shouldShowError,
    required this.shouldShowEmptyState,
    required this.fallbackMatches,
    required this.updatedMatchesBySource,
    this.errorMessage,
  });
}
