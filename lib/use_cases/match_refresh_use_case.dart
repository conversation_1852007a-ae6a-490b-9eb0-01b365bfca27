import 'package:common/models/game_match.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';

import 'package:dauntless/use_cases/interfaces/match_refresh_use_case_interface.dart';
import 'package:dauntless/use_cases/interfaces/repository_management_use_case_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:injectable/injectable.dart';

/// Concrete implementation of match refresh and source management operations
/// Extracted from MatchManagementBloc to improve testability and separation of concerns
@singleton
class MatchRefreshUseCase implements MatchRefreshUseCaseInterface {
  final RepositoryManagementUseCaseInterface _repositoryManagementUseCase;
  final RemoteLogger _logger;

  MatchRefreshUseCase(
    this._repositoryManagementUseCase,
    this._logger,
  );

  @override
  Future<Result<MatchRefreshResult>> refreshMatchesFromSource(RefreshMatchesFromSourceRequest request) async {
    try {
      _logger.info('Refreshing matches from source: ${request.sourceName}');

      // Validate source
      final validationResult = validateSourceForRefresh(request.sourceName);
      if (validationResult.isFailure) {
        return Result.failure(validationResult.error);
      }

      // Get repository for the source
      final repository = _repositoryManagementUseCase.getRepository(request.sourceName);
      if (repository == null) {
        _logger.warn('Repository not found for source: ${request.sourceName}');
        return Result.failure('Repository not found for source: ${request.sourceName}');
      }

      // Fetch matches from the repository
      final gameName = request.gameName ?? getDefaultGameName();
      final matches = await repository.fetchOpenMatches(gameName);

      // Update the matches by source map
      final updateResult = updateSourceMatches(
        request.currentMatchesBySource,
        request.sourceName,
        matches,
      );

      if (updateResult.isFailure) {
        return Result.failure(updateResult.error);
      }

      // Rebuild complete matches list
      final allMatchesResult = rebuildCompleteMatchesList(updateResult.value);
      if (allMatchesResult.isFailure) {
        return Result.failure(allMatchesResult.error);
      }

      _logger.info('Successfully refreshed ${matches.length} matches from ${request.sourceName}');

      return Result.success(MatchRefreshResult(
        refreshedMatches: matches,
        allMatches: allMatchesResult.value,
        updatedMatchesBySource: updateResult.value,
        sourceName: request.sourceName,
        success: true,
        message: 'Successfully refreshed ${matches.length} matches',
      ));
    } catch (e) {
      _logger.error('Failed to refresh matches from ${request.sourceName}: $e');

      // Handle error with appropriate fallback logic
      final errorResult = handleSourceRefreshError(
        request.sourceName,
        e.toString(),
        request.currentMatchesBySource,
      );

      if (errorResult.isFailure) {
        return Result.failure('Failed to refresh matches: $e');
      }

      final errorData = errorResult.value;
      
      if (errorData.shouldShowError) {
        return Result.failure('Failed to refresh matches from ${request.sourceName}: $e');
      } else {
        // Return success with empty/fallback data for graceful handling
        return Result.success(MatchRefreshResult(
          refreshedMatches: errorData.fallbackMatches,
          allMatches: errorData.fallbackMatches,
          updatedMatchesBySource: errorData.updatedMatchesBySource,
          sourceName: request.sourceName,
          success: false,
          message: 'Source unavailable, showing cached data',
        ));
      }
    }
  }

  @override
  Result<List<GameMatch>> rebuildCompleteMatchesList(Map<String, List<GameMatch>> matchesBySource) {
    try {
      final allMatches = matchesBySource.values.expand((list) => list).toList();
      return Result.success(allMatches);
    } catch (e) {
      _logger.error('Error rebuilding complete matches list: $e');
      return Result.failure('Error rebuilding matches list: $e');
    }
  }

  @override
  Result<MatchRefreshErrorResult> handleSourceRefreshError(
    String sourceName,
    String error,
    Map<String, List<GameMatch>> currentMatchesBySource,
  ) {
    try {
      final isLocalSource = _repositoryManagementUseCase.isLocalSource(sourceName);
      final shouldUseGracefulHandling = shouldUseGracefulErrorHandling(sourceName);

      if (shouldUseGracefulHandling) {
        // For local sources, handle errors gracefully by showing empty state
        _logger.info('Handling $sourceName source error gracefully, showing empty state');
        
        final updatedMatchesBySource = Map<String, List<GameMatch>>.from(currentMatchesBySource);
        updatedMatchesBySource[sourceName] = []; // Show empty list for local sources

        // Rebuild the complete matches list
        final allMatchesResult = rebuildCompleteMatchesList(updatedMatchesBySource);
        final allMatches = allMatchesResult.isSuccess ? allMatchesResult.value : <GameMatch>[];

        return Result.success(MatchRefreshErrorResult(
          shouldShowError: false,
          shouldShowEmptyState: true,
          fallbackMatches: allMatches,
          updatedMatchesBySource: updatedMatchesBySource,
          errorMessage: null, // Don't show error for local sources
        ));
      } else {
        // For network sources, show the error
        return Result.success(MatchRefreshErrorResult(
          shouldShowError: true,
          shouldShowEmptyState: false,
          fallbackMatches: [],
          updatedMatchesBySource: currentMatchesBySource,
          errorMessage: 'Failed to refresh matches from $sourceName: $error',
        ));
      }
    } catch (e) {
      _logger.error('Error handling source refresh error: $e');
      return Result.failure('Error handling source refresh error: $e');
    }
  }

  @override
  Result<bool> validateSourceForRefresh(String sourceName) {
    return _repositoryManagementUseCase.validateRepository(sourceName);
  }

  @override
  Result<Map<String, List<GameMatch>>> updateSourceMatches(
    Map<String, List<GameMatch>> currentMatchesBySource,
    String sourceName,
    List<GameMatch> newMatches,
  ) {
    try {
      final updatedMatchesBySource = Map<String, List<GameMatch>>.from(currentMatchesBySource);
      updatedMatchesBySource[sourceName] = newMatches;
      return Result.success(updatedMatchesBySource);
    } catch (e) {
      _logger.error('Error updating source matches: $e');
      return Result.failure('Error updating source matches: $e');
    }
  }

  @override
  bool shouldUseGracefulErrorHandling(String sourceName) {
    return _repositoryManagementUseCase.isLocalSource(sourceName);
  }

  @override
  String getDefaultGameName() {
    return 'default'; // TODO: Get from current game context or configuration
  }
}
