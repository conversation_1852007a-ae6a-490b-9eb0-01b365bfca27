import 'dart:async';


import 'package:dauntless/frameworks/game_match/game_match_state.dart';

import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/locations_use_case.dart';
import 'package:dauntless/models/base/card_action.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/repositories/match_repository.dart';
import 'package:dauntless/repositories/players_repository.dart';

import 'package:get_it/get_it.dart';

// Type aliases to match the codebase conventions
typedef GameMatchId = String;
typedef PlayerId = String;
typedef GameCardId = String;

class MatchUseCase {
  final RemoteLogger _logger;
  final PlayersRepository _playersRepository;
  final MatchRepository _matchRepository;

  MatchUseCase(
    this._logger,
    this._playersRepository,
    this._matchRepository,
  );

  bool get allPlayersSubmittedTurn =>
      _matchRepository.submittedActiveActions.length ==
      _playersRepository.playablePlayers.length;
      
  Future<GameMatchState> submitPlayerTurn(GameMatchState state) async {
    _logger.info('Submitting player turn for player: ${state.userPlayerId} to game_match: ${state.matchConfig.gameId} -- game mode: ${state.matchConfig.selectedGameMode}; submitting with ${state.currentTurnActions.length} actions');
    final gameMode = state.matchConfig.selectedGameMode;
    final selectedActions = List<TargetedAction>.from(state.currentTurnActions);
    final gameId = state.matchConfig.gameId;

        // Submit the player's actions to the repository
        _matchRepository.submitPlayerTurnActions(
            state.userPlayerId, selectedActions);
        
        // TODO: handle hot seat w/ more players; player order
        state = state.copyWith(
          userPlayerId: _playersRepository.playablePlayers
              .firstWhere((player) => player.id != state.userPlayerId)
              .id,
        );

    return state.copyWith(
      activeAction: null,
      currentTurnActions: [],
    );
  }

  Future<GameMatchState> _processActivePlayerTurnFromState(GameMatchState state) async {
    final selectedActions = List<TargetedAction>.from(state.currentTurnActions);
    var updatedState = await _processPlayerTurnActions(state, selectedActions);
    return _processActiveTurnActions(updatedState);
  }

  Future<GameMatchState> _processActiveTurnActions(GameMatchState state) async {
    /// process all active actions
    final activeActions = state.getCardsWithActiveActions();
    for (final playerId in activeActions.keys) {
      final playerActions = activeActions[playerId]!;
      for (final card in playerActions) {
        for (final action in card.activeActions) {
          state = _processAction(state, action);
        }
      }
    }

    return state;
  }

  Future<GameMatchState> _processPlayerTurnActions(
      GameMatchState state, List<TargetedAction> actions) async {
    /// handle new actions from this turn
    for (final action in actions) {
      state = _handleNewAction(state, action);
    }

    return state;
  }

  Future<GameMatchState> processTurn(GameMatchState matchState) async {
    final playerActionsToProcess =
        Map.from(_matchRepository.submittedActiveActions);

    _matchRepository.clearSubmittedActiveActions();

    for (final playerId in playerActionsToProcess.keys) {
      // final player = _playersRepository.players.firstWhereOrNull((player) => player.id == playerId);// .getPlayerForId(playerId);
      // if (player == null) {
      //   _logger.warn('Could not find player for id: $playerId');
      //   continue;
      // }
      matchState = await _processPlayerTurnActions(
          matchState, playerActionsToProcess[playerId]);
    }

    return (await _processActiveTurnActions(matchState)).copyWith(
      turnCount: matchState.turnCount + 1,
    );
  }

  GameMatchState handleImmediateAction(
          GameMatchState state, TargetedAction selectedAction) =>
      _handleNewAction(state, selectedAction);

  TargetedAction _getReconciledAction(GameMatchState state, CardAction action,
          GameCardId cardId, GameCardId? objectCardId) =>
      TargetedAction.genId(
        action: action,
        subjectCardId: cardId,
        subjectLocationId: state.getCardForId(cardId).$2?.locationId,
        objectCardId: objectCardId,
        reconciledAttributes: state.getReconciledAttributes(cardId),
      );

  GameMatchState handleSelectActiveAction(
      GameMatchState state, CardAction action, GameCardId subjectCardId) {
    final activeAction = _getReconciledAction(
        state, action, subjectCardId, null); //, objectCard?.id);

    /// if action is selected with a target
    if (activeAction.objectCardId != null) {
      /// if action is immediate, handle it immediately
      if (activeAction.action.actionAttributes
          .contains(ActionAttribute.immediateEffect)) {
        return handleImmediateAction(state, state.activeAction!);
      }
      return state.copyWith(
        activeAction: null,
        currentTurnActions: List.from(state.currentTurnActions)
          ..add(activeAction),
      );
    }

    /// otherwise, action is set 'active' for the user to select a target
    return state.copyWith(activeAction: activeAction);
  }

  GameMatchState createNewGroup(GameMatchState state, GameCard card) {
    final updatedHands = Map<String, List<GameCard>>.from(state.hands);
    final updatedHand =
        List<GameCard>.from(updatedHands[state.userPlayerId] ?? []);

    updatedHand.removeWhere((hardCard) => hardCard.id == card.id);

    final id = _getValidNewIdForGrouping(state, card.type);

    // TODO: CLEAN this
    final locationId = GetIt.I<LocationsUseCase>()
        .getBaseLocationCard(state.hands, card.locationId)
        ?.id;

    final newGroup = GameCard(
      id: id,
      name: id,
      classId: card.classId,
      type: CardType.grouping,
      locationId: locationId,
    );

    final updatedCard = card.copyWith(
      locationId: newGroup.id,
    );

    updatedHand.add(newGroup);
    updatedHand.add(updatedCard);
    updatedHands[state.userPlayerId] = updatedHand;

    return state.copyWith(
      hands: updatedHands,
      activeAction: null,
    );
  }

  GameMatchState _handleNewAction(GameMatchState state, TargetedAction selectedAction) {
    switch (selectedAction.action.type) {
      case ActionType.move:
        return _handleNewMoveAction(state, selectedAction);
      case ActionType.construct:
        return _handleNewConstructAction(state, selectedAction);
      case ActionType.changeGroup:
        return _handleNewTransferAction(state, selectedAction);
    }
  }

  GameMatchState _handleNewMoveAction(
      GameMatchState state, TargetedAction selectedAction) {
    final (playerId!, card!) = state.getCardForId(selectedAction.subjectCardId);

    final updatedAction = selectedAction.copyWith();

    final updatedCard = card.copyWith(
      locationId: inTransitLocationId, //objectCard?.id,
      activeActions: List.from(card.activeActions)..add(updatedAction),
    );

    final updatedHands = Map<String, List<GameCard>>.from(state.hands);

    final updatedHand = List<GameCard>.from(updatedHands[playerId] ?? []);

    updatedHand.removeWhere((card) => card.id == updatedCard.id);
    updatedHand.add(updatedCard);
    updatedHands[playerId] = updatedHand;

    return state.copyWith(
      hands: updatedHands,
    );
  }

  GameMatchState _handleNewConstructAction(
      GameMatchState state, TargetedAction selectedAction) {
    final (playerId, card) = state.getCardForId(selectedAction.subjectCardId);
    if (playerId == null || card == null) {
      _logger.warn('Could not find card for action: $selectedAction');
      return state;
    }
    final updatedAction = selectedAction.copyWith();
    final updatedCard = card.copyWith(
      activeActions: List.from(card.activeActions)..add(updatedAction),
    );
    final updatedHands = Map<String, List<GameCard>>.from(state.hands);
    final updatedHand = List<GameCard>.from(updatedHands[playerId] ?? []);
    updatedHand.removeWhere((card) => card.id == updatedCard.id);
    updatedHand.add(updatedCard);
    updatedHands[playerId] = updatedHand;
    return state.copyWith(
      hands: updatedHands,
    );
  }

  GameMatchState _handleNewTransferAction(
      GameMatchState state, TargetedAction selectedAction) {
    // TODO: implement
    return state;
    // final updatedHands = Map<String, List<GameCard>>.from(state.hands);
    // final updatedTurnActions = List<SelectedAction>.from(state.currentTurnActions);
    //
    // final updatedHand = List<GameCard>.from(updatedHands[_playersRepository.currentPlayer.id] ?? []);
    // updatedHand.removeWhere((card) => card.id == selectedAction.subjectCard.id);
    //
    // updatedHands[_playersRepository.currentPlayer.id] = updatedHand;
    //
    // updatedTurnActions.removeWhere((action) => action.subjectCard == selectedAction.subjectCard);
    //
    // return state.copyWith(
    //   hands: updatedHands,
    //   currentTurnActions: updatedTurnActions,
    // );
  }

  GameMatchState _processMoveActionComplete(
      GameMatchState state, TargetedAction action) {
    final (playerId, card) = state.getCardForId(action.subjectCardId);
    if (playerId == null || card == null) {
      _logger.warn('Could not find card for action: $action');
      return state;
    }
    final updatedCard = card.copyWith(
      locationId: action.objectCardId,
      activeActions: List<TargetedAction>.from(card.activeActions)
        ..removeWhere((action) => action.id == action.id),
    );
    final updatedHand = List<GameCard>.from(state.hands[playerId] ?? []);
    updatedHand.removeWhere((card) => card.id == action.subjectCardId);
    updatedHand.add(updatedCard);
    return state.copyWith(
      hands: {
        ...state.hands,
        playerId: updatedHand,
      },
    );
  }

  GameMatchState _processConstructActionComplete(
      GameMatchState state, TargetedAction action) {
    final (playerId, subjectCard) = state.getCardForId(action.subjectCardId);
    if (playerId == null || subjectCard == null) {
      _logger.warn('Could not find card for action: $action');
      return state;
    }
    final updatedCard = subjectCard.copyWith(
      activeActions: List<TargetedAction>.from(subjectCard.activeActions)
        ..removeWhere((action) => action.id == action.id),
    );
    final updatedHand = List<GameCard>.from(state.hands[playerId] ?? []);
  
    updatedHand.removeWhere((card) => card.id == updatedCard.id);
    updatedHand.add(updatedCard);

    final cardClass = state.getCardClassForId(action.objectCardClassId);
    if (cardClass == null) {
      _logger.warn(
          'Could not find card class for id: ${action.objectCardClassId}');
    }
    final objectCard = GameCard.fromGameCardClass(
      cardClass!,
      locationId: subjectCard.locationId,
    );

    updatedHand.add(objectCard);

    return state.copyWith(
      hands: {
        ...state.hands,
        playerId: updatedHand,
      },
    );
  }

  GameMatchState _processTransferActionComplete(
      GameMatchState state, TargetedAction action) {
    return state;
  }

  GameMatchState _processActionComplete(GameMatchState state, TargetedAction action) {
    switch (action.action.type) {
      case ActionType.move:
        return _processMoveActionComplete(state, action);
      case ActionType.construct:
        return _processConstructActionComplete(state, action);
      case ActionType.changeGroup:
        return _processTransferActionComplete(state, action);
    }
  }

  GameMatchState _processAction(GameMatchState state, TargetedAction action) {
    if (action.remainingTurns > 0) {
      final (playerId, card) = state.getCardForId(action.subjectCardId);
      if (playerId == null || card == null) {
        _logger.warn('Could not find card for action: $action');
        return state;
      }
      final updatedHand = List<GameCard>.from(state.hands[playerId] ?? []);
      updatedHand.removeWhere((card) => card.id == action.subjectCardId);
      final currentActions = List<TargetedAction>.from(card.activeActions)
        ..removeWhere((action) => action.id == action.id);
      final updatedAction =
          action.copyWith(remainingTurns: action.remainingTurns - 1);
      updatedHand.add(
          card.copyWith(activeActions: currentActions..add(updatedAction)));
      return state.copyWith(
        hands: {
          ...state.hands,
          playerId: updatedHand,
        },
      );
    }
    return _processActionComplete(state, action);
    return state;
    // final action = selectedAction.action;
    // final card = state.getCardForId(selectedAction.subjectCardId).$2!;
    // final objectCard = selectedAction.objectCard;
    // final subjectLocationCard = selectedAction.subjectLocationCard;
    //
    // switch (action.type) {
    //   case ActionType.move:
    //     return _handleNewMoveAction(state, selectedAction);
    //   case ActionType.changeGroup:
    //     return _handleNewTransferAction(state, selectedAction);
    // }
  }

  GameCardId _getValidNewIdForGrouping(GameMatchState state, CardType type) {
    final groupingName = type.groupingName.toLowerCase();
    final groupingsOfName = state.hands[state.userPlayerId]?.where(
        (card) => groupingName.matchAsPrefix(card.name.toLowerCase()) != null);
    final groupingsOfNameIdSuffixes = groupingsOfName
        ?.map((card) => card.name.substring(groupingName.length));
    final groupingsOfNameIdSuffixesInt =
        groupingsOfNameIdSuffixes?.map((id) => int.tryParse(id));
    final maxId = (groupingsOfNameIdSuffixesInt?.reduce((value, element) =>
            (value ?? 0) > (element ?? 0) ? value : element) ??
        0);
    return groupingName + (maxId + 1).toString();
    // return groupingName+const Uuid().v1();
  }
  
  // Match selection methods have been moved to MatchSelectionUseCase
  // to better follow CLEAN architecture principles
  // Methods removed:
  // - createMatch
  // - openMatchForJoining
  // - joinMatch
  // - fetchOpenMatches
  
  // Removed duplicate joinMatch method
}
