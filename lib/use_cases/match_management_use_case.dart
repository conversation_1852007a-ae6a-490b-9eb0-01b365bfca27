import 'package:common/models/game_match.dart';
import 'package:common/models/match_status.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:common/models/converters/generate_id_if_needed_converter.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/use_cases/interfaces/match_management_use_case_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';

/// Concrete implementation of match management operations
/// Handles match creation, deletion, joining, and leaving with business logic validation
class MatchManagementUseCase implements IMatchManagementUseCase {
  final Map<String, MatchRepositoryInterface> _repositories;
  final UserManager _userManager;
  final RemoteLogger _logger;

  MatchManagementUseCase(
    this._repositories,
    this._userManager,
    this._logger,
  );

  @override
  Future<Result<GameMatch>> createMatch(CreateMatchRequest request) async {
    try {
      // Validate request
      final validation = validateCreateMatchRequest(request);
      if (validation.isFailure) return validation.cast<GameMatch>();

      // Get user
      final user = _userManager.state.user;
      if (user == null) return Result.failure('No user logged in');

      // Create match object
      final match = GameMatch(
        id: GenerateIdIfNeededConverter().fromJson(null),
        gameTypeId: request.gameConfig.id,
        playerSlots: request.playerSlots,
        status: request.openForJoining ? MatchStatus.open : MatchStatus.active,
        creatorId: user.id,
        createdAt: DateTime.now().millisecondsSinceEpoch,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
        gameName: request.gameName,
      );

      // Determine repository
      final repositoryType = determineRepositoryType(request.playerSlots);
      final repository = _repositories[repositoryType];
      if (repository == null) {
        return Result.failure('$repositoryType repository not available');
      }

      // Create match
      final createdMatch = await repository.createMatch(match, request.gameConfig);
      if (createdMatch == null) {
        return Result.failure('Failed to create match');
      }

      _logger.info('Successfully created match: ${createdMatch.id}');
      return Result.success(createdMatch);

    } catch (e) {
      _logger.error('Error creating match: $e');
      return Result.failure('Error creating match: $e');
    }
  }

  @override
  Future<Result<bool>> deleteMatch(DeleteMatchRequest request) async {
    try {
      // Find the match in available repositories
      GameMatch? targetMatch;
      MatchRepositoryInterface? targetRepository;

      for (final entry in _repositories.entries) {
        final repository = entry.value;
        try {
          final matches = await repository.fetchOpenMatches('default'); // TODO: Use proper game name
          final match = matches.where((m) => m.id == request.matchId).firstOrNull;
          if (match != null) {
            targetMatch = match;
            targetRepository = repository;
            break;
          }
        } catch (e) {
          _logger.warn('Error searching for match in ${entry.key}: $e');
          continue;
        }
      }

      if (targetMatch == null || targetRepository == null) {
        return Result.failure('Match not found');
      }

      // Delete the match
      final success = await targetRepository.deleteMatch(request.matchId);
      if (success) {
        _logger.info('Successfully deleted match: ${request.matchId}');
        return Result.success(true);
      } else {
        return Result.failure('Failed to delete match');
      }

    } catch (e) {
      _logger.error('Error deleting match: $e');
      return Result.failure('Error deleting match: $e');
    }
  }

  @override
  Future<Result<bool>> joinMatch(JoinMatchRequest request) async {
    try {
      // Find the match in available repositories
      GameMatch? targetMatch;
      MatchRepositoryInterface? targetRepository;

      for (final entry in _repositories.entries) {
        final repository = entry.value;
        try {
          final matches = await repository.fetchOpenMatches('default'); // TODO: Use proper game name
          final match = matches.where((m) => m.id == request.matchId).firstOrNull;
          if (match != null) {
            targetMatch = match;
            targetRepository = repository;
            break;
          }
        } catch (e) {
          _logger.warn('Error searching for match in ${entry.key}: $e');
          continue;
        }
      }

      if (targetMatch == null || targetRepository == null) {
        return Result.failure('Match not found');
      }

      // Join the match
      final success = await targetRepository.joinMatch(request.matchId, playerId: request.playerId);
      if (success) {
        _logger.info('Successfully joined match: ${request.matchId}');
        return Result.success(true);
      } else {
        return Result.failure('Failed to join match');
      }

    } catch (e) {
      _logger.error('Error joining match: $e');
      return Result.failure('Error joining match: $e');
    }
  }

  @override
  Future<Result<bool>> leaveMatch(LeaveMatchRequest request) async {
    try {
      // Find the match in available repositories
      GameMatch? targetMatch;
      MatchRepositoryInterface? targetRepository;

      for (final entry in _repositories.entries) {
        final repository = entry.value;
        try {
          final matches = await repository.fetchOpenMatches('default'); // TODO: Use proper game name
          final match = matches.where((m) => m.id == request.matchId).firstOrNull;
          if (match != null) {
            targetMatch = match;
            targetRepository = repository;
            break;
          }
        } catch (e) {
          _logger.warn('Error searching for match in ${entry.key}: $e');
          continue;
        }
      }

      if (targetMatch == null || targetRepository == null) {
        return Result.failure('Match not found');
      }

      // Leave the match
      final success = await targetRepository.leaveMatch(request.matchId, playerId: request.playerId);
      if (success) {
        _logger.info('Successfully left match: ${request.matchId}');
        return Result.success(true);
      } else {
        return Result.failure('Failed to leave match');
      }

    } catch (e) {
      _logger.error('Error leaving match: $e');
      return Result.failure('Error leaving match: $e');
    }
  }

  @override
  bool isMatchConfigurationValid(GameConfig? config, List<PlayerSlot> playerSlots, String? gameName) {
    if (config == null) return false;
    if (playerSlots.isEmpty) return false;
    if (gameName?.isEmpty ?? true) return false;
    return true;
  }

  @override
  String determineRepositoryType(List<PlayerSlot> playerSlots) {
    final hasNetworkPlayers = playerSlots.any((slot) =>
        slot.type == PlayerType.humanNetwork || slot.type == PlayerType.botNetwork);
    return hasNetworkPlayers ? 'network' : 'local';
  }

  @override
  Result<void> validateCreateMatchRequest(CreateMatchRequest request) {
    if (request.playerSlots.isEmpty) {
      return Result.failure('At least one player slot required');
    }
    if (request.gameName.isEmpty) {
      return Result.failure('Game name is required');
    }
    return Result.success(null);
  }

  @override
  bool isReadyToCreateMatch(GameConfig? config, List<PlayerSlot> playerSlots, String? gameName) {
    return isMatchConfigurationValid(config, playerSlots, gameName);
  }
}
