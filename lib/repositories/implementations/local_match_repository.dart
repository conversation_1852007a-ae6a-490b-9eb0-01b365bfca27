import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;
import 'package:common/models/game_match.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';

/// Local file-based implementation of MatchRepositoryInterface
/// Handles match operations using local file system storage
class LocalMatchRepository implements MatchRepositoryInterface {
  final RemoteLogger? _logger;

  LocalMatchRepository(this._logger);

  @override
  String get sourceName => 'Local';

  @override
  bool get supportsRealTimeUpdates => false;

  @override
  Future<List<GameMatch>> fetchOpenMatches(String gameName) async {
    try {
      _logger?.info('Loading saved games for $gameName');

      // Build the path to the saved games directory
      final savesDir = Directory(path.join('games', gameName, 'savedGames'));

      // Check if the directory exists
      if (!await savesDir.exists()) {
        _logger?.warn('Saved games directory does not exist: ${savesDir.path}');
        return [];
      }

      final savedGames = <GameMatch>[];

      // Iterate through all files in the directory
      await for (final entity in savesDir.list()) {
        if (entity is File && entity.path.endsWith('.json')) {
          try {
            // Read and parse the saved game file
            final jsonContent = await entity.readAsString();
            final jsonData = jsonDecode(jsonContent) as Map<String, dynamic>;

            // Create a GameMatch object from the JSON data
            final gameMatch = GameMatch.fromJson(jsonData);
            savedGames.add(gameMatch);

            final fileName = path.basename(entity.path);
            _logger?.info('Loaded saved game: $fileName');
          } catch (e) {
            _logger?.error('Error parsing saved game file ${entity.path}: $e');
            // Continue to next file even if this one fails
          }
        }
      }

      _logger?.info('Loaded ${savedGames.length} saved games for $gameName');
      return savedGames;
    } catch (e) {
      _logger?.error('Error loading saved games: $e');
      return [];
    }
  }

  @override
  Future<GameMatch?> createMatch(GameMatch newMatch, GameConfig gameConfig) async {
    try {
      _logger?.info('Creating local match: ${newMatch.id}');
      
      // For local matches, we just return the match as-is since it's already created
      // In a more sophisticated implementation, we might save it to a specific location
      return newMatch;
    } catch (e) {
      _logger?.error('Error creating local match: $e');
      return null;
    }
  }

  @override
  Future<bool> joinMatch(String matchId, {PlayerId? playerId}) async {
    try {
      _logger?.info('Joining local match: $matchId');
      // For local matches, joining is always successful
      // In a more sophisticated implementation, we might update the match file
      return true;
    } catch (e) {
      _logger?.error('Error joining local match: $e');
      return false;
    }
  }

  @override
  Future<bool> leaveMatch(String matchId, {PlayerId? playerId}) async {
    try {
      _logger?.info('Leaving local match: $matchId');
      // For local matches, leaving is always successful
      return true;
    } catch (e) {
      _logger?.error('Error leaving local match: $e');
      return false;
    }
  }

  @override
  Future<bool> deleteMatch(String matchId) async {
    try {
      _logger?.info('Deleting local match: $matchId');
      // TODO: Implement actual file deletion based on match ID
      // This would require finding the corresponding file and deleting it
      return true;
    } catch (e) {
      _logger?.error('Error deleting local match: $e');
      return false;
    }
  }

  @override
  Future<GameMatch?> getMatch(String matchId) async {
    try {
      _logger?.info('Getting local match: $matchId');
      // TODO: Implement finding a specific match by ID
      // This would require searching through saved game files
      return null;
    } catch (e) {
      _logger?.error('Error getting local match: $e');
      return null;
    }
  }

  @override
  Future<bool> updateMatch(GameMatch match) async {
    try {
      _logger?.info('Updating local match: ${match.id}');
      // TODO: Implement updating a match file
      return true;
    } catch (e) {
      _logger?.error('Error updating local match: $e');
      return false;
    }
  }
}
