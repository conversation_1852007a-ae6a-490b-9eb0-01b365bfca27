import 'package:common/models/game_match.dart';
import 'package:dauntless/api/dtos/create_match_dto.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/models/base/match_config.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:get_it/get_it.dart';

/// Network-based implementation of MatchRepositoryInterface
/// Handles match operations using server/network communication
class NetworkMatchRepository implements MatchRepositoryInterface {
  final ServerRepository _serverRepository;
  final RemoteLogger? _logger;
  final String? _name;

  NetworkMatchRepository(this._serverRepository, this._logger, [this._name]);

  @override
  String get sourceName => _name ?? 'Network';

  @override
  bool get supportsRealTimeUpdates => true;

  @override
  Future<List<GameMatch>> fetchOpenMatches(String gameName) async {
    try {
      _logger?.info('Fetching open matches for $gameName');
      final matches = await _serverRepository.fetchOpenMatches(gameName);
      _logger?.info('Fetched ${matches.length} open matches');
      return matches;
    } catch (e) {
      _logger?.error('Error fetching open matches: $e');
      return [];
    }
  }

  @override
  Future<GameMatch?> createMatch(GameMatch newMatch, GameConfig gameConfig) async {
    try {
      _logger?.info('Creating network match for game: ${gameConfig.name}');

      // Get current user
      final user = GetIt.I<UserManager>().state.user;
      if (user == null) {
        _logger?.error('No user logged in');
        return null;
      }

      final currentUserId = user.id;
      final gameName = newMatch.gameName;

      // Create a MatchConfig from the GameConfig
      final matchConfig = MatchConfig(
        gameId: gameConfig.id,
        selectedGameMode: GameMode.server,
        hostId: currentUserId,
        matchId: "",
        name: gameName?.isNotEmpty ?? false ? gameName! : gameConfig.name,
      );

      _logger?.info(
        'Creating match: ${matchConfig.name}, mode: ${matchConfig.selectedGameMode}',
      );

      // Create the DTO for the server request
      final createMatchDto = CreateMatchDto(
        gameTypeId: gameConfig.id,
        creatorId: currentUserId,
        gameName: matchConfig.name,
        playerSlots: newMatch.playerSlots,
      );

      // Debug: log the DTO content
      _logger?.info('CreateMatchDto content: ${createMatchDto.toString()}');
      try {
        final jsonData = createMatchDto.toJson();
        _logger?.info('CreateMatchDto JSON: $jsonData');
      } catch (e) {
        _logger?.error('Failed to serialize CreateMatchDto to JSON: $e');
      }

      // Call the repository with the assembled DTO
      final match = await _serverRepository.createMatch(createMatchDto);

      if (match != null) {
        _logger?.info('Match created with ID: ${match.id}');
        return match;
      } else {
        _logger?.error('Failed to create match');
        return null;
      }
    } catch (e) {
      _logger?.error('Error creating network match: $e');
      return null;
    }
  }

  @override
  Future<bool> joinMatch(String matchId, {PlayerId? playerId}) async {
    try {
      _logger?.info('Joining network match: $matchId');
      // ServerRepository.joinMatch requires both matchId and playerId as positional parameters
      if (playerId == null) {
        _logger?.error('PlayerId is required for joining network match');
        return false;
      }
      final success = await _serverRepository.joinMatch(matchId, playerId);
      if (success) {
        _logger?.info('Successfully joined match: $matchId');
      } else {
        _logger?.error('Failed to join match: $matchId');
      }
      return success;
    } catch (e) {
      _logger?.error('Error joining network match: $e');
      return false;
    }
  }

  @override
  Future<bool> leaveMatch(String matchId, {PlayerId? playerId}) async {
    try {
      _logger?.info('Leaving network match: $matchId');
      // ServerRepository.leaveMatch requires both matchId and playerId as positional parameters
      if (playerId == null) {
        _logger?.error('PlayerId is required for leaving network match');
        return false;
      }
      final success = await _serverRepository.leaveMatch(matchId, playerId);
      if (success) {
        _logger?.info('Successfully left match: $matchId');
      } else {
        _logger?.error('Failed to leave match: $matchId');
      }
      return success;
    } catch (e) {
      _logger?.error('Error leaving network match: $e');
      return false;
    }
  }

  @override
  Future<bool> deleteMatch(String matchId) async {
    try {
      _logger?.info('Deleting network match: $matchId');
      final success = await _serverRepository.deleteMatch(matchId);
      if (success) {
        _logger?.info('Successfully deleted match: $matchId');
      } else {
        _logger?.error('Failed to delete match: $matchId');
      }
      return success;
    } catch (e) {
      _logger?.error('Error deleting network match: $e');
      return false;
    }
  }

  @override
  Future<GameMatch?> getMatch(String matchId) async {
    try {
      _logger?.info('Getting network match: $matchId');
      // TODO: ServerRepository doesn't have getMatch method yet
      // This would need to be implemented in the server repository
      _logger?.warn('getMatch not implemented in ServerRepository yet');
      return null;
    } catch (e) {
      _logger?.error('Error getting network match: $e');
      return null;
    }
  }

  @override
  Future<bool> updateMatch(GameMatch match) async {
    try {
      _logger?.info('Updating network match: ${match.id}');
      // TODO: ServerRepository doesn't have updateMatch method yet
      // For now, we can use updateMatchPlayerSlots if only updating player slots
      // This would need to be implemented in the server repository for full match updates
      _logger?.warn('updateMatch not fully implemented in ServerRepository yet');
      return false;
    } catch (e) {
      _logger?.error('Error updating network match: $e');
      return false;
    }
  }
}
