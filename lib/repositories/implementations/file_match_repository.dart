import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;
import 'package:common/models/game_match.dart';
import 'package:dauntless/repositories/interfaces/match_persistence_repository_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';

/// File-based implementation of MatchPersistenceRepositoryInterface
/// Handles saving and loading matches to/from the local file system
class FileMatchRepository implements MatchPersistenceRepositoryInterface {
  final RemoteLogger? _logger;

  FileMatchRepository(this._logger);

  @override
  String get persistenceLayerName => 'File System';

  @override
  Future<bool> isAvailable() async {
    try {
      // Check if we can write to the current directory
      final testFile = File('test_write_permission.tmp');
      await testFile.writeAsString('test');
      await testFile.delete();
      return true;
    } catch (e) {
      _logger?.error('File system not available: $e');
      return false;
    }
  }

  @override
  Future<bool> saveMatch(GameMatch match) async {
    try {
      _logger?.info('Saving match to file: ${match.id}');

      // Create the save directory structure
      final gameName = match.gameName ?? match.gameTypeId;
      final saveDir = Directory(path.join('games', gameName, 'savedGames'));
      
      if (!await saveDir.exists()) {
        await saveDir.create(recursive: true);
      }

      // Create the save file path
      final fileName = '${match.id}.json';
      final filePath = path.join(saveDir.path, fileName);
      final file = File(filePath);

      // Convert match to JSON and save
      final jsonData = match.toJson();
      final jsonString = jsonEncode(jsonData);
      await file.writeAsString(jsonString);

      _logger?.info('Successfully saved match to: $filePath');
      return true;
    } catch (e) {
      _logger?.error('Error saving match to file: $e');
      return false;
    }
  }

  @override
  Future<GameMatch?> loadMatch(String matchId) async {
    try {
      _logger?.info('Loading match from file: $matchId');

      // Search for the match file across all game directories
      final gamesDir = Directory('games');
      if (!await gamesDir.exists()) {
        _logger?.warn('Games directory does not exist');
        return null;
      }

      await for (final gameDir in gamesDir.list()) {
        if (gameDir is Directory) {
          final savedGamesDir = Directory(path.join(gameDir.path, 'savedGames'));
          if (await savedGamesDir.exists()) {
            final matchFile = File(path.join(savedGamesDir.path, '$matchId.json'));
            if (await matchFile.exists()) {
              final jsonContent = await matchFile.readAsString();
              final jsonData = jsonDecode(jsonContent) as Map<String, dynamic>;
              final match = GameMatch.fromJson(jsonData);
              _logger?.info('Successfully loaded match: $matchId');
              return match;
            }
          }
        }
      }

      _logger?.warn('Match file not found: $matchId');
      return null;
    } catch (e) {
      _logger?.error('Error loading match from file: $e');
      return null;
    }
  }

  @override
  Future<List<GameMatch>> loadSavedMatches(String gameName) async {
    try {
      _logger?.info('Loading saved matches for game: $gameName');

      final saveDir = Directory(path.join('games', gameName, 'savedGames'));
      if (!await saveDir.exists()) {
        _logger?.warn('Saved games directory does not exist: ${saveDir.path}');
        return [];
      }

      final matches = <GameMatch>[];

      await for (final entity in saveDir.list()) {
        if (entity is File && entity.path.endsWith('.json')) {
          try {
            final jsonContent = await entity.readAsString();
            final jsonData = jsonDecode(jsonContent) as Map<String, dynamic>;
            final match = GameMatch.fromJson(jsonData);
            matches.add(match);

            final fileName = path.basename(entity.path);
            _logger?.info('Loaded saved match: $fileName');
          } catch (e) {
            _logger?.error('Error parsing saved match file ${entity.path}: $e');
          }
        }
      }

      _logger?.info('Loaded ${matches.length} saved matches for $gameName');
      return matches;
    } catch (e) {
      _logger?.error('Error loading saved matches: $e');
      return [];
    }
  }

  @override
  Future<bool> deleteSavedMatch(String matchId) async {
    try {
      _logger?.info('Deleting saved match: $matchId');

      // Search for the match file across all game directories
      final gamesDir = Directory('games');
      if (!await gamesDir.exists()) {
        return false;
      }

      await for (final gameDir in gamesDir.list()) {
        if (gameDir is Directory) {
          final savedGamesDir = Directory(path.join(gameDir.path, 'savedGames'));
          if (await savedGamesDir.exists()) {
            final matchFile = File(path.join(savedGamesDir.path, '$matchId.json'));
            if (await matchFile.exists()) {
              await matchFile.delete();
              _logger?.info('Successfully deleted match: $matchId');
              return true;
            }
          }
        }
      }

      _logger?.warn('Match file not found for deletion: $matchId');
      return false;
    } catch (e) {
      _logger?.error('Error deleting saved match: $e');
      return false;
    }
  }

  @override
  Future<bool> matchExists(String matchId) async {
    try {
      final match = await loadMatch(matchId);
      return match != null;
    } catch (e) {
      _logger?.error('Error checking if match exists: $e');
      return false;
    }
  }

  @override
  Future<String?> getMatchStoragePath(String matchId) async {
    try {
      // Search for the match file across all game directories
      final gamesDir = Directory('games');
      if (!await gamesDir.exists()) {
        return null;
      }

      await for (final gameDir in gamesDir.list()) {
        if (gameDir is Directory) {
          final savedGamesDir = Directory(path.join(gameDir.path, 'savedGames'));
          if (await savedGamesDir.exists()) {
            final matchFile = File(path.join(savedGamesDir.path, '$matchId.json'));
            if (await matchFile.exists()) {
              return matchFile.path;
            }
          }
        }
      }

      return null;
    } catch (e) {
      _logger?.error('Error getting match storage path: $e');
      return null;
    }
  }

  @override
  Future<bool> clearSavedMatches(String gameName) async {
    try {
      _logger?.info('Clearing saved matches for game: $gameName');

      final saveDir = Directory(path.join('games', gameName, 'savedGames'));
      if (!await saveDir.exists()) {
        return true; // Nothing to clear
      }

      await for (final entity in saveDir.list()) {
        if (entity is File && entity.path.endsWith('.json')) {
          await entity.delete();
        }
      }

      _logger?.info('Successfully cleared saved matches for: $gameName');
      return true;
    } catch (e) {
      _logger?.error('Error clearing saved matches: $e');
      return false;
    }
  }
}
