import 'package:common/models/game_match.dart';

/// Interface for match persistence operations
/// Handles saving and loading matches to/from persistent storage
abstract class MatchPersistenceRepositoryInterface {
  /// Save a match to persistent storage
  Future<bool> saveMatch(GameMatch match);

  /// Load a match from persistent storage
  Future<GameMatch?> loadMatch(String matchId);

  /// Load all saved matches for a specific game
  Future<List<GameMatch>> loadSavedMatches(String gameName);

  /// Delete a saved match
  Future<bool> deleteSavedMatch(String matchId);

  /// Check if a match exists in persistent storage
  Future<bool> matchExists(String matchId);

  /// Get the storage path or identifier for a match
  Future<String?> getMatchStoragePath(String matchId);

  /// Clear all saved matches for a specific game
  Future<bool> clearSavedMatches(String gameName);

  /// Get the name/identifier of this persistence layer
  String get persistenceLayerName;

  /// Check if this persistence layer is available
  Future<bool> isAvailable();
}
