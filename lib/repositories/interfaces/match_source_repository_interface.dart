import 'dart:async';
import 'package:common/models/game_match.dart';

/// Interface for match discovery and listing operations
/// Handles finding and monitoring available matches from various sources
abstract class MatchSourceRepositoryInterface {
  /// Stream of match updates for real-time monitoring
  Stream<List<GameMatch>> get matchUpdates;

  /// Discover available matches from this source
  Future<List<GameMatch>> discoverMatches(String gameName);

  /// Start monitoring for match updates
  Future<void> startMonitoring(String gameName);

  /// Stop monitoring for match updates
  Future<void> stopMonitoring();

  /// Check if this source is currently available/connected
  Future<bool> isAvailable();

  /// Get the priority of this source (higher = more preferred)
  int get priority;

  /// Get the name/identifier of this source
  String get sourceName;

  /// Check if this source supports real-time monitoring
  bool get supportsRealTimeMonitoring;
}
