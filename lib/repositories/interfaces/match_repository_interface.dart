import 'package:common/models/game_match.dart';
import 'package:dauntless/models/base/game_config.dart';

// Type alias for PlayerId to match the codebase conventions
typedef PlayerId = String;

/// Interface for core match operations
/// Provides abstraction for match CRUD operations regardless of data source
abstract class MatchRepositoryInterface {
  /// Fetch all open matches for a specific game
  Future<List<GameMatch>> fetchOpenMatches(String gameName);

  /// Create a new match
  Future<GameMatch?> createMatch(GameMatch newMatch, GameConfig gameConfig);

  /// Join an existing match
  Future<bool> joinMatch(String matchId, {PlayerId? playerId});

  /// Leave a match
  Future<bool> leaveMatch(String matchId, {PlayerId? playerId});

  /// Delete a match
  Future<bool> deleteMatch(String matchId);

  /// Get a specific match by ID
  Future<GameMatch?> getMatch(String matchId);

  /// Update match status or configuration
  Future<bool> updateMatch(GameMatch match);

  /// Check if this repository supports real-time updates
  bool get supportsRealTimeUpdates;

  /// Get the name/identifier of this repository source
  String get sourceName;
}
