// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'match_management_requests.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$CreateMatchRequest {
  GameConfig get gameConfig;
  List<PlayerSlot> get playerSlots;
  String get gameName;
  bool get openForJoining;

  /// Create a copy of CreateMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CreateMatchRequestCopyWith<CreateMatchRequest> get copyWith =>
      _$CreateMatchRequestCopyWithImpl<CreateMatchRequest>(
          this as CreateMatchRequest, _$identity);

  /// Serializes this CreateMatchRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CreateMatchRequest &&
            (identical(other.gameConfig, gameConfig) ||
                other.gameConfig == gameConfig) &&
            const DeepCollectionEquality()
                .equals(other.playerSlots, playerSlots) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.openForJoining, openForJoining) ||
                other.openForJoining == openForJoining));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      gameConfig,
      const DeepCollectionEquality().hash(playerSlots),
      gameName,
      openForJoining);

  @override
  String toString() {
    return 'CreateMatchRequest(gameConfig: $gameConfig, playerSlots: $playerSlots, gameName: $gameName, openForJoining: $openForJoining)';
  }
}

/// @nodoc
abstract mixin class $CreateMatchRequestCopyWith<$Res> {
  factory $CreateMatchRequestCopyWith(
          CreateMatchRequest value, $Res Function(CreateMatchRequest) _then) =
      _$CreateMatchRequestCopyWithImpl;
  @useResult
  $Res call(
      {GameConfig gameConfig,
      List<PlayerSlot> playerSlots,
      String gameName,
      bool openForJoining});

  $GameConfigCopyWith<$Res> get gameConfig;
}

/// @nodoc
class _$CreateMatchRequestCopyWithImpl<$Res>
    implements $CreateMatchRequestCopyWith<$Res> {
  _$CreateMatchRequestCopyWithImpl(this._self, this._then);

  final CreateMatchRequest _self;
  final $Res Function(CreateMatchRequest) _then;

  /// Create a copy of CreateMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gameConfig = null,
    Object? playerSlots = null,
    Object? gameName = null,
    Object? openForJoining = null,
  }) {
    return _then(_self.copyWith(
      gameConfig: null == gameConfig
          ? _self.gameConfig
          : gameConfig // ignore: cast_nullable_to_non_nullable
              as GameConfig,
      playerSlots: null == playerSlots
          ? _self.playerSlots
          : playerSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      gameName: null == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String,
      openForJoining: null == openForJoining
          ? _self.openForJoining
          : openForJoining // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of CreateMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameConfigCopyWith<$Res> get gameConfig {
    return $GameConfigCopyWith<$Res>(_self.gameConfig, (value) {
      return _then(_self.copyWith(gameConfig: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _CreateMatchRequest implements CreateMatchRequest {
  const _CreateMatchRequest(
      {required this.gameConfig,
      required final List<PlayerSlot> playerSlots,
      required this.gameName,
      this.openForJoining = true})
      : _playerSlots = playerSlots;
  factory _CreateMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateMatchRequestFromJson(json);

  @override
  final GameConfig gameConfig;
  final List<PlayerSlot> _playerSlots;
  @override
  List<PlayerSlot> get playerSlots {
    if (_playerSlots is EqualUnmodifiableListView) return _playerSlots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_playerSlots);
  }

  @override
  final String gameName;
  @override
  @JsonKey()
  final bool openForJoining;

  /// Create a copy of CreateMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CreateMatchRequestCopyWith<_CreateMatchRequest> get copyWith =>
      __$CreateMatchRequestCopyWithImpl<_CreateMatchRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CreateMatchRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CreateMatchRequest &&
            (identical(other.gameConfig, gameConfig) ||
                other.gameConfig == gameConfig) &&
            const DeepCollectionEquality()
                .equals(other._playerSlots, _playerSlots) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.openForJoining, openForJoining) ||
                other.openForJoining == openForJoining));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      gameConfig,
      const DeepCollectionEquality().hash(_playerSlots),
      gameName,
      openForJoining);

  @override
  String toString() {
    return 'CreateMatchRequest(gameConfig: $gameConfig, playerSlots: $playerSlots, gameName: $gameName, openForJoining: $openForJoining)';
  }
}

/// @nodoc
abstract mixin class _$CreateMatchRequestCopyWith<$Res>
    implements $CreateMatchRequestCopyWith<$Res> {
  factory _$CreateMatchRequestCopyWith(
          _CreateMatchRequest value, $Res Function(_CreateMatchRequest) _then) =
      __$CreateMatchRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {GameConfig gameConfig,
      List<PlayerSlot> playerSlots,
      String gameName,
      bool openForJoining});

  @override
  $GameConfigCopyWith<$Res> get gameConfig;
}

/// @nodoc
class __$CreateMatchRequestCopyWithImpl<$Res>
    implements _$CreateMatchRequestCopyWith<$Res> {
  __$CreateMatchRequestCopyWithImpl(this._self, this._then);

  final _CreateMatchRequest _self;
  final $Res Function(_CreateMatchRequest) _then;

  /// Create a copy of CreateMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? gameConfig = null,
    Object? playerSlots = null,
    Object? gameName = null,
    Object? openForJoining = null,
  }) {
    return _then(_CreateMatchRequest(
      gameConfig: null == gameConfig
          ? _self.gameConfig
          : gameConfig // ignore: cast_nullable_to_non_nullable
              as GameConfig,
      playerSlots: null == playerSlots
          ? _self._playerSlots
          : playerSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      gameName: null == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String,
      openForJoining: null == openForJoining
          ? _self.openForJoining
          : openForJoining // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of CreateMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameConfigCopyWith<$Res> get gameConfig {
    return $GameConfigCopyWith<$Res>(_self.gameConfig, (value) {
      return _then(_self.copyWith(gameConfig: value));
    });
  }
}

/// @nodoc
mixin _$UpdatePlayerTypeRequest {
  int get slotIndex;
  PlayerType get newType;
  List<PlayerSlot> get currentSlots;
  GameConfig get gameConfig;
  String? get matchId;
  bool get isSelectedMatch;

  /// Create a copy of UpdatePlayerTypeRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $UpdatePlayerTypeRequestCopyWith<UpdatePlayerTypeRequest> get copyWith =>
      _$UpdatePlayerTypeRequestCopyWithImpl<UpdatePlayerTypeRequest>(
          this as UpdatePlayerTypeRequest, _$identity);

  /// Serializes this UpdatePlayerTypeRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UpdatePlayerTypeRequest &&
            (identical(other.slotIndex, slotIndex) ||
                other.slotIndex == slotIndex) &&
            (identical(other.newType, newType) || other.newType == newType) &&
            const DeepCollectionEquality()
                .equals(other.currentSlots, currentSlots) &&
            (identical(other.gameConfig, gameConfig) ||
                other.gameConfig == gameConfig) &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.isSelectedMatch, isSelectedMatch) ||
                other.isSelectedMatch == isSelectedMatch));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      slotIndex,
      newType,
      const DeepCollectionEquality().hash(currentSlots),
      gameConfig,
      matchId,
      isSelectedMatch);

  @override
  String toString() {
    return 'UpdatePlayerTypeRequest(slotIndex: $slotIndex, newType: $newType, currentSlots: $currentSlots, gameConfig: $gameConfig, matchId: $matchId, isSelectedMatch: $isSelectedMatch)';
  }
}

/// @nodoc
abstract mixin class $UpdatePlayerTypeRequestCopyWith<$Res> {
  factory $UpdatePlayerTypeRequestCopyWith(UpdatePlayerTypeRequest value,
          $Res Function(UpdatePlayerTypeRequest) _then) =
      _$UpdatePlayerTypeRequestCopyWithImpl;
  @useResult
  $Res call(
      {int slotIndex,
      PlayerType newType,
      List<PlayerSlot> currentSlots,
      GameConfig gameConfig,
      String? matchId,
      bool isSelectedMatch});

  $GameConfigCopyWith<$Res> get gameConfig;
}

/// @nodoc
class _$UpdatePlayerTypeRequestCopyWithImpl<$Res>
    implements $UpdatePlayerTypeRequestCopyWith<$Res> {
  _$UpdatePlayerTypeRequestCopyWithImpl(this._self, this._then);

  final UpdatePlayerTypeRequest _self;
  final $Res Function(UpdatePlayerTypeRequest) _then;

  /// Create a copy of UpdatePlayerTypeRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? slotIndex = null,
    Object? newType = null,
    Object? currentSlots = null,
    Object? gameConfig = null,
    Object? matchId = freezed,
    Object? isSelectedMatch = null,
  }) {
    return _then(_self.copyWith(
      slotIndex: null == slotIndex
          ? _self.slotIndex
          : slotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      newType: null == newType
          ? _self.newType
          : newType // ignore: cast_nullable_to_non_nullable
              as PlayerType,
      currentSlots: null == currentSlots
          ? _self.currentSlots
          : currentSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      gameConfig: null == gameConfig
          ? _self.gameConfig
          : gameConfig // ignore: cast_nullable_to_non_nullable
              as GameConfig,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
      isSelectedMatch: null == isSelectedMatch
          ? _self.isSelectedMatch
          : isSelectedMatch // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of UpdatePlayerTypeRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameConfigCopyWith<$Res> get gameConfig {
    return $GameConfigCopyWith<$Res>(_self.gameConfig, (value) {
      return _then(_self.copyWith(gameConfig: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _UpdatePlayerTypeRequest implements UpdatePlayerTypeRequest {
  const _UpdatePlayerTypeRequest(
      {required this.slotIndex,
      required this.newType,
      required final List<PlayerSlot> currentSlots,
      required this.gameConfig,
      this.matchId,
      this.isSelectedMatch = false})
      : _currentSlots = currentSlots;
  factory _UpdatePlayerTypeRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdatePlayerTypeRequestFromJson(json);

  @override
  final int slotIndex;
  @override
  final PlayerType newType;
  final List<PlayerSlot> _currentSlots;
  @override
  List<PlayerSlot> get currentSlots {
    if (_currentSlots is EqualUnmodifiableListView) return _currentSlots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_currentSlots);
  }

  @override
  final GameConfig gameConfig;
  @override
  final String? matchId;
  @override
  @JsonKey()
  final bool isSelectedMatch;

  /// Create a copy of UpdatePlayerTypeRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$UpdatePlayerTypeRequestCopyWith<_UpdatePlayerTypeRequest> get copyWith =>
      __$UpdatePlayerTypeRequestCopyWithImpl<_UpdatePlayerTypeRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$UpdatePlayerTypeRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UpdatePlayerTypeRequest &&
            (identical(other.slotIndex, slotIndex) ||
                other.slotIndex == slotIndex) &&
            (identical(other.newType, newType) || other.newType == newType) &&
            const DeepCollectionEquality()
                .equals(other._currentSlots, _currentSlots) &&
            (identical(other.gameConfig, gameConfig) ||
                other.gameConfig == gameConfig) &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.isSelectedMatch, isSelectedMatch) ||
                other.isSelectedMatch == isSelectedMatch));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      slotIndex,
      newType,
      const DeepCollectionEquality().hash(_currentSlots),
      gameConfig,
      matchId,
      isSelectedMatch);

  @override
  String toString() {
    return 'UpdatePlayerTypeRequest(slotIndex: $slotIndex, newType: $newType, currentSlots: $currentSlots, gameConfig: $gameConfig, matchId: $matchId, isSelectedMatch: $isSelectedMatch)';
  }
}

/// @nodoc
abstract mixin class _$UpdatePlayerTypeRequestCopyWith<$Res>
    implements $UpdatePlayerTypeRequestCopyWith<$Res> {
  factory _$UpdatePlayerTypeRequestCopyWith(_UpdatePlayerTypeRequest value,
          $Res Function(_UpdatePlayerTypeRequest) _then) =
      __$UpdatePlayerTypeRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int slotIndex,
      PlayerType newType,
      List<PlayerSlot> currentSlots,
      GameConfig gameConfig,
      String? matchId,
      bool isSelectedMatch});

  @override
  $GameConfigCopyWith<$Res> get gameConfig;
}

/// @nodoc
class __$UpdatePlayerTypeRequestCopyWithImpl<$Res>
    implements _$UpdatePlayerTypeRequestCopyWith<$Res> {
  __$UpdatePlayerTypeRequestCopyWithImpl(this._self, this._then);

  final _UpdatePlayerTypeRequest _self;
  final $Res Function(_UpdatePlayerTypeRequest) _then;

  /// Create a copy of UpdatePlayerTypeRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? slotIndex = null,
    Object? newType = null,
    Object? currentSlots = null,
    Object? gameConfig = null,
    Object? matchId = freezed,
    Object? isSelectedMatch = null,
  }) {
    return _then(_UpdatePlayerTypeRequest(
      slotIndex: null == slotIndex
          ? _self.slotIndex
          : slotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      newType: null == newType
          ? _self.newType
          : newType // ignore: cast_nullable_to_non_nullable
              as PlayerType,
      currentSlots: null == currentSlots
          ? _self._currentSlots
          : currentSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      gameConfig: null == gameConfig
          ? _self.gameConfig
          : gameConfig // ignore: cast_nullable_to_non_nullable
              as GameConfig,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
      isSelectedMatch: null == isSelectedMatch
          ? _self.isSelectedMatch
          : isSelectedMatch // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }

  /// Create a copy of UpdatePlayerTypeRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameConfigCopyWith<$Res> get gameConfig {
    return $GameConfigCopyWith<$Res>(_self.gameConfig, (value) {
      return _then(_self.copyWith(gameConfig: value));
    });
  }
}

/// @nodoc
mixin _$JoinSlotRequest {
  int get slotIndex;
  List<PlayerSlot> get currentSlots;
  String? get playerId;
  String? get matchId;

  /// Create a copy of JoinSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $JoinSlotRequestCopyWith<JoinSlotRequest> get copyWith =>
      _$JoinSlotRequestCopyWithImpl<JoinSlotRequest>(
          this as JoinSlotRequest, _$identity);

  /// Serializes this JoinSlotRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is JoinSlotRequest &&
            (identical(other.slotIndex, slotIndex) ||
                other.slotIndex == slotIndex) &&
            const DeepCollectionEquality()
                .equals(other.currentSlots, currentSlots) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            (identical(other.matchId, matchId) || other.matchId == matchId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, slotIndex,
      const DeepCollectionEquality().hash(currentSlots), playerId, matchId);

  @override
  String toString() {
    return 'JoinSlotRequest(slotIndex: $slotIndex, currentSlots: $currentSlots, playerId: $playerId, matchId: $matchId)';
  }
}

/// @nodoc
abstract mixin class $JoinSlotRequestCopyWith<$Res> {
  factory $JoinSlotRequestCopyWith(
          JoinSlotRequest value, $Res Function(JoinSlotRequest) _then) =
      _$JoinSlotRequestCopyWithImpl;
  @useResult
  $Res call(
      {int slotIndex,
      List<PlayerSlot> currentSlots,
      String? playerId,
      String? matchId});
}

/// @nodoc
class _$JoinSlotRequestCopyWithImpl<$Res>
    implements $JoinSlotRequestCopyWith<$Res> {
  _$JoinSlotRequestCopyWithImpl(this._self, this._then);

  final JoinSlotRequest _self;
  final $Res Function(JoinSlotRequest) _then;

  /// Create a copy of JoinSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? slotIndex = null,
    Object? currentSlots = null,
    Object? playerId = freezed,
    Object? matchId = freezed,
  }) {
    return _then(_self.copyWith(
      slotIndex: null == slotIndex
          ? _self.slotIndex
          : slotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      currentSlots: null == currentSlots
          ? _self.currentSlots
          : currentSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _JoinSlotRequest implements JoinSlotRequest {
  const _JoinSlotRequest(
      {required this.slotIndex,
      required final List<PlayerSlot> currentSlots,
      this.playerId,
      this.matchId})
      : _currentSlots = currentSlots;
  factory _JoinSlotRequest.fromJson(Map<String, dynamic> json) =>
      _$JoinSlotRequestFromJson(json);

  @override
  final int slotIndex;
  final List<PlayerSlot> _currentSlots;
  @override
  List<PlayerSlot> get currentSlots {
    if (_currentSlots is EqualUnmodifiableListView) return _currentSlots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_currentSlots);
  }

  @override
  final String? playerId;
  @override
  final String? matchId;

  /// Create a copy of JoinSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$JoinSlotRequestCopyWith<_JoinSlotRequest> get copyWith =>
      __$JoinSlotRequestCopyWithImpl<_JoinSlotRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$JoinSlotRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _JoinSlotRequest &&
            (identical(other.slotIndex, slotIndex) ||
                other.slotIndex == slotIndex) &&
            const DeepCollectionEquality()
                .equals(other._currentSlots, _currentSlots) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            (identical(other.matchId, matchId) || other.matchId == matchId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, slotIndex,
      const DeepCollectionEquality().hash(_currentSlots), playerId, matchId);

  @override
  String toString() {
    return 'JoinSlotRequest(slotIndex: $slotIndex, currentSlots: $currentSlots, playerId: $playerId, matchId: $matchId)';
  }
}

/// @nodoc
abstract mixin class _$JoinSlotRequestCopyWith<$Res>
    implements $JoinSlotRequestCopyWith<$Res> {
  factory _$JoinSlotRequestCopyWith(
          _JoinSlotRequest value, $Res Function(_JoinSlotRequest) _then) =
      __$JoinSlotRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {int slotIndex,
      List<PlayerSlot> currentSlots,
      String? playerId,
      String? matchId});
}

/// @nodoc
class __$JoinSlotRequestCopyWithImpl<$Res>
    implements _$JoinSlotRequestCopyWith<$Res> {
  __$JoinSlotRequestCopyWithImpl(this._self, this._then);

  final _JoinSlotRequest _self;
  final $Res Function(_JoinSlotRequest) _then;

  /// Create a copy of JoinSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? slotIndex = null,
    Object? currentSlots = null,
    Object? playerId = freezed,
    Object? matchId = freezed,
  }) {
    return _then(_JoinSlotRequest(
      slotIndex: null == slotIndex
          ? _self.slotIndex
          : slotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      currentSlots: null == currentSlots
          ? _self._currentSlots
          : currentSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$LoadMatchDataRequest {
  String? get gameName;
  bool get forceRefresh;
  Set<String>? get specificSources;

  /// Create a copy of LoadMatchDataRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LoadMatchDataRequestCopyWith<LoadMatchDataRequest> get copyWith =>
      _$LoadMatchDataRequestCopyWithImpl<LoadMatchDataRequest>(
          this as LoadMatchDataRequest, _$identity);

  /// Serializes this LoadMatchDataRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LoadMatchDataRequest &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.forceRefresh, forceRefresh) ||
                other.forceRefresh == forceRefresh) &&
            const DeepCollectionEquality()
                .equals(other.specificSources, specificSources));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, gameName, forceRefresh,
      const DeepCollectionEquality().hash(specificSources));

  @override
  String toString() {
    return 'LoadMatchDataRequest(gameName: $gameName, forceRefresh: $forceRefresh, specificSources: $specificSources)';
  }
}

/// @nodoc
abstract mixin class $LoadMatchDataRequestCopyWith<$Res> {
  factory $LoadMatchDataRequestCopyWith(LoadMatchDataRequest value,
          $Res Function(LoadMatchDataRequest) _then) =
      _$LoadMatchDataRequestCopyWithImpl;
  @useResult
  $Res call(
      {String? gameName, bool forceRefresh, Set<String>? specificSources});
}

/// @nodoc
class _$LoadMatchDataRequestCopyWithImpl<$Res>
    implements $LoadMatchDataRequestCopyWith<$Res> {
  _$LoadMatchDataRequestCopyWithImpl(this._self, this._then);

  final LoadMatchDataRequest _self;
  final $Res Function(LoadMatchDataRequest) _then;

  /// Create a copy of LoadMatchDataRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gameName = freezed,
    Object? forceRefresh = null,
    Object? specificSources = freezed,
  }) {
    return _then(_self.copyWith(
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
      forceRefresh: null == forceRefresh
          ? _self.forceRefresh
          : forceRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
      specificSources: freezed == specificSources
          ? _self.specificSources
          : specificSources // ignore: cast_nullable_to_non_nullable
              as Set<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _LoadMatchDataRequest implements LoadMatchDataRequest {
  const _LoadMatchDataRequest(
      {this.gameName,
      this.forceRefresh = false,
      final Set<String>? specificSources})
      : _specificSources = specificSources;
  factory _LoadMatchDataRequest.fromJson(Map<String, dynamic> json) =>
      _$LoadMatchDataRequestFromJson(json);

  @override
  final String? gameName;
  @override
  @JsonKey()
  final bool forceRefresh;
  final Set<String>? _specificSources;
  @override
  Set<String>? get specificSources {
    final value = _specificSources;
    if (value == null) return null;
    if (_specificSources is EqualUnmodifiableSetView) return _specificSources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(value);
  }

  /// Create a copy of LoadMatchDataRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LoadMatchDataRequestCopyWith<_LoadMatchDataRequest> get copyWith =>
      __$LoadMatchDataRequestCopyWithImpl<_LoadMatchDataRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LoadMatchDataRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LoadMatchDataRequest &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.forceRefresh, forceRefresh) ||
                other.forceRefresh == forceRefresh) &&
            const DeepCollectionEquality()
                .equals(other._specificSources, _specificSources));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, gameName, forceRefresh,
      const DeepCollectionEquality().hash(_specificSources));

  @override
  String toString() {
    return 'LoadMatchDataRequest(gameName: $gameName, forceRefresh: $forceRefresh, specificSources: $specificSources)';
  }
}

/// @nodoc
abstract mixin class _$LoadMatchDataRequestCopyWith<$Res>
    implements $LoadMatchDataRequestCopyWith<$Res> {
  factory _$LoadMatchDataRequestCopyWith(_LoadMatchDataRequest value,
          $Res Function(_LoadMatchDataRequest) _then) =
      __$LoadMatchDataRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String? gameName, bool forceRefresh, Set<String>? specificSources});
}

/// @nodoc
class __$LoadMatchDataRequestCopyWithImpl<$Res>
    implements _$LoadMatchDataRequestCopyWith<$Res> {
  __$LoadMatchDataRequestCopyWithImpl(this._self, this._then);

  final _LoadMatchDataRequest _self;
  final $Res Function(_LoadMatchDataRequest) _then;

  /// Create a copy of LoadMatchDataRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? gameName = freezed,
    Object? forceRefresh = null,
    Object? specificSources = freezed,
  }) {
    return _then(_LoadMatchDataRequest(
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
      forceRefresh: null == forceRefresh
          ? _self.forceRefresh
          : forceRefresh // ignore: cast_nullable_to_non_nullable
              as bool,
      specificSources: freezed == specificSources
          ? _self._specificSources
          : specificSources // ignore: cast_nullable_to_non_nullable
              as Set<String>?,
    ));
  }
}

/// @nodoc
mixin _$RefreshMatchesRequest {
  String get sourceName;
  String? get gameName;

  /// Create a copy of RefreshMatchesRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RefreshMatchesRequestCopyWith<RefreshMatchesRequest> get copyWith =>
      _$RefreshMatchesRequestCopyWithImpl<RefreshMatchesRequest>(
          this as RefreshMatchesRequest, _$identity);

  /// Serializes this RefreshMatchesRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RefreshMatchesRequest &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sourceName, gameName);

  @override
  String toString() {
    return 'RefreshMatchesRequest(sourceName: $sourceName, gameName: $gameName)';
  }
}

/// @nodoc
abstract mixin class $RefreshMatchesRequestCopyWith<$Res> {
  factory $RefreshMatchesRequestCopyWith(RefreshMatchesRequest value,
          $Res Function(RefreshMatchesRequest) _then) =
      _$RefreshMatchesRequestCopyWithImpl;
  @useResult
  $Res call({String sourceName, String? gameName});
}

/// @nodoc
class _$RefreshMatchesRequestCopyWithImpl<$Res>
    implements $RefreshMatchesRequestCopyWith<$Res> {
  _$RefreshMatchesRequestCopyWithImpl(this._self, this._then);

  final RefreshMatchesRequest _self;
  final $Res Function(RefreshMatchesRequest) _then;

  /// Create a copy of RefreshMatchesRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sourceName = null,
    Object? gameName = freezed,
  }) {
    return _then(_self.copyWith(
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _RefreshMatchesRequest implements RefreshMatchesRequest {
  const _RefreshMatchesRequest({required this.sourceName, this.gameName});
  factory _RefreshMatchesRequest.fromJson(Map<String, dynamic> json) =>
      _$RefreshMatchesRequestFromJson(json);

  @override
  final String sourceName;
  @override
  final String? gameName;

  /// Create a copy of RefreshMatchesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RefreshMatchesRequestCopyWith<_RefreshMatchesRequest> get copyWith =>
      __$RefreshMatchesRequestCopyWithImpl<_RefreshMatchesRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RefreshMatchesRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RefreshMatchesRequest &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sourceName, gameName);

  @override
  String toString() {
    return 'RefreshMatchesRequest(sourceName: $sourceName, gameName: $gameName)';
  }
}

/// @nodoc
abstract mixin class _$RefreshMatchesRequestCopyWith<$Res>
    implements $RefreshMatchesRequestCopyWith<$Res> {
  factory _$RefreshMatchesRequestCopyWith(_RefreshMatchesRequest value,
          $Res Function(_RefreshMatchesRequest) _then) =
      __$RefreshMatchesRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String sourceName, String? gameName});
}

/// @nodoc
class __$RefreshMatchesRequestCopyWithImpl<$Res>
    implements _$RefreshMatchesRequestCopyWith<$Res> {
  __$RefreshMatchesRequestCopyWithImpl(this._self, this._then);

  final _RefreshMatchesRequest _self;
  final $Res Function(_RefreshMatchesRequest) _then;

  /// Create a copy of RefreshMatchesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? sourceName = null,
    Object? gameName = freezed,
  }) {
    return _then(_RefreshMatchesRequest(
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$JoinMatchRequest {
  String get matchId;
  String? get playerId;

  /// Create a copy of JoinMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $JoinMatchRequestCopyWith<JoinMatchRequest> get copyWith =>
      _$JoinMatchRequestCopyWithImpl<JoinMatchRequest>(
          this as JoinMatchRequest, _$identity);

  /// Serializes this JoinMatchRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is JoinMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, playerId);

  @override
  String toString() {
    return 'JoinMatchRequest(matchId: $matchId, playerId: $playerId)';
  }
}

/// @nodoc
abstract mixin class $JoinMatchRequestCopyWith<$Res> {
  factory $JoinMatchRequestCopyWith(
          JoinMatchRequest value, $Res Function(JoinMatchRequest) _then) =
      _$JoinMatchRequestCopyWithImpl;
  @useResult
  $Res call({String matchId, String? playerId});
}

/// @nodoc
class _$JoinMatchRequestCopyWithImpl<$Res>
    implements $JoinMatchRequestCopyWith<$Res> {
  _$JoinMatchRequestCopyWithImpl(this._self, this._then);

  final JoinMatchRequest _self;
  final $Res Function(JoinMatchRequest) _then;

  /// Create a copy of JoinMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchId = null,
    Object? playerId = freezed,
  }) {
    return _then(_self.copyWith(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _JoinMatchRequest implements JoinMatchRequest {
  const _JoinMatchRequest({required this.matchId, this.playerId});
  factory _JoinMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$JoinMatchRequestFromJson(json);

  @override
  final String matchId;
  @override
  final String? playerId;

  /// Create a copy of JoinMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$JoinMatchRequestCopyWith<_JoinMatchRequest> get copyWith =>
      __$JoinMatchRequestCopyWithImpl<_JoinMatchRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$JoinMatchRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _JoinMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, playerId);

  @override
  String toString() {
    return 'JoinMatchRequest(matchId: $matchId, playerId: $playerId)';
  }
}

/// @nodoc
abstract mixin class _$JoinMatchRequestCopyWith<$Res>
    implements $JoinMatchRequestCopyWith<$Res> {
  factory _$JoinMatchRequestCopyWith(
          _JoinMatchRequest value, $Res Function(_JoinMatchRequest) _then) =
      __$JoinMatchRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String matchId, String? playerId});
}

/// @nodoc
class __$JoinMatchRequestCopyWithImpl<$Res>
    implements _$JoinMatchRequestCopyWith<$Res> {
  __$JoinMatchRequestCopyWithImpl(this._self, this._then);

  final _JoinMatchRequest _self;
  final $Res Function(_JoinMatchRequest) _then;

  /// Create a copy of JoinMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchId = null,
    Object? playerId = freezed,
  }) {
    return _then(_JoinMatchRequest(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$LeaveMatchRequest {
  String get matchId;
  String? get playerId;

  /// Create a copy of LeaveMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $LeaveMatchRequestCopyWith<LeaveMatchRequest> get copyWith =>
      _$LeaveMatchRequestCopyWithImpl<LeaveMatchRequest>(
          this as LeaveMatchRequest, _$identity);

  /// Serializes this LeaveMatchRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is LeaveMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, playerId);

  @override
  String toString() {
    return 'LeaveMatchRequest(matchId: $matchId, playerId: $playerId)';
  }
}

/// @nodoc
abstract mixin class $LeaveMatchRequestCopyWith<$Res> {
  factory $LeaveMatchRequestCopyWith(
          LeaveMatchRequest value, $Res Function(LeaveMatchRequest) _then) =
      _$LeaveMatchRequestCopyWithImpl;
  @useResult
  $Res call({String matchId, String? playerId});
}

/// @nodoc
class _$LeaveMatchRequestCopyWithImpl<$Res>
    implements $LeaveMatchRequestCopyWith<$Res> {
  _$LeaveMatchRequestCopyWithImpl(this._self, this._then);

  final LeaveMatchRequest _self;
  final $Res Function(LeaveMatchRequest) _then;

  /// Create a copy of LeaveMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchId = null,
    Object? playerId = freezed,
  }) {
    return _then(_self.copyWith(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _LeaveMatchRequest implements LeaveMatchRequest {
  const _LeaveMatchRequest({required this.matchId, this.playerId});
  factory _LeaveMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$LeaveMatchRequestFromJson(json);

  @override
  final String matchId;
  @override
  final String? playerId;

  /// Create a copy of LeaveMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$LeaveMatchRequestCopyWith<_LeaveMatchRequest> get copyWith =>
      __$LeaveMatchRequestCopyWithImpl<_LeaveMatchRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$LeaveMatchRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _LeaveMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, playerId);

  @override
  String toString() {
    return 'LeaveMatchRequest(matchId: $matchId, playerId: $playerId)';
  }
}

/// @nodoc
abstract mixin class _$LeaveMatchRequestCopyWith<$Res>
    implements $LeaveMatchRequestCopyWith<$Res> {
  factory _$LeaveMatchRequestCopyWith(
          _LeaveMatchRequest value, $Res Function(_LeaveMatchRequest) _then) =
      __$LeaveMatchRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String matchId, String? playerId});
}

/// @nodoc
class __$LeaveMatchRequestCopyWithImpl<$Res>
    implements _$LeaveMatchRequestCopyWith<$Res> {
  __$LeaveMatchRequestCopyWithImpl(this._self, this._then);

  final _LeaveMatchRequest _self;
  final $Res Function(_LeaveMatchRequest) _then;

  /// Create a copy of LeaveMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchId = null,
    Object? playerId = freezed,
  }) {
    return _then(_LeaveMatchRequest(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      playerId: freezed == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$DeleteMatchRequest {
  String get matchId;

  /// Create a copy of DeleteMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $DeleteMatchRequestCopyWith<DeleteMatchRequest> get copyWith =>
      _$DeleteMatchRequestCopyWithImpl<DeleteMatchRequest>(
          this as DeleteMatchRequest, _$identity);

  /// Serializes this DeleteMatchRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is DeleteMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId);

  @override
  String toString() {
    return 'DeleteMatchRequest(matchId: $matchId)';
  }
}

/// @nodoc
abstract mixin class $DeleteMatchRequestCopyWith<$Res> {
  factory $DeleteMatchRequestCopyWith(
          DeleteMatchRequest value, $Res Function(DeleteMatchRequest) _then) =
      _$DeleteMatchRequestCopyWithImpl;
  @useResult
  $Res call({String matchId});
}

/// @nodoc
class _$DeleteMatchRequestCopyWithImpl<$Res>
    implements $DeleteMatchRequestCopyWith<$Res> {
  _$DeleteMatchRequestCopyWithImpl(this._self, this._then);

  final DeleteMatchRequest _self;
  final $Res Function(DeleteMatchRequest) _then;

  /// Create a copy of DeleteMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchId = null,
  }) {
    return _then(_self.copyWith(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _DeleteMatchRequest implements DeleteMatchRequest {
  const _DeleteMatchRequest({required this.matchId});
  factory _DeleteMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$DeleteMatchRequestFromJson(json);

  @override
  final String matchId;

  /// Create a copy of DeleteMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$DeleteMatchRequestCopyWith<_DeleteMatchRequest> get copyWith =>
      __$DeleteMatchRequestCopyWithImpl<_DeleteMatchRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$DeleteMatchRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _DeleteMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId);

  @override
  String toString() {
    return 'DeleteMatchRequest(matchId: $matchId)';
  }
}

/// @nodoc
abstract mixin class _$DeleteMatchRequestCopyWith<$Res>
    implements $DeleteMatchRequestCopyWith<$Res> {
  factory _$DeleteMatchRequestCopyWith(
          _DeleteMatchRequest value, $Res Function(_DeleteMatchRequest) _then) =
      __$DeleteMatchRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String matchId});
}

/// @nodoc
class __$DeleteMatchRequestCopyWithImpl<$Res>
    implements _$DeleteMatchRequestCopyWith<$Res> {
  __$DeleteMatchRequestCopyWithImpl(this._self, this._then);

  final _DeleteMatchRequest _self;
  final $Res Function(_DeleteMatchRequest) _then;

  /// Create a copy of DeleteMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchId = null,
  }) {
    return _then(_DeleteMatchRequest(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$SubscribeToRealTimeUpdatesRequest {
  String? get gameName;

  /// Create a copy of SubscribeToRealTimeUpdatesRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SubscribeToRealTimeUpdatesRequestCopyWith<SubscribeToRealTimeUpdatesRequest>
      get copyWith => _$SubscribeToRealTimeUpdatesRequestCopyWithImpl<
              SubscribeToRealTimeUpdatesRequest>(
          this as SubscribeToRealTimeUpdatesRequest, _$identity);

  /// Serializes this SubscribeToRealTimeUpdatesRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SubscribeToRealTimeUpdatesRequest &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, gameName);

  @override
  String toString() {
    return 'SubscribeToRealTimeUpdatesRequest(gameName: $gameName)';
  }
}

/// @nodoc
abstract mixin class $SubscribeToRealTimeUpdatesRequestCopyWith<$Res> {
  factory $SubscribeToRealTimeUpdatesRequestCopyWith(
          SubscribeToRealTimeUpdatesRequest value,
          $Res Function(SubscribeToRealTimeUpdatesRequest) _then) =
      _$SubscribeToRealTimeUpdatesRequestCopyWithImpl;
  @useResult
  $Res call({String? gameName});
}

/// @nodoc
class _$SubscribeToRealTimeUpdatesRequestCopyWithImpl<$Res>
    implements $SubscribeToRealTimeUpdatesRequestCopyWith<$Res> {
  _$SubscribeToRealTimeUpdatesRequestCopyWithImpl(this._self, this._then);

  final SubscribeToRealTimeUpdatesRequest _self;
  final $Res Function(SubscribeToRealTimeUpdatesRequest) _then;

  /// Create a copy of SubscribeToRealTimeUpdatesRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gameName = freezed,
  }) {
    return _then(_self.copyWith(
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _SubscribeToRealTimeUpdatesRequest
    implements SubscribeToRealTimeUpdatesRequest {
  const _SubscribeToRealTimeUpdatesRequest({this.gameName});
  factory _SubscribeToRealTimeUpdatesRequest.fromJson(
          Map<String, dynamic> json) =>
      _$SubscribeToRealTimeUpdatesRequestFromJson(json);

  @override
  final String? gameName;

  /// Create a copy of SubscribeToRealTimeUpdatesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SubscribeToRealTimeUpdatesRequestCopyWith<
          _SubscribeToRealTimeUpdatesRequest>
      get copyWith => __$SubscribeToRealTimeUpdatesRequestCopyWithImpl<
          _SubscribeToRealTimeUpdatesRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SubscribeToRealTimeUpdatesRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SubscribeToRealTimeUpdatesRequest &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, gameName);

  @override
  String toString() {
    return 'SubscribeToRealTimeUpdatesRequest(gameName: $gameName)';
  }
}

/// @nodoc
abstract mixin class _$SubscribeToRealTimeUpdatesRequestCopyWith<$Res>
    implements $SubscribeToRealTimeUpdatesRequestCopyWith<$Res> {
  factory _$SubscribeToRealTimeUpdatesRequestCopyWith(
          _SubscribeToRealTimeUpdatesRequest value,
          $Res Function(_SubscribeToRealTimeUpdatesRequest) _then) =
      __$SubscribeToRealTimeUpdatesRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String? gameName});
}

/// @nodoc
class __$SubscribeToRealTimeUpdatesRequestCopyWithImpl<$Res>
    implements _$SubscribeToRealTimeUpdatesRequestCopyWith<$Res> {
  __$SubscribeToRealTimeUpdatesRequestCopyWithImpl(this._self, this._then);

  final _SubscribeToRealTimeUpdatesRequest _self;
  final $Res Function(_SubscribeToRealTimeUpdatesRequest) _then;

  /// Create a copy of SubscribeToRealTimeUpdatesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? gameName = freezed,
  }) {
    return _then(_SubscribeToRealTimeUpdatesRequest(
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
mixin _$UnsubscribeFromRealTimeUpdatesRequest {
  /// Serializes this UnsubscribeFromRealTimeUpdatesRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is UnsubscribeFromRealTimeUpdatesRequest);
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'UnsubscribeFromRealTimeUpdatesRequest()';
  }
}

/// @nodoc
class $UnsubscribeFromRealTimeUpdatesRequestCopyWith<$Res> {
  $UnsubscribeFromRealTimeUpdatesRequestCopyWith(
      UnsubscribeFromRealTimeUpdatesRequest _,
      $Res Function(UnsubscribeFromRealTimeUpdatesRequest) __);
}

/// @nodoc
@JsonSerializable()
class _UnsubscribeFromRealTimeUpdatesRequest
    implements UnsubscribeFromRealTimeUpdatesRequest {
  const _UnsubscribeFromRealTimeUpdatesRequest();
  factory _UnsubscribeFromRealTimeUpdatesRequest.fromJson(
          Map<String, dynamic> json) =>
      _$UnsubscribeFromRealTimeUpdatesRequestFromJson(json);

  @override
  Map<String, dynamic> toJson() {
    return _$UnsubscribeFromRealTimeUpdatesRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _UnsubscribeFromRealTimeUpdatesRequest);
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => runtimeType.hashCode;

  @override
  String toString() {
    return 'UnsubscribeFromRealTimeUpdatesRequest()';
  }
}

/// @nodoc
mixin _$RemoveMatchSourceRequest {
  String get sourceName;

  /// Create a copy of RemoveMatchSourceRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RemoveMatchSourceRequestCopyWith<RemoveMatchSourceRequest> get copyWith =>
      _$RemoveMatchSourceRequestCopyWithImpl<RemoveMatchSourceRequest>(
          this as RemoveMatchSourceRequest, _$identity);

  /// Serializes this RemoveMatchSourceRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RemoveMatchSourceRequest &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sourceName);

  @override
  String toString() {
    return 'RemoveMatchSourceRequest(sourceName: $sourceName)';
  }
}

/// @nodoc
abstract mixin class $RemoveMatchSourceRequestCopyWith<$Res> {
  factory $RemoveMatchSourceRequestCopyWith(RemoveMatchSourceRequest value,
          $Res Function(RemoveMatchSourceRequest) _then) =
      _$RemoveMatchSourceRequestCopyWithImpl;
  @useResult
  $Res call({String sourceName});
}

/// @nodoc
class _$RemoveMatchSourceRequestCopyWithImpl<$Res>
    implements $RemoveMatchSourceRequestCopyWith<$Res> {
  _$RemoveMatchSourceRequestCopyWithImpl(this._self, this._then);

  final RemoveMatchSourceRequest _self;
  final $Res Function(RemoveMatchSourceRequest) _then;

  /// Create a copy of RemoveMatchSourceRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sourceName = null,
  }) {
    return _then(_self.copyWith(
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _RemoveMatchSourceRequest implements RemoveMatchSourceRequest {
  const _RemoveMatchSourceRequest({required this.sourceName});
  factory _RemoveMatchSourceRequest.fromJson(Map<String, dynamic> json) =>
      _$RemoveMatchSourceRequestFromJson(json);

  @override
  final String sourceName;

  /// Create a copy of RemoveMatchSourceRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RemoveMatchSourceRequestCopyWith<_RemoveMatchSourceRequest> get copyWith =>
      __$RemoveMatchSourceRequestCopyWithImpl<_RemoveMatchSourceRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RemoveMatchSourceRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RemoveMatchSourceRequest &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sourceName);

  @override
  String toString() {
    return 'RemoveMatchSourceRequest(sourceName: $sourceName)';
  }
}

/// @nodoc
abstract mixin class _$RemoveMatchSourceRequestCopyWith<$Res>
    implements $RemoveMatchSourceRequestCopyWith<$Res> {
  factory _$RemoveMatchSourceRequestCopyWith(_RemoveMatchSourceRequest value,
          $Res Function(_RemoveMatchSourceRequest) _then) =
      __$RemoveMatchSourceRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String sourceName});
}

/// @nodoc
class __$RemoveMatchSourceRequestCopyWithImpl<$Res>
    implements _$RemoveMatchSourceRequestCopyWith<$Res> {
  __$RemoveMatchSourceRequestCopyWithImpl(this._self, this._then);

  final _RemoveMatchSourceRequest _self;
  final $Res Function(_RemoveMatchSourceRequest) _then;

  /// Create a copy of RemoveMatchSourceRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? sourceName = null,
  }) {
    return _then(_RemoveMatchSourceRequest(
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
mixin _$JoinMatchSlotRequest {
  String get matchId;
  int get slotIndex;
  String get playerId;
  List<PlayerSlot> get currentSlots;
  bool get isNetworkMatch;

  /// Create a copy of JoinMatchSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $JoinMatchSlotRequestCopyWith<JoinMatchSlotRequest> get copyWith =>
      _$JoinMatchSlotRequestCopyWithImpl<JoinMatchSlotRequest>(
          this as JoinMatchSlotRequest, _$identity);

  /// Serializes this JoinMatchSlotRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is JoinMatchSlotRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.slotIndex, slotIndex) ||
                other.slotIndex == slotIndex) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            const DeepCollectionEquality()
                .equals(other.currentSlots, currentSlots) &&
            (identical(other.isNetworkMatch, isNetworkMatch) ||
                other.isNetworkMatch == isNetworkMatch));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, slotIndex, playerId,
      const DeepCollectionEquality().hash(currentSlots), isNetworkMatch);

  @override
  String toString() {
    return 'JoinMatchSlotRequest(matchId: $matchId, slotIndex: $slotIndex, playerId: $playerId, currentSlots: $currentSlots, isNetworkMatch: $isNetworkMatch)';
  }
}

/// @nodoc
abstract mixin class $JoinMatchSlotRequestCopyWith<$Res> {
  factory $JoinMatchSlotRequestCopyWith(JoinMatchSlotRequest value,
          $Res Function(JoinMatchSlotRequest) _then) =
      _$JoinMatchSlotRequestCopyWithImpl;
  @useResult
  $Res call(
      {String matchId,
      int slotIndex,
      String playerId,
      List<PlayerSlot> currentSlots,
      bool isNetworkMatch});
}

/// @nodoc
class _$JoinMatchSlotRequestCopyWithImpl<$Res>
    implements $JoinMatchSlotRequestCopyWith<$Res> {
  _$JoinMatchSlotRequestCopyWithImpl(this._self, this._then);

  final JoinMatchSlotRequest _self;
  final $Res Function(JoinMatchSlotRequest) _then;

  /// Create a copy of JoinMatchSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchId = null,
    Object? slotIndex = null,
    Object? playerId = null,
    Object? currentSlots = null,
    Object? isNetworkMatch = null,
  }) {
    return _then(_self.copyWith(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      slotIndex: null == slotIndex
          ? _self.slotIndex
          : slotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      playerId: null == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String,
      currentSlots: null == currentSlots
          ? _self.currentSlots
          : currentSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      isNetworkMatch: null == isNetworkMatch
          ? _self.isNetworkMatch
          : isNetworkMatch // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _JoinMatchSlotRequest implements JoinMatchSlotRequest {
  const _JoinMatchSlotRequest(
      {required this.matchId,
      required this.slotIndex,
      required this.playerId,
      required final List<PlayerSlot> currentSlots,
      this.isNetworkMatch = false})
      : _currentSlots = currentSlots;
  factory _JoinMatchSlotRequest.fromJson(Map<String, dynamic> json) =>
      _$JoinMatchSlotRequestFromJson(json);

  @override
  final String matchId;
  @override
  final int slotIndex;
  @override
  final String playerId;
  final List<PlayerSlot> _currentSlots;
  @override
  List<PlayerSlot> get currentSlots {
    if (_currentSlots is EqualUnmodifiableListView) return _currentSlots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_currentSlots);
  }

  @override
  @JsonKey()
  final bool isNetworkMatch;

  /// Create a copy of JoinMatchSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$JoinMatchSlotRequestCopyWith<_JoinMatchSlotRequest> get copyWith =>
      __$JoinMatchSlotRequestCopyWithImpl<_JoinMatchSlotRequest>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$JoinMatchSlotRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _JoinMatchSlotRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.slotIndex, slotIndex) ||
                other.slotIndex == slotIndex) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            const DeepCollectionEquality()
                .equals(other._currentSlots, _currentSlots) &&
            (identical(other.isNetworkMatch, isNetworkMatch) ||
                other.isNetworkMatch == isNetworkMatch));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, slotIndex, playerId,
      const DeepCollectionEquality().hash(_currentSlots), isNetworkMatch);

  @override
  String toString() {
    return 'JoinMatchSlotRequest(matchId: $matchId, slotIndex: $slotIndex, playerId: $playerId, currentSlots: $currentSlots, isNetworkMatch: $isNetworkMatch)';
  }
}

/// @nodoc
abstract mixin class _$JoinMatchSlotRequestCopyWith<$Res>
    implements $JoinMatchSlotRequestCopyWith<$Res> {
  factory _$JoinMatchSlotRequestCopyWith(_JoinMatchSlotRequest value,
          $Res Function(_JoinMatchSlotRequest) _then) =
      __$JoinMatchSlotRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String matchId,
      int slotIndex,
      String playerId,
      List<PlayerSlot> currentSlots,
      bool isNetworkMatch});
}

/// @nodoc
class __$JoinMatchSlotRequestCopyWithImpl<$Res>
    implements _$JoinMatchSlotRequestCopyWith<$Res> {
  __$JoinMatchSlotRequestCopyWithImpl(this._self, this._then);

  final _JoinMatchSlotRequest _self;
  final $Res Function(_JoinMatchSlotRequest) _then;

  /// Create a copy of JoinMatchSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchId = null,
    Object? slotIndex = null,
    Object? playerId = null,
    Object? currentSlots = null,
    Object? isNetworkMatch = null,
  }) {
    return _then(_JoinMatchSlotRequest(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      slotIndex: null == slotIndex
          ? _self.slotIndex
          : slotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      playerId: null == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String,
      currentSlots: null == currentSlots
          ? _self._currentSlots
          : currentSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      isNetworkMatch: null == isNetworkMatch
          ? _self.isNetworkMatch
          : isNetworkMatch // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
mixin _$FindMatchRequest {
  String get matchId;
  Map<String, List<GameMatch>>? get matchesBySource;

  /// Create a copy of FindMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $FindMatchRequestCopyWith<FindMatchRequest> get copyWith =>
      _$FindMatchRequestCopyWithImpl<FindMatchRequest>(
          this as FindMatchRequest, _$identity);

  /// Serializes this FindMatchRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is FindMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            const DeepCollectionEquality()
                .equals(other.matchesBySource, matchesBySource));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId,
      const DeepCollectionEquality().hash(matchesBySource));

  @override
  String toString() {
    return 'FindMatchRequest(matchId: $matchId, matchesBySource: $matchesBySource)';
  }
}

/// @nodoc
abstract mixin class $FindMatchRequestCopyWith<$Res> {
  factory $FindMatchRequestCopyWith(
          FindMatchRequest value, $Res Function(FindMatchRequest) _then) =
      _$FindMatchRequestCopyWithImpl;
  @useResult
  $Res call({String matchId, Map<String, List<GameMatch>>? matchesBySource});
}

/// @nodoc
class _$FindMatchRequestCopyWithImpl<$Res>
    implements $FindMatchRequestCopyWith<$Res> {
  _$FindMatchRequestCopyWithImpl(this._self, this._then);

  final FindMatchRequest _self;
  final $Res Function(FindMatchRequest) _then;

  /// Create a copy of FindMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchId = null,
    Object? matchesBySource = freezed,
  }) {
    return _then(_self.copyWith(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      matchesBySource: freezed == matchesBySource
          ? _self.matchesBySource
          : matchesBySource // ignore: cast_nullable_to_non_nullable
              as Map<String, List<GameMatch>>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _FindMatchRequest implements FindMatchRequest {
  const _FindMatchRequest(
      {required this.matchId,
      final Map<String, List<GameMatch>>? matchesBySource})
      : _matchesBySource = matchesBySource;
  factory _FindMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$FindMatchRequestFromJson(json);

  @override
  final String matchId;
  final Map<String, List<GameMatch>>? _matchesBySource;
  @override
  Map<String, List<GameMatch>>? get matchesBySource {
    final value = _matchesBySource;
    if (value == null) return null;
    if (_matchesBySource is EqualUnmodifiableMapView) return _matchesBySource;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of FindMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$FindMatchRequestCopyWith<_FindMatchRequest> get copyWith =>
      __$FindMatchRequestCopyWithImpl<_FindMatchRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$FindMatchRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _FindMatchRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            const DeepCollectionEquality()
                .equals(other._matchesBySource, _matchesBySource));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId,
      const DeepCollectionEquality().hash(_matchesBySource));

  @override
  String toString() {
    return 'FindMatchRequest(matchId: $matchId, matchesBySource: $matchesBySource)';
  }
}

/// @nodoc
abstract mixin class _$FindMatchRequestCopyWith<$Res>
    implements $FindMatchRequestCopyWith<$Res> {
  factory _$FindMatchRequestCopyWith(
          _FindMatchRequest value, $Res Function(_FindMatchRequest) _then) =
      __$FindMatchRequestCopyWithImpl;
  @override
  @useResult
  $Res call({String matchId, Map<String, List<GameMatch>>? matchesBySource});
}

/// @nodoc
class __$FindMatchRequestCopyWithImpl<$Res>
    implements _$FindMatchRequestCopyWith<$Res> {
  __$FindMatchRequestCopyWithImpl(this._self, this._then);

  final _FindMatchRequest _self;
  final $Res Function(_FindMatchRequest) _then;

  /// Create a copy of FindMatchRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchId = null,
    Object? matchesBySource = freezed,
  }) {
    return _then(_FindMatchRequest(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      matchesBySource: freezed == matchesBySource
          ? _self._matchesBySource
          : matchesBySource // ignore: cast_nullable_to_non_nullable
              as Map<String, List<GameMatch>>?,
    ));
  }
}

/// @nodoc
mixin _$SwitchSlotRequest {
  String get matchId;
  String get playerId;
  int get fromSlotIndex;
  int get toSlotIndex;
  List<PlayerSlot> get currentSlots;

  /// Create a copy of SwitchSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SwitchSlotRequestCopyWith<SwitchSlotRequest> get copyWith =>
      _$SwitchSlotRequestCopyWithImpl<SwitchSlotRequest>(
          this as SwitchSlotRequest, _$identity);

  /// Serializes this SwitchSlotRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SwitchSlotRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            (identical(other.fromSlotIndex, fromSlotIndex) ||
                other.fromSlotIndex == fromSlotIndex) &&
            (identical(other.toSlotIndex, toSlotIndex) ||
                other.toSlotIndex == toSlotIndex) &&
            const DeepCollectionEquality()
                .equals(other.currentSlots, currentSlots));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, playerId, fromSlotIndex,
      toSlotIndex, const DeepCollectionEquality().hash(currentSlots));

  @override
  String toString() {
    return 'SwitchSlotRequest(matchId: $matchId, playerId: $playerId, fromSlotIndex: $fromSlotIndex, toSlotIndex: $toSlotIndex, currentSlots: $currentSlots)';
  }
}

/// @nodoc
abstract mixin class $SwitchSlotRequestCopyWith<$Res> {
  factory $SwitchSlotRequestCopyWith(
          SwitchSlotRequest value, $Res Function(SwitchSlotRequest) _then) =
      _$SwitchSlotRequestCopyWithImpl;
  @useResult
  $Res call(
      {String matchId,
      String playerId,
      int fromSlotIndex,
      int toSlotIndex,
      List<PlayerSlot> currentSlots});
}

/// @nodoc
class _$SwitchSlotRequestCopyWithImpl<$Res>
    implements $SwitchSlotRequestCopyWith<$Res> {
  _$SwitchSlotRequestCopyWithImpl(this._self, this._then);

  final SwitchSlotRequest _self;
  final $Res Function(SwitchSlotRequest) _then;

  /// Create a copy of SwitchSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchId = null,
    Object? playerId = null,
    Object? fromSlotIndex = null,
    Object? toSlotIndex = null,
    Object? currentSlots = null,
  }) {
    return _then(_self.copyWith(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      playerId: null == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String,
      fromSlotIndex: null == fromSlotIndex
          ? _self.fromSlotIndex
          : fromSlotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      toSlotIndex: null == toSlotIndex
          ? _self.toSlotIndex
          : toSlotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      currentSlots: null == currentSlots
          ? _self.currentSlots
          : currentSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _SwitchSlotRequest implements SwitchSlotRequest {
  const _SwitchSlotRequest(
      {required this.matchId,
      required this.playerId,
      required this.fromSlotIndex,
      required this.toSlotIndex,
      required final List<PlayerSlot> currentSlots})
      : _currentSlots = currentSlots;
  factory _SwitchSlotRequest.fromJson(Map<String, dynamic> json) =>
      _$SwitchSlotRequestFromJson(json);

  @override
  final String matchId;
  @override
  final String playerId;
  @override
  final int fromSlotIndex;
  @override
  final int toSlotIndex;
  final List<PlayerSlot> _currentSlots;
  @override
  List<PlayerSlot> get currentSlots {
    if (_currentSlots is EqualUnmodifiableListView) return _currentSlots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_currentSlots);
  }

  /// Create a copy of SwitchSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SwitchSlotRequestCopyWith<_SwitchSlotRequest> get copyWith =>
      __$SwitchSlotRequestCopyWithImpl<_SwitchSlotRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SwitchSlotRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SwitchSlotRequest &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            (identical(other.playerId, playerId) ||
                other.playerId == playerId) &&
            (identical(other.fromSlotIndex, fromSlotIndex) ||
                other.fromSlotIndex == fromSlotIndex) &&
            (identical(other.toSlotIndex, toSlotIndex) ||
                other.toSlotIndex == toSlotIndex) &&
            const DeepCollectionEquality()
                .equals(other._currentSlots, _currentSlots));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, matchId, playerId, fromSlotIndex,
      toSlotIndex, const DeepCollectionEquality().hash(_currentSlots));

  @override
  String toString() {
    return 'SwitchSlotRequest(matchId: $matchId, playerId: $playerId, fromSlotIndex: $fromSlotIndex, toSlotIndex: $toSlotIndex, currentSlots: $currentSlots)';
  }
}

/// @nodoc
abstract mixin class _$SwitchSlotRequestCopyWith<$Res>
    implements $SwitchSlotRequestCopyWith<$Res> {
  factory _$SwitchSlotRequestCopyWith(
          _SwitchSlotRequest value, $Res Function(_SwitchSlotRequest) _then) =
      __$SwitchSlotRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String matchId,
      String playerId,
      int fromSlotIndex,
      int toSlotIndex,
      List<PlayerSlot> currentSlots});
}

/// @nodoc
class __$SwitchSlotRequestCopyWithImpl<$Res>
    implements _$SwitchSlotRequestCopyWith<$Res> {
  __$SwitchSlotRequestCopyWithImpl(this._self, this._then);

  final _SwitchSlotRequest _self;
  final $Res Function(_SwitchSlotRequest) _then;

  /// Create a copy of SwitchSlotRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchId = null,
    Object? playerId = null,
    Object? fromSlotIndex = null,
    Object? toSlotIndex = null,
    Object? currentSlots = null,
  }) {
    return _then(_SwitchSlotRequest(
      matchId: null == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String,
      playerId: null == playerId
          ? _self.playerId
          : playerId // ignore: cast_nullable_to_non_nullable
              as String,
      fromSlotIndex: null == fromSlotIndex
          ? _self.fromSlotIndex
          : fromSlotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      toSlotIndex: null == toSlotIndex
          ? _self.toSlotIndex
          : toSlotIndex // ignore: cast_nullable_to_non_nullable
              as int,
      currentSlots: null == currentSlots
          ? _self._currentSlots
          : currentSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
    ));
  }
}

/// @nodoc
mixin _$ProcessWebSocketUpdateRequest {
  List<GameMatch> get matches;
  Map<String, List<GameMatch>> get currentMatchesBySource;
  GameMatch? get currentSelectedMatch;

  /// Create a copy of ProcessWebSocketUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $ProcessWebSocketUpdateRequestCopyWith<ProcessWebSocketUpdateRequest>
      get copyWith => _$ProcessWebSocketUpdateRequestCopyWithImpl<
              ProcessWebSocketUpdateRequest>(
          this as ProcessWebSocketUpdateRequest, _$identity);

  /// Serializes this ProcessWebSocketUpdateRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is ProcessWebSocketUpdateRequest &&
            const DeepCollectionEquality().equals(other.matches, matches) &&
            const DeepCollectionEquality()
                .equals(other.currentMatchesBySource, currentMatchesBySource) &&
            (identical(other.currentSelectedMatch, currentSelectedMatch) ||
                other.currentSelectedMatch == currentSelectedMatch));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(matches),
      const DeepCollectionEquality().hash(currentMatchesBySource),
      currentSelectedMatch);

  @override
  String toString() {
    return 'ProcessWebSocketUpdateRequest(matches: $matches, currentMatchesBySource: $currentMatchesBySource, currentSelectedMatch: $currentSelectedMatch)';
  }
}

/// @nodoc
abstract mixin class $ProcessWebSocketUpdateRequestCopyWith<$Res> {
  factory $ProcessWebSocketUpdateRequestCopyWith(
          ProcessWebSocketUpdateRequest value,
          $Res Function(ProcessWebSocketUpdateRequest) _then) =
      _$ProcessWebSocketUpdateRequestCopyWithImpl;
  @useResult
  $Res call(
      {List<GameMatch> matches,
      Map<String, List<GameMatch>> currentMatchesBySource,
      GameMatch? currentSelectedMatch});

  $GameMatchCopyWith<$Res>? get currentSelectedMatch;
}

/// @nodoc
class _$ProcessWebSocketUpdateRequestCopyWithImpl<$Res>
    implements $ProcessWebSocketUpdateRequestCopyWith<$Res> {
  _$ProcessWebSocketUpdateRequestCopyWithImpl(this._self, this._then);

  final ProcessWebSocketUpdateRequest _self;
  final $Res Function(ProcessWebSocketUpdateRequest) _then;

  /// Create a copy of ProcessWebSocketUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matches = null,
    Object? currentMatchesBySource = null,
    Object? currentSelectedMatch = freezed,
  }) {
    return _then(_self.copyWith(
      matches: null == matches
          ? _self.matches
          : matches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      currentMatchesBySource: null == currentMatchesBySource
          ? _self.currentMatchesBySource
          : currentMatchesBySource // ignore: cast_nullable_to_non_nullable
              as Map<String, List<GameMatch>>,
      currentSelectedMatch: freezed == currentSelectedMatch
          ? _self.currentSelectedMatch
          : currentSelectedMatch // ignore: cast_nullable_to_non_nullable
              as GameMatch?,
    ));
  }

  /// Create a copy of ProcessWebSocketUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameMatchCopyWith<$Res>? get currentSelectedMatch {
    if (_self.currentSelectedMatch == null) {
      return null;
    }

    return $GameMatchCopyWith<$Res>(_self.currentSelectedMatch!, (value) {
      return _then(_self.copyWith(currentSelectedMatch: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _ProcessWebSocketUpdateRequest implements ProcessWebSocketUpdateRequest {
  const _ProcessWebSocketUpdateRequest(
      {required final List<GameMatch> matches,
      required final Map<String, List<GameMatch>> currentMatchesBySource,
      this.currentSelectedMatch})
      : _matches = matches,
        _currentMatchesBySource = currentMatchesBySource;
  factory _ProcessWebSocketUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$ProcessWebSocketUpdateRequestFromJson(json);

  final List<GameMatch> _matches;
  @override
  List<GameMatch> get matches {
    if (_matches is EqualUnmodifiableListView) return _matches;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_matches);
  }

  final Map<String, List<GameMatch>> _currentMatchesBySource;
  @override
  Map<String, List<GameMatch>> get currentMatchesBySource {
    if (_currentMatchesBySource is EqualUnmodifiableMapView)
      return _currentMatchesBySource;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_currentMatchesBySource);
  }

  @override
  final GameMatch? currentSelectedMatch;

  /// Create a copy of ProcessWebSocketUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$ProcessWebSocketUpdateRequestCopyWith<_ProcessWebSocketUpdateRequest>
      get copyWith => __$ProcessWebSocketUpdateRequestCopyWithImpl<
          _ProcessWebSocketUpdateRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$ProcessWebSocketUpdateRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _ProcessWebSocketUpdateRequest &&
            const DeepCollectionEquality().equals(other._matches, _matches) &&
            const DeepCollectionEquality().equals(
                other._currentMatchesBySource, _currentMatchesBySource) &&
            (identical(other.currentSelectedMatch, currentSelectedMatch) ||
                other.currentSelectedMatch == currentSelectedMatch));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(_matches),
      const DeepCollectionEquality().hash(_currentMatchesBySource),
      currentSelectedMatch);

  @override
  String toString() {
    return 'ProcessWebSocketUpdateRequest(matches: $matches, currentMatchesBySource: $currentMatchesBySource, currentSelectedMatch: $currentSelectedMatch)';
  }
}

/// @nodoc
abstract mixin class _$ProcessWebSocketUpdateRequestCopyWith<$Res>
    implements $ProcessWebSocketUpdateRequestCopyWith<$Res> {
  factory _$ProcessWebSocketUpdateRequestCopyWith(
          _ProcessWebSocketUpdateRequest value,
          $Res Function(_ProcessWebSocketUpdateRequest) _then) =
      __$ProcessWebSocketUpdateRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {List<GameMatch> matches,
      Map<String, List<GameMatch>> currentMatchesBySource,
      GameMatch? currentSelectedMatch});

  @override
  $GameMatchCopyWith<$Res>? get currentSelectedMatch;
}

/// @nodoc
class __$ProcessWebSocketUpdateRequestCopyWithImpl<$Res>
    implements _$ProcessWebSocketUpdateRequestCopyWith<$Res> {
  __$ProcessWebSocketUpdateRequestCopyWithImpl(this._self, this._then);

  final _ProcessWebSocketUpdateRequest _self;
  final $Res Function(_ProcessWebSocketUpdateRequest) _then;

  /// Create a copy of ProcessWebSocketUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matches = null,
    Object? currentMatchesBySource = null,
    Object? currentSelectedMatch = freezed,
  }) {
    return _then(_ProcessWebSocketUpdateRequest(
      matches: null == matches
          ? _self._matches
          : matches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      currentMatchesBySource: null == currentMatchesBySource
          ? _self._currentMatchesBySource
          : currentMatchesBySource // ignore: cast_nullable_to_non_nullable
              as Map<String, List<GameMatch>>,
      currentSelectedMatch: freezed == currentSelectedMatch
          ? _self.currentSelectedMatch
          : currentSelectedMatch // ignore: cast_nullable_to_non_nullable
              as GameMatch?,
    ));
  }

  /// Create a copy of ProcessWebSocketUpdateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameMatchCopyWith<$Res>? get currentSelectedMatch {
    if (_self.currentSelectedMatch == null) {
      return null;
    }

    return $GameMatchCopyWith<$Res>(_self.currentSelectedMatch!, (value) {
      return _then(_self.copyWith(currentSelectedMatch: value));
    });
  }
}

/// @nodoc
mixin _$SetupWebSocketListenerRequest {
  Map<String, dynamic>? get repositories;

  /// Create a copy of SetupWebSocketListenerRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $SetupWebSocketListenerRequestCopyWith<SetupWebSocketListenerRequest>
      get copyWith => _$SetupWebSocketListenerRequestCopyWithImpl<
              SetupWebSocketListenerRequest>(
          this as SetupWebSocketListenerRequest, _$identity);

  /// Serializes this SetupWebSocketListenerRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is SetupWebSocketListenerRequest &&
            const DeepCollectionEquality()
                .equals(other.repositories, repositories));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(repositories));

  @override
  String toString() {
    return 'SetupWebSocketListenerRequest(repositories: $repositories)';
  }
}

/// @nodoc
abstract mixin class $SetupWebSocketListenerRequestCopyWith<$Res> {
  factory $SetupWebSocketListenerRequestCopyWith(
          SetupWebSocketListenerRequest value,
          $Res Function(SetupWebSocketListenerRequest) _then) =
      _$SetupWebSocketListenerRequestCopyWithImpl;
  @useResult
  $Res call({Map<String, dynamic>? repositories});
}

/// @nodoc
class _$SetupWebSocketListenerRequestCopyWithImpl<$Res>
    implements $SetupWebSocketListenerRequestCopyWith<$Res> {
  _$SetupWebSocketListenerRequestCopyWithImpl(this._self, this._then);

  final SetupWebSocketListenerRequest _self;
  final $Res Function(SetupWebSocketListenerRequest) _then;

  /// Create a copy of SetupWebSocketListenerRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? repositories = freezed,
  }) {
    return _then(_self.copyWith(
      repositories: freezed == repositories
          ? _self.repositories
          : repositories // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _SetupWebSocketListenerRequest implements SetupWebSocketListenerRequest {
  const _SetupWebSocketListenerRequest(
      {final Map<String, dynamic>? repositories})
      : _repositories = repositories;
  factory _SetupWebSocketListenerRequest.fromJson(Map<String, dynamic> json) =>
      _$SetupWebSocketListenerRequestFromJson(json);

  final Map<String, dynamic>? _repositories;
  @override
  Map<String, dynamic>? get repositories {
    final value = _repositories;
    if (value == null) return null;
    if (_repositories is EqualUnmodifiableMapView) return _repositories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  /// Create a copy of SetupWebSocketListenerRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$SetupWebSocketListenerRequestCopyWith<_SetupWebSocketListenerRequest>
      get copyWith => __$SetupWebSocketListenerRequestCopyWithImpl<
          _SetupWebSocketListenerRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$SetupWebSocketListenerRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _SetupWebSocketListenerRequest &&
            const DeepCollectionEquality()
                .equals(other._repositories, _repositories));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_repositories));

  @override
  String toString() {
    return 'SetupWebSocketListenerRequest(repositories: $repositories)';
  }
}

/// @nodoc
abstract mixin class _$SetupWebSocketListenerRequestCopyWith<$Res>
    implements $SetupWebSocketListenerRequestCopyWith<$Res> {
  factory _$SetupWebSocketListenerRequestCopyWith(
          _SetupWebSocketListenerRequest value,
          $Res Function(_SetupWebSocketListenerRequest) _then) =
      __$SetupWebSocketListenerRequestCopyWithImpl;
  @override
  @useResult
  $Res call({Map<String, dynamic>? repositories});
}

/// @nodoc
class __$SetupWebSocketListenerRequestCopyWithImpl<$Res>
    implements _$SetupWebSocketListenerRequestCopyWith<$Res> {
  __$SetupWebSocketListenerRequestCopyWithImpl(this._self, this._then);

  final _SetupWebSocketListenerRequest _self;
  final $Res Function(_SetupWebSocketListenerRequest) _then;

  /// Create a copy of SetupWebSocketListenerRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? repositories = freezed,
  }) {
    return _then(_SetupWebSocketListenerRequest(
      repositories: freezed == repositories
          ? _self._repositories
          : repositories // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
    ));
  }
}

/// @nodoc
mixin _$EvaluateNetworkStateRequest {
  bool get hasNetworkCapability;
  bool get isCreatingMatch;
  List<PlayerSlot> get playerSlots;
  GameMatch? get selectedMatch;

  /// Create a copy of EvaluateNetworkStateRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $EvaluateNetworkStateRequestCopyWith<EvaluateNetworkStateRequest>
      get copyWith => _$EvaluateNetworkStateRequestCopyWithImpl<
              EvaluateNetworkStateRequest>(
          this as EvaluateNetworkStateRequest, _$identity);

  /// Serializes this EvaluateNetworkStateRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is EvaluateNetworkStateRequest &&
            (identical(other.hasNetworkCapability, hasNetworkCapability) ||
                other.hasNetworkCapability == hasNetworkCapability) &&
            (identical(other.isCreatingMatch, isCreatingMatch) ||
                other.isCreatingMatch == isCreatingMatch) &&
            const DeepCollectionEquality()
                .equals(other.playerSlots, playerSlots) &&
            (identical(other.selectedMatch, selectedMatch) ||
                other.selectedMatch == selectedMatch));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      hasNetworkCapability,
      isCreatingMatch,
      const DeepCollectionEquality().hash(playerSlots),
      selectedMatch);

  @override
  String toString() {
    return 'EvaluateNetworkStateRequest(hasNetworkCapability: $hasNetworkCapability, isCreatingMatch: $isCreatingMatch, playerSlots: $playerSlots, selectedMatch: $selectedMatch)';
  }
}

/// @nodoc
abstract mixin class $EvaluateNetworkStateRequestCopyWith<$Res> {
  factory $EvaluateNetworkStateRequestCopyWith(
          EvaluateNetworkStateRequest value,
          $Res Function(EvaluateNetworkStateRequest) _then) =
      _$EvaluateNetworkStateRequestCopyWithImpl;
  @useResult
  $Res call(
      {bool hasNetworkCapability,
      bool isCreatingMatch,
      List<PlayerSlot> playerSlots,
      GameMatch? selectedMatch});

  $GameMatchCopyWith<$Res>? get selectedMatch;
}

/// @nodoc
class _$EvaluateNetworkStateRequestCopyWithImpl<$Res>
    implements $EvaluateNetworkStateRequestCopyWith<$Res> {
  _$EvaluateNetworkStateRequestCopyWithImpl(this._self, this._then);

  final EvaluateNetworkStateRequest _self;
  final $Res Function(EvaluateNetworkStateRequest) _then;

  /// Create a copy of EvaluateNetworkStateRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasNetworkCapability = null,
    Object? isCreatingMatch = null,
    Object? playerSlots = null,
    Object? selectedMatch = freezed,
  }) {
    return _then(_self.copyWith(
      hasNetworkCapability: null == hasNetworkCapability
          ? _self.hasNetworkCapability
          : hasNetworkCapability // ignore: cast_nullable_to_non_nullable
              as bool,
      isCreatingMatch: null == isCreatingMatch
          ? _self.isCreatingMatch
          : isCreatingMatch // ignore: cast_nullable_to_non_nullable
              as bool,
      playerSlots: null == playerSlots
          ? _self.playerSlots
          : playerSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      selectedMatch: freezed == selectedMatch
          ? _self.selectedMatch
          : selectedMatch // ignore: cast_nullable_to_non_nullable
              as GameMatch?,
    ));
  }

  /// Create a copy of EvaluateNetworkStateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameMatchCopyWith<$Res>? get selectedMatch {
    if (_self.selectedMatch == null) {
      return null;
    }

    return $GameMatchCopyWith<$Res>(_self.selectedMatch!, (value) {
      return _then(_self.copyWith(selectedMatch: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _EvaluateNetworkStateRequest implements EvaluateNetworkStateRequest {
  const _EvaluateNetworkStateRequest(
      {required this.hasNetworkCapability,
      required this.isCreatingMatch,
      required final List<PlayerSlot> playerSlots,
      this.selectedMatch})
      : _playerSlots = playerSlots;
  factory _EvaluateNetworkStateRequest.fromJson(Map<String, dynamic> json) =>
      _$EvaluateNetworkStateRequestFromJson(json);

  @override
  final bool hasNetworkCapability;
  @override
  final bool isCreatingMatch;
  final List<PlayerSlot> _playerSlots;
  @override
  List<PlayerSlot> get playerSlots {
    if (_playerSlots is EqualUnmodifiableListView) return _playerSlots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_playerSlots);
  }

  @override
  final GameMatch? selectedMatch;

  /// Create a copy of EvaluateNetworkStateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$EvaluateNetworkStateRequestCopyWith<_EvaluateNetworkStateRequest>
      get copyWith => __$EvaluateNetworkStateRequestCopyWithImpl<
          _EvaluateNetworkStateRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$EvaluateNetworkStateRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _EvaluateNetworkStateRequest &&
            (identical(other.hasNetworkCapability, hasNetworkCapability) ||
                other.hasNetworkCapability == hasNetworkCapability) &&
            (identical(other.isCreatingMatch, isCreatingMatch) ||
                other.isCreatingMatch == isCreatingMatch) &&
            const DeepCollectionEquality()
                .equals(other._playerSlots, _playerSlots) &&
            (identical(other.selectedMatch, selectedMatch) ||
                other.selectedMatch == selectedMatch));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      hasNetworkCapability,
      isCreatingMatch,
      const DeepCollectionEquality().hash(_playerSlots),
      selectedMatch);

  @override
  String toString() {
    return 'EvaluateNetworkStateRequest(hasNetworkCapability: $hasNetworkCapability, isCreatingMatch: $isCreatingMatch, playerSlots: $playerSlots, selectedMatch: $selectedMatch)';
  }
}

/// @nodoc
abstract mixin class _$EvaluateNetworkStateRequestCopyWith<$Res>
    implements $EvaluateNetworkStateRequestCopyWith<$Res> {
  factory _$EvaluateNetworkStateRequestCopyWith(
          _EvaluateNetworkStateRequest value,
          $Res Function(_EvaluateNetworkStateRequest) _then) =
      __$EvaluateNetworkStateRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {bool hasNetworkCapability,
      bool isCreatingMatch,
      List<PlayerSlot> playerSlots,
      GameMatch? selectedMatch});

  @override
  $GameMatchCopyWith<$Res>? get selectedMatch;
}

/// @nodoc
class __$EvaluateNetworkStateRequestCopyWithImpl<$Res>
    implements _$EvaluateNetworkStateRequestCopyWith<$Res> {
  __$EvaluateNetworkStateRequestCopyWithImpl(this._self, this._then);

  final _EvaluateNetworkStateRequest _self;
  final $Res Function(_EvaluateNetworkStateRequest) _then;

  /// Create a copy of EvaluateNetworkStateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? hasNetworkCapability = null,
    Object? isCreatingMatch = null,
    Object? playerSlots = null,
    Object? selectedMatch = freezed,
  }) {
    return _then(_EvaluateNetworkStateRequest(
      hasNetworkCapability: null == hasNetworkCapability
          ? _self.hasNetworkCapability
          : hasNetworkCapability // ignore: cast_nullable_to_non_nullable
              as bool,
      isCreatingMatch: null == isCreatingMatch
          ? _self.isCreatingMatch
          : isCreatingMatch // ignore: cast_nullable_to_non_nullable
              as bool,
      playerSlots: null == playerSlots
          ? _self._playerSlots
          : playerSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      selectedMatch: freezed == selectedMatch
          ? _self.selectedMatch
          : selectedMatch // ignore: cast_nullable_to_non_nullable
              as GameMatch?,
    ));
  }

  /// Create a copy of EvaluateNetworkStateRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameMatchCopyWith<$Res>? get selectedMatch {
    if (_self.selectedMatch == null) {
      return null;
    }

    return $GameMatchCopyWith<$Res>(_self.selectedMatch!, (value) {
      return _then(_self.copyWith(selectedMatch: value));
    });
  }
}

/// @nodoc
mixin _$CleanupNetworkMatchesRequest {
  Map<String, List<GameMatch>> get matchesBySource;

  /// Create a copy of CleanupNetworkMatchesRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $CleanupNetworkMatchesRequestCopyWith<CleanupNetworkMatchesRequest>
      get copyWith => _$CleanupNetworkMatchesRequestCopyWithImpl<
              CleanupNetworkMatchesRequest>(
          this as CleanupNetworkMatchesRequest, _$identity);

  /// Serializes this CleanupNetworkMatchesRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CleanupNetworkMatchesRequest &&
            const DeepCollectionEquality()
                .equals(other.matchesBySource, matchesBySource));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(matchesBySource));

  @override
  String toString() {
    return 'CleanupNetworkMatchesRequest(matchesBySource: $matchesBySource)';
  }
}

/// @nodoc
abstract mixin class $CleanupNetworkMatchesRequestCopyWith<$Res> {
  factory $CleanupNetworkMatchesRequestCopyWith(
          CleanupNetworkMatchesRequest value,
          $Res Function(CleanupNetworkMatchesRequest) _then) =
      _$CleanupNetworkMatchesRequestCopyWithImpl;
  @useResult
  $Res call({Map<String, List<GameMatch>> matchesBySource});
}

/// @nodoc
class _$CleanupNetworkMatchesRequestCopyWithImpl<$Res>
    implements $CleanupNetworkMatchesRequestCopyWith<$Res> {
  _$CleanupNetworkMatchesRequestCopyWithImpl(this._self, this._then);

  final CleanupNetworkMatchesRequest _self;
  final $Res Function(CleanupNetworkMatchesRequest) _then;

  /// Create a copy of CleanupNetworkMatchesRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? matchesBySource = null,
  }) {
    return _then(_self.copyWith(
      matchesBySource: null == matchesBySource
          ? _self.matchesBySource
          : matchesBySource // ignore: cast_nullable_to_non_nullable
              as Map<String, List<GameMatch>>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _CleanupNetworkMatchesRequest implements CleanupNetworkMatchesRequest {
  const _CleanupNetworkMatchesRequest(
      {required final Map<String, List<GameMatch>> matchesBySource})
      : _matchesBySource = matchesBySource;
  factory _CleanupNetworkMatchesRequest.fromJson(Map<String, dynamic> json) =>
      _$CleanupNetworkMatchesRequestFromJson(json);

  final Map<String, List<GameMatch>> _matchesBySource;
  @override
  Map<String, List<GameMatch>> get matchesBySource {
    if (_matchesBySource is EqualUnmodifiableMapView) return _matchesBySource;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_matchesBySource);
  }

  /// Create a copy of CleanupNetworkMatchesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$CleanupNetworkMatchesRequestCopyWith<_CleanupNetworkMatchesRequest>
      get copyWith => __$CleanupNetworkMatchesRequestCopyWithImpl<
          _CleanupNetworkMatchesRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$CleanupNetworkMatchesRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _CleanupNetworkMatchesRequest &&
            const DeepCollectionEquality()
                .equals(other._matchesBySource, _matchesBySource));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_matchesBySource));

  @override
  String toString() {
    return 'CleanupNetworkMatchesRequest(matchesBySource: $matchesBySource)';
  }
}

/// @nodoc
abstract mixin class _$CleanupNetworkMatchesRequestCopyWith<$Res>
    implements $CleanupNetworkMatchesRequestCopyWith<$Res> {
  factory _$CleanupNetworkMatchesRequestCopyWith(
          _CleanupNetworkMatchesRequest value,
          $Res Function(_CleanupNetworkMatchesRequest) _then) =
      __$CleanupNetworkMatchesRequestCopyWithImpl;
  @override
  @useResult
  $Res call({Map<String, List<GameMatch>> matchesBySource});
}

/// @nodoc
class __$CleanupNetworkMatchesRequestCopyWithImpl<$Res>
    implements _$CleanupNetworkMatchesRequestCopyWith<$Res> {
  __$CleanupNetworkMatchesRequestCopyWithImpl(this._self, this._then);

  final _CleanupNetworkMatchesRequest _self;
  final $Res Function(_CleanupNetworkMatchesRequest) _then;

  /// Create a copy of CleanupNetworkMatchesRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? matchesBySource = null,
  }) {
    return _then(_CleanupNetworkMatchesRequest(
      matchesBySource: null == matchesBySource
          ? _self._matchesBySource
          : matchesBySource // ignore: cast_nullable_to_non_nullable
              as Map<String, List<GameMatch>>,
    ));
  }
}

/// @nodoc
mixin _$RefreshMatchesFromSourceRequest {
  String get sourceName;
  Map<String, List<GameMatch>> get currentMatchesBySource;
  String? get gameName;

  /// Create a copy of RefreshMatchesFromSourceRequest
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $RefreshMatchesFromSourceRequestCopyWith<RefreshMatchesFromSourceRequest>
      get copyWith => _$RefreshMatchesFromSourceRequestCopyWithImpl<
              RefreshMatchesFromSourceRequest>(
          this as RefreshMatchesFromSourceRequest, _$identity);

  /// Serializes this RefreshMatchesFromSourceRequest to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is RefreshMatchesFromSourceRequest &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName) &&
            const DeepCollectionEquality()
                .equals(other.currentMatchesBySource, currentMatchesBySource) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sourceName,
      const DeepCollectionEquality().hash(currentMatchesBySource), gameName);

  @override
  String toString() {
    return 'RefreshMatchesFromSourceRequest(sourceName: $sourceName, currentMatchesBySource: $currentMatchesBySource, gameName: $gameName)';
  }
}

/// @nodoc
abstract mixin class $RefreshMatchesFromSourceRequestCopyWith<$Res> {
  factory $RefreshMatchesFromSourceRequestCopyWith(
          RefreshMatchesFromSourceRequest value,
          $Res Function(RefreshMatchesFromSourceRequest) _then) =
      _$RefreshMatchesFromSourceRequestCopyWithImpl;
  @useResult
  $Res call(
      {String sourceName,
      Map<String, List<GameMatch>> currentMatchesBySource,
      String? gameName});
}

/// @nodoc
class _$RefreshMatchesFromSourceRequestCopyWithImpl<$Res>
    implements $RefreshMatchesFromSourceRequestCopyWith<$Res> {
  _$RefreshMatchesFromSourceRequestCopyWithImpl(this._self, this._then);

  final RefreshMatchesFromSourceRequest _self;
  final $Res Function(RefreshMatchesFromSourceRequest) _then;

  /// Create a copy of RefreshMatchesFromSourceRequest
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sourceName = null,
    Object? currentMatchesBySource = null,
    Object? gameName = freezed,
  }) {
    return _then(_self.copyWith(
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
      currentMatchesBySource: null == currentMatchesBySource
          ? _self.currentMatchesBySource
          : currentMatchesBySource // ignore: cast_nullable_to_non_nullable
              as Map<String, List<GameMatch>>,
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _RefreshMatchesFromSourceRequest
    implements RefreshMatchesFromSourceRequest {
  const _RefreshMatchesFromSourceRequest(
      {required this.sourceName,
      required final Map<String, List<GameMatch>> currentMatchesBySource,
      this.gameName})
      : _currentMatchesBySource = currentMatchesBySource;
  factory _RefreshMatchesFromSourceRequest.fromJson(
          Map<String, dynamic> json) =>
      _$RefreshMatchesFromSourceRequestFromJson(json);

  @override
  final String sourceName;
  final Map<String, List<GameMatch>> _currentMatchesBySource;
  @override
  Map<String, List<GameMatch>> get currentMatchesBySource {
    if (_currentMatchesBySource is EqualUnmodifiableMapView)
      return _currentMatchesBySource;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_currentMatchesBySource);
  }

  @override
  final String? gameName;

  /// Create a copy of RefreshMatchesFromSourceRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$RefreshMatchesFromSourceRequestCopyWith<_RefreshMatchesFromSourceRequest>
      get copyWith => __$RefreshMatchesFromSourceRequestCopyWithImpl<
          _RefreshMatchesFromSourceRequest>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$RefreshMatchesFromSourceRequestToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _RefreshMatchesFromSourceRequest &&
            (identical(other.sourceName, sourceName) ||
                other.sourceName == sourceName) &&
            const DeepCollectionEquality().equals(
                other._currentMatchesBySource, _currentMatchesBySource) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, sourceName,
      const DeepCollectionEquality().hash(_currentMatchesBySource), gameName);

  @override
  String toString() {
    return 'RefreshMatchesFromSourceRequest(sourceName: $sourceName, currentMatchesBySource: $currentMatchesBySource, gameName: $gameName)';
  }
}

/// @nodoc
abstract mixin class _$RefreshMatchesFromSourceRequestCopyWith<$Res>
    implements $RefreshMatchesFromSourceRequestCopyWith<$Res> {
  factory _$RefreshMatchesFromSourceRequestCopyWith(
          _RefreshMatchesFromSourceRequest value,
          $Res Function(_RefreshMatchesFromSourceRequest) _then) =
      __$RefreshMatchesFromSourceRequestCopyWithImpl;
  @override
  @useResult
  $Res call(
      {String sourceName,
      Map<String, List<GameMatch>> currentMatchesBySource,
      String? gameName});
}

/// @nodoc
class __$RefreshMatchesFromSourceRequestCopyWithImpl<$Res>
    implements _$RefreshMatchesFromSourceRequestCopyWith<$Res> {
  __$RefreshMatchesFromSourceRequestCopyWithImpl(this._self, this._then);

  final _RefreshMatchesFromSourceRequest _self;
  final $Res Function(_RefreshMatchesFromSourceRequest) _then;

  /// Create a copy of RefreshMatchesFromSourceRequest
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? sourceName = null,
    Object? currentMatchesBySource = null,
    Object? gameName = freezed,
  }) {
    return _then(_RefreshMatchesFromSourceRequest(
      sourceName: null == sourceName
          ? _self.sourceName
          : sourceName // ignore: cast_nullable_to_non_nullable
              as String,
      currentMatchesBySource: null == currentMatchesBySource
          ? _self._currentMatchesBySource
          : currentMatchesBySource // ignore: cast_nullable_to_non_nullable
              as Map<String, List<GameMatch>>,
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

// dart format on
