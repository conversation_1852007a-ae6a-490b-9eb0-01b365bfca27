import 'package:common/models/game_match.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'match_management_requests.freezed.dart';
part 'match_management_requests.g.dart';

/// Request model for creating a new match
@freezed
abstract class CreateMatchRequest with _$CreateMatchRequest {
  const factory CreateMatchRequest({
    required GameConfig gameConfig,
    required List<PlayerSlot> playerSlots,
    required String gameName,
    @Default(true) bool openForJoining,
  }) = _CreateMatchRequest;

  factory CreateMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$CreateMatchRequestFromJson(json);
}

/// Request model for updating a player type in a slot
@freezed
abstract class UpdatePlayerTypeRequest with _$UpdatePlayerTypeRequest {
  const factory UpdatePlayerTypeRequest({
    required int slotIndex,
    required PlayerType newType,
    required List<PlayerSlot> currentSlots,
    required GameConfig gameConfig,
    String? matchId,
    @Default(false) bool isSelectedMatch,
  }) = _UpdatePlayerTypeRequest;

  factory UpdatePlayerTypeRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdatePlayerTypeRequestFromJson(json);
}

/// Request model for joining a player slot
@freezed
abstract class JoinSlotRequest with _$JoinSlotRequest {
  const factory JoinSlotRequest({
    required int slotIndex,
    required List<PlayerSlot> currentSlots,
    String? playerId,
    String? matchId,
  }) = _JoinSlotRequest;

  factory JoinSlotRequest.fromJson(Map<String, dynamic> json) =>
      _$JoinSlotRequestFromJson(json);
}

/// Request model for loading match data
@freezed
abstract class LoadMatchDataRequest with _$LoadMatchDataRequest {
  const factory LoadMatchDataRequest({
    String? gameName,
    @Default(false) bool forceRefresh,
    Set<String>? specificSources,
  }) = _LoadMatchDataRequest;

  factory LoadMatchDataRequest.fromJson(Map<String, dynamic> json) =>
      _$LoadMatchDataRequestFromJson(json);
}

/// Request model for refreshing matches from a specific source
@freezed
abstract class RefreshMatchesRequest with _$RefreshMatchesRequest {
  const factory RefreshMatchesRequest({
    required String sourceName,
    String? gameName,
  }) = _RefreshMatchesRequest;

  factory RefreshMatchesRequest.fromJson(Map<String, dynamic> json) =>
      _$RefreshMatchesRequestFromJson(json);
}

/// Request model for joining a match
@freezed
abstract class JoinMatchRequest with _$JoinMatchRequest {
  const factory JoinMatchRequest({
    required String matchId,
    String? playerId,
  }) = _JoinMatchRequest;

  factory JoinMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$JoinMatchRequestFromJson(json);
}

/// Request model for leaving a match
@freezed
abstract class LeaveMatchRequest with _$LeaveMatchRequest {
  const factory LeaveMatchRequest({
    required String matchId,
    String? playerId,
  }) = _LeaveMatchRequest;

  factory LeaveMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$LeaveMatchRequestFromJson(json);
}

/// Request model for deleting a match
@freezed
abstract class DeleteMatchRequest with _$DeleteMatchRequest {
  const factory DeleteMatchRequest({
    required String matchId,
  }) = _DeleteMatchRequest;

  factory DeleteMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$DeleteMatchRequestFromJson(json);
}

/// Request model for subscribing to real-time updates
@freezed
abstract class SubscribeToRealTimeUpdatesRequest with _$SubscribeToRealTimeUpdatesRequest {
  const factory SubscribeToRealTimeUpdatesRequest({
    String? gameName,
  }) = _SubscribeToRealTimeUpdatesRequest;

  factory SubscribeToRealTimeUpdatesRequest.fromJson(Map<String, dynamic> json) =>
      _$SubscribeToRealTimeUpdatesRequestFromJson(json);
}

/// Request model for unsubscribing from real-time updates
@freezed
abstract class UnsubscribeFromRealTimeUpdatesRequest with _$UnsubscribeFromRealTimeUpdatesRequest {
  const factory UnsubscribeFromRealTimeUpdatesRequest() = _UnsubscribeFromRealTimeUpdatesRequest;

  factory UnsubscribeFromRealTimeUpdatesRequest.fromJson(Map<String, dynamic> json) =>
      _$UnsubscribeFromRealTimeUpdatesRequestFromJson(json);
}

/// Request model for removing a match source
@freezed
abstract class RemoveMatchSourceRequest with _$RemoveMatchSourceRequest {
  const factory RemoveMatchSourceRequest({
    required String sourceName,
  }) = _RemoveMatchSourceRequest;

  factory RemoveMatchSourceRequest.fromJson(Map<String, dynamic> json) =>
      _$RemoveMatchSourceRequestFromJson(json);
}

/// Request model for joining a match slot with slot switching
@freezed
abstract class JoinMatchSlotRequest with _$JoinMatchSlotRequest {
  const factory JoinMatchSlotRequest({
    required String matchId,
    required int slotIndex,
    required String playerId,
    required List<PlayerSlot> currentSlots,
    @Default(false) bool isNetworkMatch,
  }) = _JoinMatchSlotRequest;

  factory JoinMatchSlotRequest.fromJson(Map<String, dynamic> json) =>
      _$JoinMatchSlotRequestFromJson(json);
}

/// Request model for finding a match by ID across sources
@freezed
abstract class FindMatchRequest with _$FindMatchRequest {
  const factory FindMatchRequest({
    required String matchId,
    Map<String, List<GameMatch>>? matchesBySource,
  }) = _FindMatchRequest;

  factory FindMatchRequest.fromJson(Map<String, dynamic> json) =>
      _$FindMatchRequestFromJson(json);
}

/// Request model for switching player slots
@freezed
abstract class SwitchSlotRequest with _$SwitchSlotRequest {
  const factory SwitchSlotRequest({
    required String matchId,
    required String playerId,
    required int fromSlotIndex,
    required int toSlotIndex,
    required List<PlayerSlot> currentSlots,
  }) = _SwitchSlotRequest;

  factory SwitchSlotRequest.fromJson(Map<String, dynamic> json) =>
      _$SwitchSlotRequestFromJson(json);
}

/// Request model for processing WebSocket updates
@freezed
abstract class ProcessWebSocketUpdateRequest with _$ProcessWebSocketUpdateRequest {
  const factory ProcessWebSocketUpdateRequest({
    required List<GameMatch> matches,
    required Map<String, List<GameMatch>> currentMatchesBySource,
    GameMatch? currentSelectedMatch,
  }) = _ProcessWebSocketUpdateRequest;

  factory ProcessWebSocketUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$ProcessWebSocketUpdateRequestFromJson(json);
}

/// Request model for setting up WebSocket listener
@freezed
abstract class SetupWebSocketListenerRequest with _$SetupWebSocketListenerRequest {
  const factory SetupWebSocketListenerRequest({
    Map<String, dynamic>? repositories,
  }) = _SetupWebSocketListenerRequest;

  factory SetupWebSocketListenerRequest.fromJson(Map<String, dynamic> json) =>
      _$SetupWebSocketListenerRequestFromJson(json);
}

/// Request model for evaluating network state
@freezed
abstract class EvaluateNetworkStateRequest with _$EvaluateNetworkStateRequest {
  const factory EvaluateNetworkStateRequest({
    required bool hasNetworkCapability,
    required bool isCreatingMatch,
    required List<PlayerSlot> playerSlots,
    GameMatch? selectedMatch,
  }) = _EvaluateNetworkStateRequest;

  factory EvaluateNetworkStateRequest.fromJson(Map<String, dynamic> json) =>
      _$EvaluateNetworkStateRequestFromJson(json);
}

/// Request model for cleaning up network matches
@freezed
abstract class CleanupNetworkMatchesRequest with _$CleanupNetworkMatchesRequest {
  const factory CleanupNetworkMatchesRequest({
    required Map<String, List<GameMatch>> matchesBySource,
  }) = _CleanupNetworkMatchesRequest;

  factory CleanupNetworkMatchesRequest.fromJson(Map<String, dynamic> json) =>
      _$CleanupNetworkMatchesRequestFromJson(json);
}

/// Request model for refreshing matches from a specific source
@freezed
abstract class RefreshMatchesFromSourceRequest with _$RefreshMatchesFromSourceRequest {
  const factory RefreshMatchesFromSourceRequest({
    required String sourceName,
    required Map<String, List<GameMatch>> currentMatchesBySource,
    String? gameName,
  }) = _RefreshMatchesFromSourceRequest;

  factory RefreshMatchesFromSourceRequest.fromJson(Map<String, dynamic> json) =>
      _$RefreshMatchesFromSourceRequestFromJson(json);
}
