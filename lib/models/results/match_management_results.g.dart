// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_management_results.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MatchDataResult _$MatchDataResultFromJson(Map<String, dynamic> json) =>
    _MatchDataResult(
      allMatches: (json['allMatches'] as List<dynamic>)
          .map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
          .toList(),
      matchesBySource: (json['matchesBySource'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
            k,
            (e as List<dynamic>)
                .map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
                .toList()),
      ),
      availableSources: (json['availableSources'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      hasNetworkCapability: json['hasNetworkCapability'] as bool? ?? false,
    );

Map<String, dynamic> _$MatchDataResultToJson(_MatchDataResult instance) =>
    <String, dynamic>{
      'allMatches': instance.allMatches,
      'matchesBySource': instance.matchesBySource,
      'availableSources': instance.availableSources,
      'hasNetworkCapability': instance.hasNetworkCapability,
    };

_PlayerSlotUpdateResult _$PlayerSlotUpdateResultFromJson(
        Map<String, dynamic> json) =>
    _PlayerSlotUpdateResult(
      updatedSlots: (json['updatedSlots'] as List<dynamic>)
          .map((e) => PlayerSlot.fromJson(e as Map<String, dynamic>))
          .toList(),
      requiresNetworkMatch: json['requiresNetworkMatch'] as bool,
      repositoryType: json['repositoryType'] as String?,
      updatedMatch: json['updatedMatch'] == null
          ? null
          : GameMatch.fromJson(json['updatedMatch'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PlayerSlotUpdateResultToJson(
        _PlayerSlotUpdateResult instance) =>
    <String, dynamic>{
      'updatedSlots': instance.updatedSlots,
      'requiresNetworkMatch': instance.requiresNetworkMatch,
      'repositoryType': instance.repositoryType,
      'updatedMatch': instance.updatedMatch,
    };

_RepositoryInitializationResult _$RepositoryInitializationResultFromJson(
        Map<String, dynamic> json) =>
    _RepositoryInitializationResult(
      availableSources: (json['availableSources'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      sourceCapabilities:
          Map<String, bool>.from(json['sourceCapabilities'] as Map),
      hasNetworkCapability: json['hasNetworkCapability'] as bool? ?? false,
    );

Map<String, dynamic> _$RepositoryInitializationResultToJson(
        _RepositoryInitializationResult instance) =>
    <String, dynamic>{
      'availableSources': instance.availableSources,
      'sourceCapabilities': instance.sourceCapabilities,
      'hasNetworkCapability': instance.hasNetworkCapability,
    };

_SourceRefreshResult _$SourceRefreshResultFromJson(Map<String, dynamic> json) =>
    _SourceRefreshResult(
      sourceName: json['sourceName'] as String,
      matches: (json['matches'] as List<dynamic>)
          .map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
          .toList(),
      isNetworkSource: json['isNetworkSource'] as bool? ?? false,
    );

Map<String, dynamic> _$SourceRefreshResultToJson(
        _SourceRefreshResult instance) =>
    <String, dynamic>{
      'sourceName': instance.sourceName,
      'matches': instance.matches,
      'isNetworkSource': instance.isNetworkSource,
    };

_MatchUpdateResult _$MatchUpdateResultFromJson(Map<String, dynamic> json) =>
    _MatchUpdateResult(
      updatedMatches: (json['updatedMatches'] as List<dynamic>)
          .map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
          .toList(),
      sourceName: json['sourceName'] as String,
      requiresStateUpdate: json['requiresStateUpdate'] as bool? ?? false,
    );

Map<String, dynamic> _$MatchUpdateResultToJson(_MatchUpdateResult instance) =>
    <String, dynamic>{
      'updatedMatches': instance.updatedMatches,
      'sourceName': instance.sourceName,
      'requiresStateUpdate': instance.requiresStateUpdate,
    };

_SubscriptionResult _$SubscriptionResultFromJson(Map<String, dynamic> json) =>
    _SubscriptionResult(
      subscribed: json['subscribed'] as bool? ?? false,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$SubscriptionResultToJson(_SubscriptionResult instance) =>
    <String, dynamic>{
      'subscribed': instance.subscribed,
      'message': instance.message,
    };

_UnsubscriptionResult _$UnsubscriptionResultFromJson(
        Map<String, dynamic> json) =>
    _UnsubscriptionResult(
      unsubscribed: json['unsubscribed'] as bool? ?? false,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$UnsubscriptionResultToJson(
        _UnsubscriptionResult instance) =>
    <String, dynamic>{
      'unsubscribed': instance.unsubscribed,
      'message': instance.message,
    };

_SourceRemovalResult _$SourceRemovalResultFromJson(Map<String, dynamic> json) =>
    _SourceRemovalResult(
      sourceRemoved: json['sourceRemoved'] as bool? ?? false,
      removedSourceName: json['removedSourceName'] as String?,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$SourceRemovalResultToJson(
        _SourceRemovalResult instance) =>
    <String, dynamic>{
      'sourceRemoved': instance.sourceRemoved,
      'removedSourceName': instance.removedSourceName,
      'message': instance.message,
    };

_MatchJoinResult _$MatchJoinResultFromJson(Map<String, dynamic> json) =>
    _MatchJoinResult(
      success: json['success'] as bool? ?? false,
      matchId: json['matchId'] as String?,
      sourceName: json['sourceName'] as String?,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$MatchJoinResultToJson(_MatchJoinResult instance) =>
    <String, dynamic>{
      'success': instance.success,
      'matchId': instance.matchId,
      'sourceName': instance.sourceName,
      'message': instance.message,
    };

_MatchLeaveResult _$MatchLeaveResultFromJson(Map<String, dynamic> json) =>
    _MatchLeaveResult(
      success: json['success'] as bool? ?? false,
      matchId: json['matchId'] as String?,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$MatchLeaveResultToJson(_MatchLeaveResult instance) =>
    <String, dynamic>{
      'success': instance.success,
      'matchId': instance.matchId,
      'message': instance.message,
    };

_FindMatchResult _$FindMatchResultFromJson(Map<String, dynamic> json) =>
    _FindMatchResult(
      match: json['match'] == null
          ? null
          : GameMatch.fromJson(json['match'] as Map<String, dynamic>),
      sourceName: json['sourceName'] as String?,
      found: json['found'] as bool? ?? false,
    );

Map<String, dynamic> _$FindMatchResultToJson(_FindMatchResult instance) =>
    <String, dynamic>{
      'match': instance.match,
      'sourceName': instance.sourceName,
      'found': instance.found,
    };

_SlotSwitchResult _$SlotSwitchResultFromJson(Map<String, dynamic> json) =>
    _SlotSwitchResult(
      updatedSlots: (json['updatedSlots'] as List<dynamic>)
          .map((e) => PlayerSlot.fromJson(e as Map<String, dynamic>))
          .toList(),
      success: json['success'] as bool? ?? false,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$SlotSwitchResultToJson(_SlotSwitchResult instance) =>
    <String, dynamic>{
      'updatedSlots': instance.updatedSlots,
      'success': instance.success,
      'message': instance.message,
    };

_WebSocketListenerResult _$WebSocketListenerResultFromJson(
        Map<String, dynamic> json) =>
    _WebSocketListenerResult(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String?,
      subscriptionId: json['subscriptionId'] as String?,
    );

Map<String, dynamic> _$WebSocketListenerResultToJson(
        _WebSocketListenerResult instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'subscriptionId': instance.subscriptionId,
    };

_WebSocketUpdateResult _$WebSocketUpdateResultFromJson(
        Map<String, dynamic> json) =>
    _WebSocketUpdateResult(
      updatedMatches: (json['updatedMatches'] as List<dynamic>)
          .map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
          .toList(),
      updatedMatchesBySource:
          (json['updatedMatchesBySource'] as Map<String, dynamic>).map(
        (k, e) => MapEntry(
            k,
            (e as List<dynamic>)
                .map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
                .toList()),
      ),
      updatedSelectedMatch: json['updatedSelectedMatch'] == null
          ? null
          : GameMatch.fromJson(
              json['updatedSelectedMatch'] as Map<String, dynamic>),
      requiresStateUpdate: json['requiresStateUpdate'] as bool? ?? false,
      networkSourceName: json['networkSourceName'] as String?,
    );

Map<String, dynamic> _$WebSocketUpdateResultToJson(
        _WebSocketUpdateResult instance) =>
    <String, dynamic>{
      'updatedMatches': instance.updatedMatches,
      'updatedMatchesBySource': instance.updatedMatchesBySource,
      'updatedSelectedMatch': instance.updatedSelectedMatch,
      'requiresStateUpdate': instance.requiresStateUpdate,
      'networkSourceName': instance.networkSourceName,
    };

_NetworkStateEvaluationResult _$NetworkStateEvaluationResultFromJson(
        Map<String, dynamic> json) =>
    _NetworkStateEvaluationResult(
      shouldClearMatchCreation:
          json['shouldClearMatchCreation'] as bool? ?? false,
      shouldClearSelectedMatch:
          json['shouldClearSelectedMatch'] as bool? ?? false,
      reason: json['reason'] as String?,
    );

Map<String, dynamic> _$NetworkStateEvaluationResultToJson(
        _NetworkStateEvaluationResult instance) =>
    <String, dynamic>{
      'shouldClearMatchCreation': instance.shouldClearMatchCreation,
      'shouldClearSelectedMatch': instance.shouldClearSelectedMatch,
      'reason': instance.reason,
    };

_ServerConnectionResult _$ServerConnectionResultFromJson(
        Map<String, dynamic> json) =>
    _ServerConnectionResult(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String?,
      scopeName: json['scopeName'] as String?,
    );

Map<String, dynamic> _$ServerConnectionResultToJson(
        _ServerConnectionResult instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'scopeName': instance.scopeName,
    };

_NetworkCleanupResult _$NetworkCleanupResultFromJson(
        Map<String, dynamic> json) =>
    _NetworkCleanupResult(
      success: json['success'] as bool? ?? false,
      removedMatchCount: (json['removedMatchCount'] as num?)?.toInt() ?? 0,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$NetworkCleanupResultToJson(
        _NetworkCleanupResult instance) =>
    <String, dynamic>{
      'success': instance.success,
      'removedMatchCount': instance.removedMatchCount,
      'message': instance.message,
    };
