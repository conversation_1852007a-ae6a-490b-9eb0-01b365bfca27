import 'package:common/models/game_match.dart';
import 'package:common/models/player_slot.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'match_management_results.freezed.dart';
part 'match_management_results.g.dart';

/// Result model for match data loading operations
@freezed
abstract class MatchDataResult with _$MatchDataResult {
  const factory MatchDataResult({
    required List<GameMatch> allMatches,
    required Map<String, List<GameMatch>> matchesBySource,
    required List<String> availableSources,
    @Default(false) bool hasNetworkCapability,
  }) = _MatchDataResult;

  factory MatchDataResult.fromJson(Map<String, dynamic> json) =>
      _$MatchDataResultFromJson(json);
}

/// Result model for player slot update operations
@freezed
abstract class PlayerSlotUpdateResult with _$PlayerSlotUpdateResult {
  const factory PlayerSlotUpdateResult({
    required List<PlayerSlot> updatedSlots,
    required bool requiresNetworkMatch,
    String? repositoryType,
    GameMatch? updatedMatch,
  }) = _PlayerSlotUpdateResult;

  factory PlayerSlotUpdateResult.fromJson(Map<String, dynamic> json) =>
      _$PlayerSlotUpdateResultFromJson(json);
}

/// Result model for repository initialization operations
@freezed
abstract class RepositoryInitializationResult with _$RepositoryInitializationResult {
  const factory RepositoryInitializationResult({
    required List<String> availableSources,
    required Map<String, bool> sourceCapabilities,
    @Default(false) bool hasNetworkCapability,
  }) = _RepositoryInitializationResult;

  factory RepositoryInitializationResult.fromJson(Map<String, dynamic> json) =>
      _$RepositoryInitializationResultFromJson(json);
}

/// Result model for match source refresh operations
@freezed
abstract class SourceRefreshResult with _$SourceRefreshResult {
  const factory SourceRefreshResult({
    required String sourceName,
    required List<GameMatch> matches,
    @Default(false) bool isNetworkSource,
  }) = _SourceRefreshResult;

  factory SourceRefreshResult.fromJson(Map<String, dynamic> json) =>
      _$SourceRefreshResultFromJson(json);
}

/// Result model for match update operations
@freezed
abstract class MatchUpdateResult with _$MatchUpdateResult {
  const factory MatchUpdateResult({
    required List<GameMatch> updatedMatches,
    required String sourceName,
    @Default(false) bool requiresStateUpdate,
  }) = _MatchUpdateResult;

  factory MatchUpdateResult.fromJson(Map<String, dynamic> json) =>
      _$MatchUpdateResultFromJson(json);
}

/// Result model for subscription operations
@freezed
abstract class SubscriptionResult with _$SubscriptionResult {
  const factory SubscriptionResult({
    @Default(false) bool subscribed,
    String? message,
  }) = _SubscriptionResult;

  factory SubscriptionResult.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionResultFromJson(json);
}

/// Result model for unsubscription operations
@freezed
abstract class UnsubscriptionResult with _$UnsubscriptionResult {
  const factory UnsubscriptionResult({
    @Default(false) bool unsubscribed,
    String? message,
  }) = _UnsubscriptionResult;

  factory UnsubscriptionResult.fromJson(Map<String, dynamic> json) =>
      _$UnsubscriptionResultFromJson(json);
}

/// Result model for match source removal operations
@freezed
abstract class SourceRemovalResult with _$SourceRemovalResult {
  const factory SourceRemovalResult({
    @Default(false) bool sourceRemoved,
    String? removedSourceName,
    String? message,
  }) = _SourceRemovalResult;

  factory SourceRemovalResult.fromJson(Map<String, dynamic> json) =>
      _$SourceRemovalResultFromJson(json);
}

/// Result model for match joining operations
@freezed
abstract class MatchJoinResult with _$MatchJoinResult {
  const factory MatchJoinResult({
    @Default(false) bool success,
    String? matchId,
    String? sourceName,
    String? message,
  }) = _MatchJoinResult;

  factory MatchJoinResult.fromJson(Map<String, dynamic> json) =>
      _$MatchJoinResultFromJson(json);
}

/// Result model for match leaving operations
@freezed
abstract class MatchLeaveResult with _$MatchLeaveResult {
  const factory MatchLeaveResult({
    @Default(false) bool success,
    String? matchId,
    String? message,
  }) = _MatchLeaveResult;

  factory MatchLeaveResult.fromJson(Map<String, dynamic> json) =>
      _$MatchLeaveResultFromJson(json);
}

/// Result model for finding a match across sources
@freezed
abstract class FindMatchResult with _$FindMatchResult {
  const factory FindMatchResult({
    GameMatch? match,
    String? sourceName,
    @Default(false) bool found,
  }) = _FindMatchResult;

  factory FindMatchResult.fromJson(Map<String, dynamic> json) =>
      _$FindMatchResultFromJson(json);
}

/// Result model for slot switching operations
@freezed
abstract class SlotSwitchResult with _$SlotSwitchResult {
  const factory SlotSwitchResult({
    required List<PlayerSlot> updatedSlots,
    @Default(false) bool success,
    String? message,
  }) = _SlotSwitchResult;

  factory SlotSwitchResult.fromJson(Map<String, dynamic> json) =>
      _$SlotSwitchResultFromJson(json);
}

/// Result model for WebSocket listener setup operations
@freezed
abstract class WebSocketListenerResult with _$WebSocketListenerResult {
  const factory WebSocketListenerResult({
    @Default(false) bool success,
    String? message,
    String? subscriptionId,
  }) = _WebSocketListenerResult;

  factory WebSocketListenerResult.fromJson(Map<String, dynamic> json) =>
      _$WebSocketListenerResultFromJson(json);
}

/// Result model for WebSocket update processing
@freezed
abstract class WebSocketUpdateResult with _$WebSocketUpdateResult {
  const factory WebSocketUpdateResult({
    required List<GameMatch> updatedMatches,
    required Map<String, List<GameMatch>> updatedMatchesBySource,
    GameMatch? updatedSelectedMatch,
    @Default(false) bool requiresStateUpdate,
    String? networkSourceName,
  }) = _WebSocketUpdateResult;

  factory WebSocketUpdateResult.fromJson(Map<String, dynamic> json) =>
      _$WebSocketUpdateResultFromJson(json);
}

/// Result model for network state evaluation
@freezed
abstract class NetworkStateEvaluationResult with _$NetworkStateEvaluationResult {
  const factory NetworkStateEvaluationResult({
    @Default(false) bool shouldClearMatchCreation,
    @Default(false) bool shouldClearSelectedMatch,
    String? reason,
  }) = _NetworkStateEvaluationResult;

  factory NetworkStateEvaluationResult.fromJson(Map<String, dynamic> json) =>
      _$NetworkStateEvaluationResultFromJson(json);
}

/// Result model for server connection operations
@freezed
abstract class ServerConnectionResult with _$ServerConnectionResult {
  const factory ServerConnectionResult({
    @Default(false) bool success,
    String? message,
    String? scopeName,
  }) = _ServerConnectionResult;

  factory ServerConnectionResult.fromJson(Map<String, dynamic> json) =>
      _$ServerConnectionResultFromJson(json);
}

/// Result model for network cleanup operations
@freezed
abstract class NetworkCleanupResult with _$NetworkCleanupResult {
  const factory NetworkCleanupResult({
    @Default(false) bool success,
    @Default(0) int removedMatchCount,
    String? message,
  }) = _NetworkCleanupResult;

  factory NetworkCleanupResult.fromJson(Map<String, dynamic> json) =>
      _$NetworkCleanupResultFromJson(json);
}
