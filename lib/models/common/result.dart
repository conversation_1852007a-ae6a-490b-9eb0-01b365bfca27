/// A generic result type for handling success and failure states
/// Follows the Either pattern for functional error handling
abstract class Result<T> {
  const Result();

  /// Create a successful result
  factory Result.success(T value) = Success<T>;

  /// Create a failure result
  factory Result.failure(String error) = Failure<T>;

  /// Check if this result represents a success
  bool get isSuccess => this is Success<T>;

  /// Check if this result represents a failure
  bool get isFailure => this is Failure<T>;

  /// Get the success value (throws if this is a failure)
  T get value {
    if (this is Success<T>) {
      return (this as Success<T>).value;
    }
    throw StateError('Cannot get value from a failure result');
  }

  /// Get the error message (throws if this is a success)
  String get error {
    if (this is Failure<T>) {
      return (this as Failure<T>).error;
    }
    throw StateError('Cannot get error from a success result');
  }

  /// Transform the success value if this is a success, otherwise return the failure
  Result<U> map<U>(U Function(T value) transform) {
    if (this is Success<T>) {
      try {
        return Result.success(transform((this as Success<T>).value));
      } catch (e) {
        return Result.failure('Transform failed: $e');
      }
    }
    return Result.failure((this as Failure<T>).error);
  }

  /// Transform the success value to another Result, flattening nested Results
  Result<U> flatMap<U>(Result<U> Function(T value) transform) {
    if (this is Success<T>) {
      try {
        return transform((this as Success<T>).value);
      } catch (e) {
        return Result.failure('Transform failed: $e');
      }
    }
    return Result.failure((this as Failure<T>).error);
  }

  /// Execute different functions based on success or failure
  U fold<U>(U Function(String error) onFailure, U Function(T value) onSuccess) {
    if (this is Success<T>) {
      return onSuccess((this as Success<T>).value);
    }
    return onFailure((this as Failure<T>).error);
  }

  /// Cast this result to a different type (useful for error propagation)
  Result<U> cast<U>() {
    if (this is Success<T>) {
      throw StateError('Cannot cast a success result to a different type');
    }
    return Result.failure((this as Failure<T>).error);
  }
}

/// Represents a successful result
class Success<T> extends Result<T> {
  final T value;

  const Success(this.value);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Success<T> && runtimeType == other.runtimeType && value == other.value;

  @override
  int get hashCode => value.hashCode;

  @override
  String toString() => 'Success($value)';
}

/// Represents a failed result
class Failure<T> extends Result<T> {
  final String error;

  const Failure(this.error);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Failure<T> && runtimeType == other.runtimeType && error == other.error;

  @override
  int get hashCode => error.hashCode;

  @override
  String toString() => 'Failure($error)';
}
