// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'match_management_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MatchManagementState {
// ========================================================================
// PROCESSING & ERROR STATE
// ========================================================================
  /// Overall processing status
  ProcessingStatus get processingStatus;

  /// Optional error message
  String?
      get errorMessage; // ========================================================================
// MATCH DISCOVERY & SELECTION STATE
// ========================================================================
  /// All open matches from all sources
  List<GameMatch> get openMatches;

  /// Matches organized by source name
  Map<String, List<GameMatch>> get matchesBySource;

  /// Available match sources (local, network, etc.)
  List<String> get availableMatchSources;

  /// Currently selected match for joining/editing
  GameMatch? get selectedMatch;

  /// Whether we are subscribed to real-time updates
  bool get isSubscribedToUpdates;

  /// Sources that are currently being monitored
  Set<String>
      get monitoredSources; // ========================================================================
// MATCH CREATION STATE
// ========================================================================
  /// Current mode: 'selection' or 'creation'
  String get currentMode;

  /// Available game configurations for match creation
  List<GameConfig> get availableConfigs;

  /// Currently selected match configuration ID for creation
  String? get selectedConfigId;

  /// Game name for the match being created
  String? get gameName;

  /// Match ID for the match being created/edited
  String? get matchId;

  /// Player slots for the match being created
  List<PlayerSlot> get playerSlots;

  /// Players list (legacy support)
  List<Player>
      get players; // ========================================================================
// NETWORK & CAPABILITY STATE
// ========================================================================
  /// Whether network match functionality is available
  bool
      get hasNetworkCapability; // ========================================================================
// UI STATE
// ========================================================================
  /// Whether server profile selector is expanded
  bool get serverProfileSelectorExpanded;

  /// Sources that should be removed by parent components
  Set<String> get sourcesToRemove;

  /// Create a copy of MatchManagementState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  $MatchManagementStateCopyWith<MatchManagementState> get copyWith =>
      _$MatchManagementStateCopyWithImpl<MatchManagementState>(
          this as MatchManagementState, _$identity);

  /// Serializes this MatchManagementState to a JSON map.
  Map<String, dynamic> toJson();

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is MatchManagementState &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality()
                .equals(other.openMatches, openMatches) &&
            const DeepCollectionEquality()
                .equals(other.matchesBySource, matchesBySource) &&
            const DeepCollectionEquality()
                .equals(other.availableMatchSources, availableMatchSources) &&
            (identical(other.selectedMatch, selectedMatch) ||
                other.selectedMatch == selectedMatch) &&
            (identical(other.isSubscribedToUpdates, isSubscribedToUpdates) ||
                other.isSubscribedToUpdates == isSubscribedToUpdates) &&
            const DeepCollectionEquality()
                .equals(other.monitoredSources, monitoredSources) &&
            (identical(other.currentMode, currentMode) ||
                other.currentMode == currentMode) &&
            const DeepCollectionEquality()
                .equals(other.availableConfigs, availableConfigs) &&
            (identical(other.selectedConfigId, selectedConfigId) ||
                other.selectedConfigId == selectedConfigId) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            const DeepCollectionEquality()
                .equals(other.playerSlots, playerSlots) &&
            const DeepCollectionEquality().equals(other.players, players) &&
            (identical(other.hasNetworkCapability, hasNetworkCapability) ||
                other.hasNetworkCapability == hasNetworkCapability) &&
            (identical(other.serverProfileSelectorExpanded,
                    serverProfileSelectorExpanded) ||
                other.serverProfileSelectorExpanded ==
                    serverProfileSelectorExpanded) &&
            const DeepCollectionEquality()
                .equals(other.sourcesToRemove, sourcesToRemove));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      processingStatus,
      errorMessage,
      const DeepCollectionEquality().hash(openMatches),
      const DeepCollectionEquality().hash(matchesBySource),
      const DeepCollectionEquality().hash(availableMatchSources),
      selectedMatch,
      isSubscribedToUpdates,
      const DeepCollectionEquality().hash(monitoredSources),
      currentMode,
      const DeepCollectionEquality().hash(availableConfigs),
      selectedConfigId,
      gameName,
      matchId,
      const DeepCollectionEquality().hash(playerSlots),
      const DeepCollectionEquality().hash(players),
      hasNetworkCapability,
      serverProfileSelectorExpanded,
      const DeepCollectionEquality().hash(sourcesToRemove));

  @override
  String toString() {
    return 'MatchManagementState(processingStatus: $processingStatus, errorMessage: $errorMessage, openMatches: $openMatches, matchesBySource: $matchesBySource, availableMatchSources: $availableMatchSources, selectedMatch: $selectedMatch, isSubscribedToUpdates: $isSubscribedToUpdates, monitoredSources: $monitoredSources, currentMode: $currentMode, availableConfigs: $availableConfigs, selectedConfigId: $selectedConfigId, gameName: $gameName, matchId: $matchId, playerSlots: $playerSlots, players: $players, hasNetworkCapability: $hasNetworkCapability, serverProfileSelectorExpanded: $serverProfileSelectorExpanded, sourcesToRemove: $sourcesToRemove)';
  }
}

/// @nodoc
abstract mixin class $MatchManagementStateCopyWith<$Res> {
  factory $MatchManagementStateCopyWith(MatchManagementState value,
          $Res Function(MatchManagementState) _then) =
      _$MatchManagementStateCopyWithImpl;
  @useResult
  $Res call(
      {ProcessingStatus processingStatus,
      String? errorMessage,
      List<GameMatch> openMatches,
      Map<String, List<GameMatch>> matchesBySource,
      List<String> availableMatchSources,
      GameMatch? selectedMatch,
      bool isSubscribedToUpdates,
      Set<String> monitoredSources,
      String currentMode,
      List<GameConfig> availableConfigs,
      String? selectedConfigId,
      String? gameName,
      String? matchId,
      List<PlayerSlot> playerSlots,
      List<Player> players,
      bool hasNetworkCapability,
      bool serverProfileSelectorExpanded,
      Set<String> sourcesToRemove});

  $GameMatchCopyWith<$Res>? get selectedMatch;
}

/// @nodoc
class _$MatchManagementStateCopyWithImpl<$Res>
    implements $MatchManagementStateCopyWith<$Res> {
  _$MatchManagementStateCopyWithImpl(this._self, this._then);

  final MatchManagementState _self;
  final $Res Function(MatchManagementState) _then;

  /// Create a copy of MatchManagementState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? processingStatus = null,
    Object? errorMessage = freezed,
    Object? openMatches = null,
    Object? matchesBySource = null,
    Object? availableMatchSources = null,
    Object? selectedMatch = freezed,
    Object? isSubscribedToUpdates = null,
    Object? monitoredSources = null,
    Object? currentMode = null,
    Object? availableConfigs = null,
    Object? selectedConfigId = freezed,
    Object? gameName = freezed,
    Object? matchId = freezed,
    Object? playerSlots = null,
    Object? players = null,
    Object? hasNetworkCapability = null,
    Object? serverProfileSelectorExpanded = null,
    Object? sourcesToRemove = null,
  }) {
    return _then(_self.copyWith(
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      openMatches: null == openMatches
          ? _self.openMatches
          : openMatches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      matchesBySource: null == matchesBySource
          ? _self.matchesBySource
          : matchesBySource // ignore: cast_nullable_to_non_nullable
              as Map<String, List<GameMatch>>,
      availableMatchSources: null == availableMatchSources
          ? _self.availableMatchSources
          : availableMatchSources // ignore: cast_nullable_to_non_nullable
              as List<String>,
      selectedMatch: freezed == selectedMatch
          ? _self.selectedMatch
          : selectedMatch // ignore: cast_nullable_to_non_nullable
              as GameMatch?,
      isSubscribedToUpdates: null == isSubscribedToUpdates
          ? _self.isSubscribedToUpdates
          : isSubscribedToUpdates // ignore: cast_nullable_to_non_nullable
              as bool,
      monitoredSources: null == monitoredSources
          ? _self.monitoredSources
          : monitoredSources // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      currentMode: null == currentMode
          ? _self.currentMode
          : currentMode // ignore: cast_nullable_to_non_nullable
              as String,
      availableConfigs: null == availableConfigs
          ? _self.availableConfigs
          : availableConfigs // ignore: cast_nullable_to_non_nullable
              as List<GameConfig>,
      selectedConfigId: freezed == selectedConfigId
          ? _self.selectedConfigId
          : selectedConfigId // ignore: cast_nullable_to_non_nullable
              as String?,
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
      playerSlots: null == playerSlots
          ? _self.playerSlots
          : playerSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      players: null == players
          ? _self.players
          : players // ignore: cast_nullable_to_non_nullable
              as List<Player>,
      hasNetworkCapability: null == hasNetworkCapability
          ? _self.hasNetworkCapability
          : hasNetworkCapability // ignore: cast_nullable_to_non_nullable
              as bool,
      serverProfileSelectorExpanded: null == serverProfileSelectorExpanded
          ? _self.serverProfileSelectorExpanded
          : serverProfileSelectorExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      sourcesToRemove: null == sourcesToRemove
          ? _self.sourcesToRemove
          : sourcesToRemove // ignore: cast_nullable_to_non_nullable
              as Set<String>,
    ));
  }

  /// Create a copy of MatchManagementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameMatchCopyWith<$Res>? get selectedMatch {
    if (_self.selectedMatch == null) {
      return null;
    }

    return $GameMatchCopyWith<$Res>(_self.selectedMatch!, (value) {
      return _then(_self.copyWith(selectedMatch: value));
    });
  }
}

/// @nodoc
@JsonSerializable()
class _MatchManagementState extends MatchManagementState {
  const _MatchManagementState(
      {this.processingStatus = ProcessingStatus.start,
      this.errorMessage,
      final List<GameMatch> openMatches = const [],
      final Map<String, List<GameMatch>> matchesBySource = const {},
      final List<String> availableMatchSources = const [],
      this.selectedMatch,
      this.isSubscribedToUpdates = false,
      final Set<String> monitoredSources = const {},
      this.currentMode = 'selection',
      final List<GameConfig> availableConfigs = const [],
      this.selectedConfigId,
      this.gameName,
      this.matchId,
      final List<PlayerSlot> playerSlots = const [],
      final List<Player> players = const [],
      this.hasNetworkCapability = false,
      this.serverProfileSelectorExpanded = false,
      final Set<String> sourcesToRemove = const {}})
      : _openMatches = openMatches,
        _matchesBySource = matchesBySource,
        _availableMatchSources = availableMatchSources,
        _monitoredSources = monitoredSources,
        _availableConfigs = availableConfigs,
        _playerSlots = playerSlots,
        _players = players,
        _sourcesToRemove = sourcesToRemove,
        super._();
  factory _MatchManagementState.fromJson(Map<String, dynamic> json) =>
      _$MatchManagementStateFromJson(json);

// ========================================================================
// PROCESSING & ERROR STATE
// ========================================================================
  /// Overall processing status
  @override
  @JsonKey()
  final ProcessingStatus processingStatus;

  /// Optional error message
  @override
  final String? errorMessage;
// ========================================================================
// MATCH DISCOVERY & SELECTION STATE
// ========================================================================
  /// All open matches from all sources
  final List<GameMatch> _openMatches;
// ========================================================================
// MATCH DISCOVERY & SELECTION STATE
// ========================================================================
  /// All open matches from all sources
  @override
  @JsonKey()
  List<GameMatch> get openMatches {
    if (_openMatches is EqualUnmodifiableListView) return _openMatches;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_openMatches);
  }

  /// Matches organized by source name
  final Map<String, List<GameMatch>> _matchesBySource;

  /// Matches organized by source name
  @override
  @JsonKey()
  Map<String, List<GameMatch>> get matchesBySource {
    if (_matchesBySource is EqualUnmodifiableMapView) return _matchesBySource;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_matchesBySource);
  }

  /// Available match sources (local, network, etc.)
  final List<String> _availableMatchSources;

  /// Available match sources (local, network, etc.)
  @override
  @JsonKey()
  List<String> get availableMatchSources {
    if (_availableMatchSources is EqualUnmodifiableListView)
      return _availableMatchSources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableMatchSources);
  }

  /// Currently selected match for joining/editing
  @override
  final GameMatch? selectedMatch;

  /// Whether we are subscribed to real-time updates
  @override
  @JsonKey()
  final bool isSubscribedToUpdates;

  /// Sources that are currently being monitored
  final Set<String> _monitoredSources;

  /// Sources that are currently being monitored
  @override
  @JsonKey()
  Set<String> get monitoredSources {
    if (_monitoredSources is EqualUnmodifiableSetView) return _monitoredSources;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_monitoredSources);
  }

// ========================================================================
// MATCH CREATION STATE
// ========================================================================
  /// Current mode: 'selection' or 'creation'
  @override
  @JsonKey()
  final String currentMode;

  /// Available game configurations for match creation
  final List<GameConfig> _availableConfigs;

  /// Available game configurations for match creation
  @override
  @JsonKey()
  List<GameConfig> get availableConfigs {
    if (_availableConfigs is EqualUnmodifiableListView)
      return _availableConfigs;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_availableConfigs);
  }

  /// Currently selected match configuration ID for creation
  @override
  final String? selectedConfigId;

  /// Game name for the match being created
  @override
  final String? gameName;

  /// Match ID for the match being created/edited
  @override
  final String? matchId;

  /// Player slots for the match being created
  final List<PlayerSlot> _playerSlots;

  /// Player slots for the match being created
  @override
  @JsonKey()
  List<PlayerSlot> get playerSlots {
    if (_playerSlots is EqualUnmodifiableListView) return _playerSlots;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_playerSlots);
  }

  /// Players list (legacy support)
  final List<Player> _players;

  /// Players list (legacy support)
  @override
  @JsonKey()
  List<Player> get players {
    if (_players is EqualUnmodifiableListView) return _players;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_players);
  }

// ========================================================================
// NETWORK & CAPABILITY STATE
// ========================================================================
  /// Whether network match functionality is available
  @override
  @JsonKey()
  final bool hasNetworkCapability;
// ========================================================================
// UI STATE
// ========================================================================
  /// Whether server profile selector is expanded
  @override
  @JsonKey()
  final bool serverProfileSelectorExpanded;

  /// Sources that should be removed by parent components
  final Set<String> _sourcesToRemove;

  /// Sources that should be removed by parent components
  @override
  @JsonKey()
  Set<String> get sourcesToRemove {
    if (_sourcesToRemove is EqualUnmodifiableSetView) return _sourcesToRemove;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableSetView(_sourcesToRemove);
  }

  /// Create a copy of MatchManagementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  @pragma('vm:prefer-inline')
  _$MatchManagementStateCopyWith<_MatchManagementState> get copyWith =>
      __$MatchManagementStateCopyWithImpl<_MatchManagementState>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$MatchManagementStateToJson(
      this,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _MatchManagementState &&
            (identical(other.processingStatus, processingStatus) ||
                other.processingStatus == processingStatus) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            const DeepCollectionEquality()
                .equals(other._openMatches, _openMatches) &&
            const DeepCollectionEquality()
                .equals(other._matchesBySource, _matchesBySource) &&
            const DeepCollectionEquality()
                .equals(other._availableMatchSources, _availableMatchSources) &&
            (identical(other.selectedMatch, selectedMatch) ||
                other.selectedMatch == selectedMatch) &&
            (identical(other.isSubscribedToUpdates, isSubscribedToUpdates) ||
                other.isSubscribedToUpdates == isSubscribedToUpdates) &&
            const DeepCollectionEquality()
                .equals(other._monitoredSources, _monitoredSources) &&
            (identical(other.currentMode, currentMode) ||
                other.currentMode == currentMode) &&
            const DeepCollectionEquality()
                .equals(other._availableConfigs, _availableConfigs) &&
            (identical(other.selectedConfigId, selectedConfigId) ||
                other.selectedConfigId == selectedConfigId) &&
            (identical(other.gameName, gameName) ||
                other.gameName == gameName) &&
            (identical(other.matchId, matchId) || other.matchId == matchId) &&
            const DeepCollectionEquality()
                .equals(other._playerSlots, _playerSlots) &&
            const DeepCollectionEquality().equals(other._players, _players) &&
            (identical(other.hasNetworkCapability, hasNetworkCapability) ||
                other.hasNetworkCapability == hasNetworkCapability) &&
            (identical(other.serverProfileSelectorExpanded,
                    serverProfileSelectorExpanded) ||
                other.serverProfileSelectorExpanded ==
                    serverProfileSelectorExpanded) &&
            const DeepCollectionEquality()
                .equals(other._sourcesToRemove, _sourcesToRemove));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      processingStatus,
      errorMessage,
      const DeepCollectionEquality().hash(_openMatches),
      const DeepCollectionEquality().hash(_matchesBySource),
      const DeepCollectionEquality().hash(_availableMatchSources),
      selectedMatch,
      isSubscribedToUpdates,
      const DeepCollectionEquality().hash(_monitoredSources),
      currentMode,
      const DeepCollectionEquality().hash(_availableConfigs),
      selectedConfigId,
      gameName,
      matchId,
      const DeepCollectionEquality().hash(_playerSlots),
      const DeepCollectionEquality().hash(_players),
      hasNetworkCapability,
      serverProfileSelectorExpanded,
      const DeepCollectionEquality().hash(_sourcesToRemove));

  @override
  String toString() {
    return 'MatchManagementState(processingStatus: $processingStatus, errorMessage: $errorMessage, openMatches: $openMatches, matchesBySource: $matchesBySource, availableMatchSources: $availableMatchSources, selectedMatch: $selectedMatch, isSubscribedToUpdates: $isSubscribedToUpdates, monitoredSources: $monitoredSources, currentMode: $currentMode, availableConfigs: $availableConfigs, selectedConfigId: $selectedConfigId, gameName: $gameName, matchId: $matchId, playerSlots: $playerSlots, players: $players, hasNetworkCapability: $hasNetworkCapability, serverProfileSelectorExpanded: $serverProfileSelectorExpanded, sourcesToRemove: $sourcesToRemove)';
  }
}

/// @nodoc
abstract mixin class _$MatchManagementStateCopyWith<$Res>
    implements $MatchManagementStateCopyWith<$Res> {
  factory _$MatchManagementStateCopyWith(_MatchManagementState value,
          $Res Function(_MatchManagementState) _then) =
      __$MatchManagementStateCopyWithImpl;
  @override
  @useResult
  $Res call(
      {ProcessingStatus processingStatus,
      String? errorMessage,
      List<GameMatch> openMatches,
      Map<String, List<GameMatch>> matchesBySource,
      List<String> availableMatchSources,
      GameMatch? selectedMatch,
      bool isSubscribedToUpdates,
      Set<String> monitoredSources,
      String currentMode,
      List<GameConfig> availableConfigs,
      String? selectedConfigId,
      String? gameName,
      String? matchId,
      List<PlayerSlot> playerSlots,
      List<Player> players,
      bool hasNetworkCapability,
      bool serverProfileSelectorExpanded,
      Set<String> sourcesToRemove});

  @override
  $GameMatchCopyWith<$Res>? get selectedMatch;
}

/// @nodoc
class __$MatchManagementStateCopyWithImpl<$Res>
    implements _$MatchManagementStateCopyWith<$Res> {
  __$MatchManagementStateCopyWithImpl(this._self, this._then);

  final _MatchManagementState _self;
  final $Res Function(_MatchManagementState) _then;

  /// Create a copy of MatchManagementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $Res call({
    Object? processingStatus = null,
    Object? errorMessage = freezed,
    Object? openMatches = null,
    Object? matchesBySource = null,
    Object? availableMatchSources = null,
    Object? selectedMatch = freezed,
    Object? isSubscribedToUpdates = null,
    Object? monitoredSources = null,
    Object? currentMode = null,
    Object? availableConfigs = null,
    Object? selectedConfigId = freezed,
    Object? gameName = freezed,
    Object? matchId = freezed,
    Object? playerSlots = null,
    Object? players = null,
    Object? hasNetworkCapability = null,
    Object? serverProfileSelectorExpanded = null,
    Object? sourcesToRemove = null,
  }) {
    return _then(_MatchManagementState(
      processingStatus: null == processingStatus
          ? _self.processingStatus
          : processingStatus // ignore: cast_nullable_to_non_nullable
              as ProcessingStatus,
      errorMessage: freezed == errorMessage
          ? _self.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      openMatches: null == openMatches
          ? _self._openMatches
          : openMatches // ignore: cast_nullable_to_non_nullable
              as List<GameMatch>,
      matchesBySource: null == matchesBySource
          ? _self._matchesBySource
          : matchesBySource // ignore: cast_nullable_to_non_nullable
              as Map<String, List<GameMatch>>,
      availableMatchSources: null == availableMatchSources
          ? _self._availableMatchSources
          : availableMatchSources // ignore: cast_nullable_to_non_nullable
              as List<String>,
      selectedMatch: freezed == selectedMatch
          ? _self.selectedMatch
          : selectedMatch // ignore: cast_nullable_to_non_nullable
              as GameMatch?,
      isSubscribedToUpdates: null == isSubscribedToUpdates
          ? _self.isSubscribedToUpdates
          : isSubscribedToUpdates // ignore: cast_nullable_to_non_nullable
              as bool,
      monitoredSources: null == monitoredSources
          ? _self._monitoredSources
          : monitoredSources // ignore: cast_nullable_to_non_nullable
              as Set<String>,
      currentMode: null == currentMode
          ? _self.currentMode
          : currentMode // ignore: cast_nullable_to_non_nullable
              as String,
      availableConfigs: null == availableConfigs
          ? _self._availableConfigs
          : availableConfigs // ignore: cast_nullable_to_non_nullable
              as List<GameConfig>,
      selectedConfigId: freezed == selectedConfigId
          ? _self.selectedConfigId
          : selectedConfigId // ignore: cast_nullable_to_non_nullable
              as String?,
      gameName: freezed == gameName
          ? _self.gameName
          : gameName // ignore: cast_nullable_to_non_nullable
              as String?,
      matchId: freezed == matchId
          ? _self.matchId
          : matchId // ignore: cast_nullable_to_non_nullable
              as String?,
      playerSlots: null == playerSlots
          ? _self._playerSlots
          : playerSlots // ignore: cast_nullable_to_non_nullable
              as List<PlayerSlot>,
      players: null == players
          ? _self._players
          : players // ignore: cast_nullable_to_non_nullable
              as List<Player>,
      hasNetworkCapability: null == hasNetworkCapability
          ? _self.hasNetworkCapability
          : hasNetworkCapability // ignore: cast_nullable_to_non_nullable
              as bool,
      serverProfileSelectorExpanded: null == serverProfileSelectorExpanded
          ? _self.serverProfileSelectorExpanded
          : serverProfileSelectorExpanded // ignore: cast_nullable_to_non_nullable
              as bool,
      sourcesToRemove: null == sourcesToRemove
          ? _self._sourcesToRemove
          : sourcesToRemove // ignore: cast_nullable_to_non_nullable
              as Set<String>,
    ));
  }

  /// Create a copy of MatchManagementState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $GameMatchCopyWith<$Res>? get selectedMatch {
    if (_self.selectedMatch == null) {
      return null;
    }

    return $GameMatchCopyWith<$Res>(_self.selectedMatch!, (value) {
      return _then(_self.copyWith(selectedMatch: value));
    });
  }
}

// dart format on
