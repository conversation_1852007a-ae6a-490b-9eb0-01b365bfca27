import 'package:common/models/game_match.dart';
import 'package:common/models/player_type.dart';
import 'package:equatable/equatable.dart';

/// Events for the consolidated MatchManagementBloc
/// Combines functionality from MatchSelectionBloc, CreateMatchBloc, and MatchSelectionEnvironmentManager
abstract class MatchManagementEvent extends Equatable {
  const MatchManagementEvent();

  @override
  List<Object?> get props => [];
}

// ============================================================================
// INITIALIZATION EVENTS
// ============================================================================

/// Initialize the bloc with configuration data and available sources
class InitializeMatchManagementEvent extends MatchManagementEvent {
  const InitializeMatchManagementEvent();
}

/// Load available match sources (local, network, etc.)
class LoadAvailableMatchSourcesEvent extends MatchManagementEvent {
  const LoadAvailableMatchSourcesEvent();
}

// ============================================================================
// MATCH DISCOVERY & SELECTION EVENTS
// ============================================================================

/// Load available matches from all sources
class LoadMatchDataEvent extends MatchManagementEvent {
  final String? gameName;
  
  const LoadMatchDataEvent({this.gameName});
  
  @override
  List<Object?> get props => [gameName];
}

/// Refresh matches from a specific source
class RefreshMatchesFromSourceEvent extends MatchManagementEvent {
  final String sourceName;
  
  const RefreshMatchesFromSourceEvent(this.sourceName);
  
  @override
  List<Object?> get props => [sourceName];
}

/// Select a match for joining or editing
class SelectMatchEvent extends MatchManagementEvent {
  final GameMatch match;
  
  const SelectMatchEvent(this.match);
  
  @override
  List<Object?> get props => [match];
}

/// Clear the currently selected match
class ClearSelectedMatchEvent extends MatchManagementEvent {
  final String? matchId;
  
  const ClearSelectedMatchEvent({this.matchId});
  
  @override
  List<Object?> get props => [matchId];
}

// ============================================================================
// MATCH JOINING EVENTS
// ============================================================================

/// Join the currently selected match
class JoinSelectedMatchEvent extends MatchManagementEvent {
  const JoinSelectedMatchEvent();
}

/// Join a specific match by ID
class JoinMatchEvent extends MatchManagementEvent {
  final String matchId;
  final String? playerId;
  
  const JoinMatchEvent(this.matchId, {this.playerId});
  
  @override
  List<Object?> get props => [matchId, playerId];
}

/// Leave a match
class LeaveMatchEvent extends MatchManagementEvent {
  final String matchId;
  final String? playerId;
  
  const LeaveMatchEvent(this.matchId, {this.playerId});
  
  @override
  List<Object?> get props => [matchId, playerId];
}

// ============================================================================
// MATCH CREATION EVENTS
// ============================================================================

/// Start creating a new match
class StartMatchCreationEvent extends MatchManagementEvent {
  const StartMatchCreationEvent();
}

/// Cancel match creation and return to selection
class CancelMatchCreationEvent extends MatchManagementEvent {
  const CancelMatchCreationEvent();
}

/// Select a match configuration template
class SelectMatchConfigEvent extends MatchManagementEvent {
  final String matchConfigId;
  
  const SelectMatchConfigEvent(this.matchConfigId);
  
  @override
  List<Object?> get props => [matchConfigId];
}

/// Update the game name for the match being created
class UpdateGameNameEvent extends MatchManagementEvent {
  final String gameName;
  
  const UpdateGameNameEvent(this.gameName);
  
  @override
  List<Object?> get props => [gameName];
}

// ============================================================================
// PLAYER SLOT MANAGEMENT EVENTS
// ============================================================================

/// Add a new player slot
class AddPlayerSlotEvent extends MatchManagementEvent {
  const AddPlayerSlotEvent();
}

/// Remove a player slot
class RemovePlayerSlotEvent extends MatchManagementEvent {
  final int slotIndex;
  
  const RemovePlayerSlotEvent(this.slotIndex);
  
  @override
  List<Object?> get props => [slotIndex];
}

/// Update a player's type (human, bot, network, etc.)
class UpdatePlayerTypeEvent extends MatchManagementEvent {
  final int slotIndex;
  final PlayerType playerType;

  const UpdatePlayerTypeEvent(this.slotIndex, this.playerType);

  @override
  List<Object?> get props => [slotIndex, playerType];
}

/// Update a player's type in the selected match (when joining existing matches)
class UpdateSelectedMatchPlayerTypeEvent extends MatchManagementEvent {
  final int slotIndex;
  final PlayerType playerType;

  const UpdateSelectedMatchPlayerTypeEvent(this.slotIndex, this.playerType);

  @override
  List<Object?> get props => [slotIndex, playerType];
}

/// Update a player's name
class UpdatePlayerNameEvent extends MatchManagementEvent {
  final int slotIndex;
  final String name;
  
  const UpdatePlayerNameEvent(this.slotIndex, this.name);
  
  @override
  List<Object?> get props => [slotIndex, name];
}

/// Set a player slot as the host player
class SetHostPlayerEvent extends MatchManagementEvent {
  final String slotId;
  
  const SetHostPlayerEvent(this.slotId);
  
  @override
  List<Object?> get props => [slotId];
}

/// Join the current user to a player slot
class JoinPlayerSlotEvent extends MatchManagementEvent {
  final int slotIndex;

  const JoinPlayerSlotEvent(this.slotIndex);

  @override
  List<Object?> get props => [slotIndex];
}

/// Join the current user to a specific slot in the selected match (with server update)
class JoinSelectedMatchSlotEvent extends MatchManagementEvent {
  final int slotIndex;

  const JoinSelectedMatchSlotEvent(this.slotIndex);

  @override
  List<Object?> get props => [slotIndex];
}

// ============================================================================
// MATCH LIFECYCLE EVENTS
// ============================================================================

/// Create and start a match with the current configuration
class CreateAndStartMatchEvent extends MatchManagementEvent {
  final bool openForJoining;
  
  const CreateAndStartMatchEvent({this.openForJoining = false});
  
  @override
  List<Object?> get props => [openForJoining];
}

/// Delete a match
class DeleteMatchEvent extends MatchManagementEvent {
  final String matchId;
  
  const DeleteMatchEvent(this.matchId);
  
  @override
  List<Object?> get props => [matchId];
}

// ============================================================================
// REAL-TIME MONITORING EVENTS
// ============================================================================

/// Subscribe to real-time match updates
class SubscribeToMatchUpdatesEvent extends MatchManagementEvent {
  final String? gameName;
  
  const SubscribeToMatchUpdatesEvent({this.gameName});
  
  @override
  List<Object?> get props => [gameName];
}

/// Unsubscribe from real-time match updates
class UnsubscribeFromMatchUpdatesEvent extends MatchManagementEvent {
  const UnsubscribeFromMatchUpdatesEvent();
}

// ============================================================================
// SOURCE MANAGEMENT EVENTS
// ============================================================================

/// Add a match source to monitoring
class AddMatchSourceEvent extends MatchManagementEvent {
  final String sourceName;
  
  const AddMatchSourceEvent(this.sourceName);
  
  @override
  List<Object?> get props => [sourceName];
}

/// Remove a match source from monitoring
class RemoveMatchSourceEvent extends MatchManagementEvent {
  final String sourceName;
  
  const RemoveMatchSourceEvent(this.sourceName);
  
  @override
  List<Object?> get props => [sourceName];
}

// ============================================================================
// NETWORK & CAPABILITY EVENTS
// ============================================================================

/// Handle changes in network capability availability
class NetworkCapabilityChangedEvent extends MatchManagementEvent {
  final bool hasNetworkCapability;
  
  const NetworkCapabilityChangedEvent(this.hasNetworkCapability);
  
  @override
  List<Object?> get props => [hasNetworkCapability];
}

// ============================================================================
// INTERNAL UPDATE EVENTS
// ============================================================================

/// Handle match updates from a specific source (internal event)
class HandleMatchUpdatesFromSourceEvent extends MatchManagementEvent {
  final String sourceName;
  final List<GameMatch> matches;

  const HandleMatchUpdatesFromSourceEvent(this.sourceName, this.matches);

  @override
  List<Object?> get props => [sourceName, matches];
}

/// Handle WebSocket match updates (internal event)
class HandleWebSocketMatchUpdateEvent extends MatchManagementEvent {
  final List<GameMatch> matches;

  const HandleWebSocketMatchUpdateEvent(this.matches);

  @override
  List<Object?> get props => [matches];
}

/// Server scope became available (internal event)
class ServerScopeAvailableEvent extends MatchManagementEvent {
  const ServerScopeAvailableEvent();
}

/// Server scope was lost (internal event)
class ServerScopeLostEvent extends MatchManagementEvent {
  const ServerScopeLostEvent();
}

// ============================================================================
// UI STATE EVENTS
// ============================================================================

/// Toggle server profile selector expansion
class ToggleServerProfileSelectorEvent extends MatchManagementEvent {
  final bool? expanded;

  const ToggleServerProfileSelectorEvent(this.expanded);

  @override
  List<Object?> get props => [expanded];
}
