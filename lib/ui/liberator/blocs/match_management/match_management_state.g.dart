// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'match_management_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MatchManagementState _$MatchManagementStateFromJson(
        Map<String, dynamic> json) =>
    _MatchManagementState(
      processingStatus: $enumDecodeNullable(
              _$ProcessingStatusEnumMap, json['processingStatus']) ??
          ProcessingStatus.start,
      errorMessage: json['errorMessage'] as String?,
      openMatches: (json['openMatches'] as List<dynamic>?)
              ?.map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      matchesBySource: (json['matchesBySource'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
                k,
                (e as List<dynamic>)
                    .map((e) => GameMatch.fromJson(e as Map<String, dynamic>))
                    .toList()),
          ) ??
          const {},
      availableMatchSources: (json['availableMatchSources'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      selectedMatch: json['selectedMatch'] == null
          ? null
          : GameMatch.fromJson(json['selectedMatch'] as Map<String, dynamic>),
      isSubscribedToUpdates: json['isSubscribedToUpdates'] as bool? ?? false,
      monitoredSources: (json['monitoredSources'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toSet() ??
          const {},
      currentMode: json['currentMode'] as String? ?? 'selection',
      availableConfigs: (json['availableConfigs'] as List<dynamic>?)
              ?.map((e) => GameConfig.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      selectedConfigId: json['selectedConfigId'] as String?,
      gameName: json['gameName'] as String?,
      matchId: json['matchId'] as String?,
      playerSlots: (json['playerSlots'] as List<dynamic>?)
              ?.map((e) => PlayerSlot.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      players: (json['players'] as List<dynamic>?)
              ?.map((e) => Player.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      hasNetworkCapability: json['hasNetworkCapability'] as bool? ?? false,
      serverProfileSelectorExpanded:
          json['serverProfileSelectorExpanded'] as bool? ?? false,
      sourcesToRemove: (json['sourcesToRemove'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toSet() ??
          const {},
    );

Map<String, dynamic> _$MatchManagementStateToJson(
        _MatchManagementState instance) =>
    <String, dynamic>{
      'processingStatus': _$ProcessingStatusEnumMap[instance.processingStatus]!,
      'errorMessage': instance.errorMessage,
      'openMatches': instance.openMatches,
      'matchesBySource': instance.matchesBySource,
      'availableMatchSources': instance.availableMatchSources,
      'selectedMatch': instance.selectedMatch,
      'isSubscribedToUpdates': instance.isSubscribedToUpdates,
      'monitoredSources': instance.monitoredSources.toList(),
      'currentMode': instance.currentMode,
      'availableConfigs': instance.availableConfigs,
      'selectedConfigId': instance.selectedConfigId,
      'gameName': instance.gameName,
      'matchId': instance.matchId,
      'playerSlots': instance.playerSlots,
      'players': instance.players,
      'hasNetworkCapability': instance.hasNetworkCapability,
      'serverProfileSelectorExpanded': instance.serverProfileSelectorExpanded,
      'sourcesToRemove': instance.sourcesToRemove.toList(),
    };

const _$ProcessingStatusEnumMap = {
  ProcessingStatus.start: 'start',
  ProcessingStatus.loading: 'loading',
  ProcessingStatus.loaded: 'loaded',
  ProcessingStatus.error: 'error',
};
