import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:common/models/game_match.dart';
import 'package:common/models/player.dart';
import 'package:common/models/player_slot.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';

part 'match_management_state.freezed.dart';
part 'match_management_state.g.dart';

/// Consolidated state for match management
/// Combines functionality from MatchSelectionBloc, CreateMatchBloc, and MatchSelectionEnvironmentManager
@freezed
abstract class MatchManagementState with _$MatchManagementState {
  // Private constructor required for freezed when using custom getters
  const MatchManagementState._();

  const factory MatchManagementState({
    // ========================================================================
    // PROCESSING & ERROR STATE
    // ========================================================================
    
    /// Overall processing status
    @Default(ProcessingStatus.start) ProcessingStatus processingStatus,
    
    /// Optional error message
    String? errorMessage,

    // ========================================================================
    // MATCH DISCOVERY & SELECTION STATE
    // ========================================================================
    
    /// All open matches from all sources
    @Default([]) List<GameMatch> openMatches,
    
    /// Matches organized by source name
    @Default({}) Map<String, List<GameMatch>> matchesBySource,
    
    /// Available match sources (local, network, etc.)
    @Default([]) List<String> availableMatchSources,
    
    /// Currently selected match for joining/editing
    GameMatch? selectedMatch,
    
    /// Whether we are subscribed to real-time updates
    @Default(false) bool isSubscribedToUpdates,
    
    /// Sources that are currently being monitored
    @Default({}) Set<String> monitoredSources,

    // ========================================================================
    // MATCH CREATION STATE
    // ========================================================================
    
    /// Current mode: 'selection' or 'creation'
    @Default('selection') String currentMode,
    
    /// Available game configurations for match creation
    @Default([]) List<GameConfig> availableConfigs,
    
    /// Currently selected match configuration ID for creation
    String? selectedConfigId,
    
    /// Game name for the match being created
    String? gameName,
    
    /// Match ID for the match being created/edited
    String? matchId,
    
    /// Player slots for the match being created
    @Default([]) List<PlayerSlot> playerSlots,
    
    /// Players list (legacy support)
    @Default([]) List<Player> players,

    // ========================================================================
    // NETWORK & CAPABILITY STATE
    // ========================================================================
    
    /// Whether network match functionality is available
    @Default(false) bool hasNetworkCapability,

    // ========================================================================
    // UI STATE
    // ========================================================================
    
    /// Whether server profile selector is expanded
    @Default(false) bool serverProfileSelectorExpanded,
    
    /// Sources that should be removed by parent components
    @Default({}) Set<String> sourcesToRemove,
  }) = _MatchManagementState;

  factory MatchManagementState.fromJson(Map<String, dynamic> json) => 
      _$MatchManagementStateFromJson(json);

  // ========================================================================
  // COMPUTED PROPERTIES
  // ========================================================================

  /// Get the currently selected match configuration
  GameConfig? get selectedConfig => selectedConfigId != null
      ? availableConfigs.firstWhere(
          (config) => config.id == selectedConfigId,
          orElse: () => availableConfigs.isNotEmpty
              ? availableConfigs.first
              : throw Exception('No match configurations available'),
        )
      : null;

  /// Get initial match state for game loading
  /// TODO: Implement when GameMatchState is available
  // GameMatchState? get initialMatchState => selectedConfig != null
  //     ? GameMatchState(
  //         matchConfig: _createMatchConfig(),
  //         userPlayerId: playerSlots.isNotEmpty
  //             ? (playerSlots.first.playerId ?? '')
  //             : '',
  //         turnCount: 0,
  //         hands: const {},
  //         resources: const {},
  //         loadedCardClasses: const {},
  //       )
  //     : null;

  /// Check if we're currently in match creation mode
  bool get isCreatingMatch => currentMode == 'creation';

  /// Check if we're currently in match selection mode
  bool get isSelectingMatch => currentMode == 'selection';

  /// Check if a match is currently selected
  bool get hasSelectedMatch => selectedMatch != null;

  /// Check if match creation is ready (has config and slots)
  bool get isReadyToCreateMatch => 
      selectedConfig != null && 
      playerSlots.isNotEmpty;

  /// Get total number of matches across all sources
  int get totalMatchCount => openMatches.length;

  /// Get number of active sources
  int get activeSourceCount => availableMatchSources.length;

  /// Check if any source supports real-time updates
  bool get hasRealTimeCapability => isSubscribedToUpdates;

  // ========================================================================
  // STATE TRANSFORMATION HELPERS
  // ========================================================================

  /// Set processing status to loading
  MatchManagementState setLoading() => copyWith(
    processingStatus: ProcessingStatus.loading,
    errorMessage: null,
  );

  /// Set processing status to loaded
  MatchManagementState setLoaded() => copyWith(
    processingStatus: ProcessingStatus.loaded,
    errorMessage: null,
  );

  /// Set error state with message
  MatchManagementState setError(String message) => copyWith(
    processingStatus: ProcessingStatus.error,
    errorMessage: message,
  );

  /// Clear error state
  MatchManagementState clearError() => copyWith(
    errorMessage: null,
  );

  /// Switch to match creation mode
  MatchManagementState enterCreationMode() => copyWith(
    currentMode: 'creation',
    selectedMatch: null,
  );

  /// Switch to match selection mode
  MatchManagementState enterSelectionMode() => copyWith(
    currentMode: 'selection',
  );

  // ========================================================================
  // PRIVATE HELPER METHODS
  // ========================================================================




}
