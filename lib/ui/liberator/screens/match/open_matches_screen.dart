import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/models/base/match_config.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_event.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';

class OpenMatchesScreen extends StatefulWidget {
  const OpenMatchesScreen({super.key});

  @override
  State<OpenMatchesScreen> createState() => _OpenMatchesScreenState();
}

class _OpenMatchesScreenState extends State<OpenMatchesScreen> {
  // We'll store match data with extra information that's not in the MatchConfig class
  final List<(MatchConfig, int, int)> _openMatches = [];  // (config, currentPlayers, maxPlayers)
  bool _isLoading = true;
  String? _selectedMatchId;
  
  @override
  void initState() {
    super.initState();
    _loadOpenMatches();
  }
  
  void _loadOpenMatches() async {
    setState(() {
      _isLoading = true;
    });
    
    // In a real implementation, this would fetch open matches from the server
    // Simulating network delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Sample data for demonstration
    setState(() {
      _openMatches.clear();
      _openMatches.addAll([
        (
          MatchConfig(
            matchId: 'match_1',
            gameId: 'game_1',
            name: 'Player 1\'s Match',
            selectedGameMode: GameMode.server,
            hostId: 'player_1',
          ),
          1, // current players
          4, // max players
        ),
        (
          MatchConfig(
            matchId: 'match_2',
            gameId: 'game_1',
            name: 'Player 2\'s Match',
            selectedGameMode: GameMode.server,
            hostId: 'player_2',
          ),
          2, // current players
          3, // max players
        ),
      ]);
      _isLoading = false;
    });
  }
  
  void _joinMatch((MatchConfig, int, int) matchData) {
    final match = matchData.$1;
    // Join the selected match - use MatchManagementBloc
    final matchManagementBloc = GetIt.I<MatchManagementBloc>();
    final user = GetIt.I<UserManager>().state.user;
    if (user?.id != null) {
      matchManagementBloc.add(JoinMatchEvent(match.matchId, playerId: user!.id));
    }
    
    // Navigate back
    // Navigator.of(context).pop();
    
    // Start the match (in a real app, this would happen after successful join)
    // GetIt.I<CommandCenterBloc>().add(TapStartMatchEvent());
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Open Matches'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadOpenMatches,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _openMatches.isEmpty
              ? const Center(child: Text('No open matches available'))
              : ListView.builder(
                  itemCount: _openMatches.length,
                  itemBuilder: (context, index) {
                    final matchData = _openMatches[index];
                    final match = matchData.$1;
                    final currentPlayers = matchData.$2;
                    final maxPlayers = matchData.$3;
                    
                    return Card(
                      margin: const EdgeInsets.symmetric(
                        vertical: 8.0,
                        horizontal: 16.0,
                      ),
                      child: ListTile(
                        title: Text(match.name ?? 'Unnamed Match'),
                        subtitle: Text(
                          'Players: $currentPlayers/$maxPlayers',
                        ),
                        trailing: ElevatedButton(
                          onPressed: () => _joinMatch(matchData),
                          child: const Text('Join'),
                        ),
                        onTap: () {
                          setState(() {
                            _selectedMatchId = match.matchId;
                          });
                        },
                        selected: _selectedMatchId == match.matchId,
                      ),
                    );
                  },
                ),
    );
  }
}
