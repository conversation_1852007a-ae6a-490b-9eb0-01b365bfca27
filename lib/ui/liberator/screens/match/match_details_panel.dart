import 'package:dauntless/models/base/game_match.dart';
import 'package:flutter/material.dart';

/// Panel that displays details for an existing match and actions to join or start it
class MatchDetailsPanel extends StatelessWidget {
  final GameMatch match;
  
  const MatchDetailsPanel({super.key, required this.match});

  @override
  Widget build(BuildContext context) {
    // Get match details
    final matchName = match.gameName ?? 'Match ${match.id}';
    final gameType = match.gameTypeId;
    final gameMode = 'Standard'; // TODO: Get from match configuration
    final playerCount = match.playerSlots.where((slot) => slot.playerId != null).length;
    final maxPlayers = match.playerSlots.length;

    // Check if the current user is the host
    final isHost = match.creatorId == 'current_user_id'; // In a real app, use actual user ID

    // Check if all required slots are filled - a match is ready when all player slots are filled
    final bool allSlotsAreFilled = playerCount >= maxPlayers;
    
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Match Configuration Section
            _MatchInfoCard(
              matchName: matchName,
              gameType: gameType,
              gameMode: gameMode,
              playerCount: playerCount,
              maxPlayers: maxPlayers,
              creatorId: match.creatorId,
              isHost: isHost,
            ),

            const SizedBox(height: 16),
            
            // Player list section
            _PlayerList(match: match, isHost: isHost),
            
            const SizedBox(height: 24),

            // Action button - either Join or Start Match depending on user role
            Center(
              child: isHost
                ? _StartMatchButton(
                    allSlotsAreFilled: allSlotsAreFilled,
                    onStartMatch: () => _startMatch(context),
                  )
                : _JoinMatchButton(
                    onJoinMatch: () => _joinMatch(context),
                  ),
            ),

            // Show status info below the button for hosts waiting for players
            if (isHost && !allSlotsAreFilled) ...[
              const SizedBox(height: 8),
              Center(
                child: Text(
                  'Waiting for players... ($playerCount/$maxPlayers)',
                  style: TextStyle(color: Colors.orange.shade800),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Start the match (for hosts)
  void _startMatch(BuildContext context) {
    // TODO: Implement start match functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Start match functionality not yet implemented')),
    );
  }

  /// Join the match (for non-hosts)
  void _joinMatch(BuildContext context) {
    // TODO: Implement join match functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Join match functionality not yet implemented')),
    );
  }
}

/// Card displaying match information
class _MatchInfoCard extends StatelessWidget {
  final String matchName;
  final String gameType;
  final String gameMode;
  final int playerCount;
  final int maxPlayers;
  final String creatorId;
  final bool isHost;

  const _MatchInfoCard({
    required this.matchName,
    required this.gameType,
    required this.gameMode,
    required this.playerCount,
    required this.maxPlayers,
    required this.creatorId,
    required this.isHost,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              matchName,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Game Type', style: TextStyle(color: Colors.grey)),
                      const SizedBox(height: 4),
                      Text(gameType, style: const TextStyle(fontWeight: FontWeight.w500)),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Game Mode', style: TextStyle(color: Colors.grey)),
                      const SizedBox(height: 4),
                      Text(gameMode, style: const TextStyle(fontWeight: FontWeight.w500)),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Players', style: TextStyle(color: Colors.grey)),
                      const SizedBox(height: 4),
                      Text('$playerCount / $maxPlayers', style: const TextStyle(fontWeight: FontWeight.w500)),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text('Host', style: TextStyle(color: Colors.grey)),
                      const SizedBox(height: 4),
                      Text(
                        isHost ? 'You (Host)' : creatorId,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}



class _MatchNameField extends StatelessWidget {
  final String matchName;

  const _MatchNameField({
    required this.matchName,
  });

  @override
  Widget build(BuildContext context) {
    return TextField(
      decoration: const InputDecoration(
        hintText: 'Enter match name',
        isDense: true,
        contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        border: OutlineInputBorder(),
      ),
      controller: TextEditingController(text: matchName),
      onChanged: (value) {
        // Update match name (would require API call in real app)
        // In a real implementation, here we would update the backend:
        // context.read<MatchCubit>().updateMatchName(match.id, value);
      },
    );
  }
}

class _GameTypeAndModeSection extends StatelessWidget {
  final String gameType;
  final String gameMode;

  const _GameTypeAndModeSection({
    required this.gameType,
    required this.gameMode,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Game Type', style: TextStyle(color: Colors.grey)),
              const SizedBox(height: 4),
              Text(gameType, style: const TextStyle(fontWeight: FontWeight.w500)),
            ],
          ),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Game Mode', style: TextStyle(color: Colors.grey)),
              const SizedBox(height: 4),
              Text(gameMode, style: const TextStyle(fontWeight: FontWeight.w500)),
            ],
          ),
        ),
      ],
    );
  }
}

class _PlayerCountAndHostDetailsSection extends StatelessWidget {
  final int playerCount;
  final int maxPlayers;
  final String creatorId;
  final bool isHost;

  const _PlayerCountAndHostDetailsSection({
    required this.playerCount,
    required this.maxPlayers,
    required this.creatorId,
    required this.isHost,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Players', style: TextStyle(color: Colors.grey)),
              const SizedBox(height: 4),
              Text('$playerCount / $maxPlayers', style: const TextStyle(fontWeight: FontWeight.w500)),
            ],
          ),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Host', style: TextStyle(color: Colors.grey)),
              const SizedBox(height: 4),
              Text(
                creatorId == 'current_user_id' ? 'You (Host)' : 'User $creatorId',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Player list widget
class _PlayerList extends StatelessWidget {
  final GameMatch match;
  final bool isHost;

  const _PlayerList({
    required this.match,
    required this.isHost,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Players', style: TextStyle(fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        
        // Player Slots List
        Container(
          height: 200,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: _PlayerSlotsList(
            match: match,
          ),
        ),
      ],
    );
  }
}

class _PlayerSlotsList extends StatelessWidget {
  final GameMatch match;

  const _PlayerSlotsList({
    required this.match,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: match.players.length,
      itemBuilder: (context, index) {
        final player = match.players[index];
        return ListTile(
          leading: Icon(
            Icons.person, 
            color: player.id == match.creatorId ? Colors.orange : Colors.blue
          ),
          title: Text(player.name ?? 'Unknown name'),
          subtitle: Text(player.id == match.creatorId ? 'Host' : 'Player ${index + 1}'),
        );
      },
    );
  }
}

/// Start match button widget
class _StartMatchButton extends StatelessWidget {
  final bool allSlotsAreFilled;
  final VoidCallback onStartMatch;

  const _StartMatchButton({
    required this.allSlotsAreFilled,
    required this.onStartMatch,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
        backgroundColor: allSlotsAreFilled ? Colors.green : Colors.grey.shade300,
      ),
      onPressed: allSlotsAreFilled ? onStartMatch : null,
      child: Text(
        'Start Match',
        style: TextStyle(
          fontSize: 16,
          color: allSlotsAreFilled ? Colors.white : Colors.grey.shade700,
        ),
      ),
    );
  }
}

/// Join match button widget
class _JoinMatchButton extends StatelessWidget {
  final VoidCallback onJoinMatch;

  const _JoinMatchButton({
    required this.onJoinMatch,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
      ),
      onPressed: onJoinMatch,
      child: const Text('Join Match!!!', style: TextStyle(fontSize: 16)),
    );
  }
}
