import 'package:common/models/player_slot.dart';
import 'package:common/models/player_class.dart';
import 'package:common/models/player_type.dart';
import 'package:common/models/game_match.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/frameworks/user/user_state.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_event.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_state.dart';
import 'package:dauntless/ui/widgets/image_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

/// Player slot item widget
class PlayerSlotItem extends StatelessWidget {
  final int index;
  final PlayerSlot slot;
  final GameMatch? selectedMatch;
  final bool isJoining;

  const PlayerSlotItem({
    super.key,
    required this.index,
    required this.slot,
    this.selectedMatch,
    this.isJoining = false,
  });

  @override
  Widget build(BuildContext context) {
    final isLocalPlayer = slot.type == PlayerType.humanLocal;
    final isJoinable = slot.type.isHuman && !isLocalPlayer;

    return BlocBuilder<MatchManagementBloc, MatchManagementState>(
      bloc: GetIt.I<MatchManagementBloc>(),
      builder: (context, state) {
        // Get player class with fallback to first available or default
        PlayerClass? playerClass;
        if (state.selectedConfig != null) {
          playerClass = state.selectedConfig!.getPlayerClass(slot.playerClassId);
          // If the specific class isn't found, use the first available one
          if (playerClass == null && state.selectedConfig!.playerClasses.isNotEmpty) {
            playerClass = state.selectedConfig!.playerClasses.first;
          }
        }

        // If still no player class, create a default one
        if (playerClass == null) {
          playerClass = const PlayerClass(
            id: 'default',
            name: 'Default Player',
            icon: null,
          );
        }
        return ListTile(
          leading: PlayerSlotLeading(
            slot: slot,
            playerClass: playerClass,
          ),
          title: isLocalPlayer
              ? TextField(
                  decoration: InputDecoration(
                    hintText: 'Enter player name',
                    border: InputBorder.none,
                    isDense: true,
                    contentPadding: EdgeInsets.zero,
                  ),
                  controller: TextEditingController(text: slot.name),
                  onSubmitted: (newName) {
                    if (newName.trim().isNotEmpty) {
                      context
                          .read<MatchManagementBloc>()
                          .add(UpdatePlayerNameEvent(index, newName));
                    }
                  },
                  onTapOutside: (event) {
                    // Hide keyboard when tapped outside
                    FocusManager.instance.primaryFocus?.unfocus();
                  },
                )
              : Text(slot.name ?? 'Unnamed Player'),
          subtitle: Text(playerClass.name),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (slot.playerId != null && slot.playerId!.isNotEmpty)
                _buildPlayerIdDisplay(slot.playerId!)
              else if (isJoinable && isJoining)
                _buildJoinMatchSlotButton(selectedMatch!, index)
              else if (isJoinable && !isJoining)
                _buildPlayerJoinButton(state, index),

              // Show player type dropdown for both creating and joining matches
              DropdownButton<PlayerType>(
                value: slot.type,
                underline: Container(height: 1, color: Colors.grey.shade300),
                onChanged: (newType) {
                  if (newType != null) {
                    if (isJoining) {
                      // When joining, update the selected match's player slots
                      context
                          .read<MatchManagementBloc>()
                          .add(UpdateSelectedMatchPlayerTypeEvent(index, newType));
                    } else {
                      // When creating, use the existing event
                      context
                          .read<MatchManagementBloc>()
                          .add(UpdatePlayerTypeEvent(index, newType));
                    }
                  }
                },
                items: _getAvailablePlayerTypes(state.hasNetworkCapability, isJoining, selectedMatch)
                    .map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        getPlayerTypeIcon(type),
                        const SizedBox(width: 8),
                        Text(getPlayerTypeDisplayName(type)),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPlayerJoinButton(MatchManagementState state, int index) => BlocBuilder<UserManager, UserState>(
      builder: (context, userState) {
        final String? currentUserId = userState.user?.id;
        final bool canJoin = currentUserId != null &&
                          currentUserId.isNotEmpty &&
                          (slot.playerId == null || slot.playerId!.isEmpty);

        return OutlinedButton(
          onPressed: canJoin
              ? () => context.read<MatchManagementBloc>().add(JoinPlayerSlotEvent(index))
              : null,
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.blue,
            side: BorderSide(color: canJoin ? Colors.blue : Colors.grey.shade300),
          ),
          child: const Text('JOIN'),
        );
      },
    );

  /// Get a display name for a player type
  String getPlayerTypeDisplayName(PlayerType type) {
    switch (type) {
      case PlayerType.humanLocal:
        return 'Human (Local)';
      case PlayerType.humanNetwork:
        return 'Human (Network)';
      case PlayerType.botLocal:
        return 'Bot (Local)';
      case PlayerType.botNetwork:
        return 'Bot (Network)';
    }
  }

  // Note: Player ID lookup is now handled by CreateMatchBloc.findPlayerIdForSlot method

  /// Build join button for match slots (when joining existing matches)
  Widget _buildJoinMatchSlotButton(GameMatch match, int slotIndex) {
    return BlocBuilder<UserManager, UserState>(
      builder: (context, userState) {
        return OutlinedButton(
          onPressed: () {
            final playerId = userState.user?.id;
            if (playerId != null) {
              // Use the new slot-specific join event for better slot control
              context.read<MatchManagementBloc>().add(JoinSelectedMatchSlotEvent(slotIndex));
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Please set a username first')),
              );
            }
          },
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.green,
            side: const BorderSide(color: Colors.green),
          ),
          child: const Text('JOIN SLOT'),
        );
      },
    );
  }

  /// Get available player types based on network capability and joining context
  List<PlayerType> _getAvailablePlayerTypes(bool hasNetworkCapability, bool isJoining, GameMatch? selectedMatch) {
    // Base types available based on network capability
    List<PlayerType> availableTypes;

    if (hasNetworkCapability) {
      availableTypes = [
        PlayerType.humanLocal,
        PlayerType.humanNetwork,
        PlayerType.botLocal,
        PlayerType.botNetwork,
      ];
    } else {
      availableTypes = [
        PlayerType.humanLocal,
        PlayerType.botLocal,
      ];
    }

    // When joining a match, ensure all player types from the selected match are available
    // This prevents dropdown errors when the match has network types but hasNetworkCapability is false
    if (isJoining && selectedMatch != null) {
      final matchPlayerTypes = selectedMatch.playerSlots.map((slot) => slot.type).toSet();

      // Add any missing player types from the selected match
      for (final type in matchPlayerTypes) {
        if (!availableTypes.contains(type)) {
          availableTypes.add(type);
        }
      }
    }

    // Always ensure the current slot's type is available to prevent dropdown assertion errors
    // This handles cases where server connection is lost while a network type is selected
    if (!availableTypes.contains(slot.type)) {
      availableTypes.add(slot.type);
    }

    return availableTypes;
  }
}

/// Player slot leading widget
class PlayerSlotLeading extends StatelessWidget {
  final PlayerSlot slot;
  final PlayerClass playerClass;

  const PlayerSlotLeading({
    super.key,
    required this.slot,
    required this.playerClass,
  });

  @override
  Widget build(BuildContext context) => SizedBox(
        width: 128,
        child: Row(
          children: [
            // HostLeadingButton(slot: slot),
            playerClass.icon != null
                ? CardIcon(iconBaseId: playerClass.icon, width: 64, height: 128)
                : getPlayerTypeIcon(slot.type),
            Text(playerClass.name)
          ],
        ),
      );
}

/// Builds a widget to display the player ID
Widget _buildPlayerIdDisplay(String playerId) {
  return Row(
    children: [
      const Icon(Icons.person, size: 14, color: Colors.blue),
      const SizedBox(width: 4),
      Text(
        playerId,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 12,
          color: Colors.blue,
        ),
      ),
    ],
  );
}

/// Host leading button widget
class HostLeadingButton extends StatelessWidget {
  final PlayerSlot slot;

  const HostLeadingButton({
    super.key,
    required this.slot,
  });

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<MatchManagementBloc, MatchManagementState>(
        builder: (context, state) {
          final isHost = true; //state.match?.creatorId == slot.id;
          return IconButton(
            icon: Icon(
              Icons.star,
              color: isHost ? Colors.orange : Colors.grey.withValues(alpha: 0.5),
            ),
            tooltip: isHost ? 'Current host player' : 'Set as host player',
            onPressed: () {
              context.read<MatchManagementBloc>().add(SetHostPlayerEvent(slot.id));
            },
          );
        },
      );
}

/// Get an icon for a player type
Widget getPlayerTypeIcon(PlayerType? type) {
  switch (type) {
    case PlayerType.humanLocal:
      return const Icon(Icons.person, color: Colors.blue);
    case PlayerType.humanNetwork:
      return const Icon(Icons.cloud_queue, color: Colors.indigo);
    case PlayerType.botLocal:
      return const Icon(Icons.smart_toy, color: Colors.orange);
    case PlayerType.botNetwork:
      return const Icon(Icons.cloud_queue, color: Colors.orange);
    default:
      return const Icon(Icons.person, color: Colors.grey);
  }
}
