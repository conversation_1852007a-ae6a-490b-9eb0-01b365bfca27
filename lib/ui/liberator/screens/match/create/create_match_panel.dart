import 'package:common/models/game_match.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_event.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_state.dart';
import 'package:dauntless/ui/liberator/screens/match/create/player_slots_section.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

/// Panel for creating new matches or editing existing matches with player slot configuration
class CreateMatchPanel extends StatelessWidget {
  const CreateMatchPanel({super.key});

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<MatchManagementBloc, MatchManagementState>(
        bloc: GetIt.I<MatchManagementBloc>(),
        builder: (context, state) {
          if (state.processingStatus == ProcessingStatus.loading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.errorMessage != null) {
            return _buildErrorState(errorMessage: state.errorMessage!);
          }

          // Show unified match panel based on current mode
          return _buildUnifiedMatchPanel(context, state);
        },
      );

  /// Build unified match panel that handles selection, creation, and joining
  Widget _buildUnifiedMatchPanel(BuildContext context, MatchManagementState state) {
    final panelMode = _determinePanelMode(state);

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context, panelMode),
            const SizedBox(height: 16),
            _buildContent(context, state, panelMode),
          ],
        ),
      ),
    );
  }

  /// Determine the current panel mode based on state
  _PanelMode _determinePanelMode(MatchManagementState state) {
    final isSelectingMatch = state.isSelectingMatch;
    final hasSelectedMatch = state.selectedMatch != null;

    if (hasSelectedMatch) {
      return _PanelMode.joining;
    } else if (!isSelectingMatch) {
      return _PanelMode.creating;
    } else {
      return _PanelMode.selecting;
    }
  }

  /// Build the header with title and optional back button
  Widget _buildHeader(BuildContext context, _PanelMode mode) {
    final showBackButton = mode != _PanelMode.selecting;
    final title = _getTitleForMode(mode);

    return Row(
      children: [
        if (showBackButton) ...[
          IconButton(
            onPressed: () => _handleBackButton(context, mode),
            icon: const Icon(Icons.arrow_back),
            tooltip: 'Back to Match Selection',
          ),
          const SizedBox(width: 8),
        ],
        Text(
          title,
          style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  /// Get the appropriate title for the current mode
  String _getTitleForMode(_PanelMode mode) {
    switch (mode) {
      case _PanelMode.joining:
        return 'Join Match';
      case _PanelMode.creating:
        return 'Create New Match';
      case _PanelMode.selecting:
        return 'Match Selection';
    }
  }

  /// Handle back button press based on current mode
  void _handleBackButton(BuildContext context, _PanelMode mode) {
    final bloc = context.read<MatchManagementBloc>();
    switch (mode) {
      case _PanelMode.joining:
        bloc.add(const ClearSelectedMatchEvent());
        break;
      case _PanelMode.creating:
        bloc.add(const CancelMatchCreationEvent());
        break;
      case _PanelMode.selecting:
        // No action needed for selecting mode
        break;
    }
  }

  /// Build the main content based on current mode
  Widget _buildContent(BuildContext context, MatchManagementState state, _PanelMode mode) {
    switch (mode) {
      case _PanelMode.selecting:
        return _buildSelectionContent(context);
      case _PanelMode.joining:
      case _PanelMode.creating:
        return _buildMatchConfigurationContent(context, state, mode);
    }
  }

  /// Build content for match selection mode
  Widget _buildSelectionContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('Select a match from the left panel to view details.'),
        const SizedBox(height: 16),
        ElevatedButton(
          onPressed: () {
            context.read<MatchManagementBloc>().add(const StartMatchCreationEvent());
          },
          child: const Text('Create New Match'),
        ),
      ],
    );
  }

  /// Build content for match configuration (creation or joining)
  Widget _buildMatchConfigurationContent(BuildContext context, MatchManagementState state, _PanelMode mode) {
    final isJoining = mode == _PanelMode.joining;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _MatchConfigurationCard(
          state: state,
          selectedMatch: state.selectedMatch,
          isJoining: isJoining,
        ),
        const SizedBox(height: 16),
        PlayerSlotsSection(
          selectedMatch: state.selectedMatch,
          isJoining: isJoining,
        ),
        const SizedBox(height: 24),
        _buildActionButtons(context, state, mode),
      ],
    );
  }

  /// Build action buttons based on current mode
  Widget _buildActionButtons(BuildContext context, MatchManagementState state, _PanelMode mode) {
    switch (mode) {
      case _PanelMode.creating:
        return _buildCreateMatchActions(context, state);
      case _PanelMode.joining:
      case _PanelMode.selecting:
      return const SizedBox.shrink();
    }
  }

  /// Build create match action buttons
  Widget _buildCreateMatchActions(BuildContext context, MatchManagementState state) {
    if (!state.isReadyToCreateMatch) {
      return const SizedBox.shrink();
    }

    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: [
        ElevatedButton(
          onPressed: () {
            context.read<MatchManagementBloc>().add(
              const CreateAndStartMatchEvent(openForJoining: false),
            );
          },
          child: const Text('Create & Start Match'),
        ),
        ElevatedButton(
          onPressed: () {
            context.read<MatchManagementBloc>().add(
              const CreateAndStartMatchEvent(openForJoining: true),
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
          ),
          child: const Text('Create & Open for Joining'),
        ),
      ],
    );
  }

  /// Error state widget
  Widget _buildErrorState({required String errorMessage}) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: Colors.red.shade400,
            size: 64,
          ),
          const SizedBox(height: 16),
          Text(
            'Error',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.red.shade400,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            errorMessage,
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey.shade600),
          ),
          const SizedBox(height: 16),
          // TODO: Add retry button when specific error handling is implemented
        ],
      ),
    );
  }
}

/// Enum to represent the different panel modes
enum _PanelMode {
  selecting,
  creating,
  joining,
}

/// Match configuration card widget
class _MatchConfigurationCard extends StatelessWidget {
  final MatchManagementState state;
  final GameMatch? selectedMatch;
  final bool isJoining;

  const _MatchConfigurationCard({
    required this.state,
    this.selectedMatch,
    this.isJoining = false,
  });

  @override
  Widget build(BuildContext context) {
    final gameType = _getGameType();
    final matchName = _getMatchName();

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              isJoining ? 'Match Information' : 'Match Configuration',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            _buildGameTypeField(context, gameType),
            const SizedBox(height: 16),
            _buildMatchNameField(context, matchName),
            if (isJoining && selectedMatch != null) ...[
              const SizedBox(height: 16),
              _buildMatchInfoSection(),
            ],
          ],
        ),
      ),
    );
  }

  /// Get the game type to display
  String? _getGameType() {
    if (isJoining && selectedMatch != null) {
      return selectedMatch!.gameTypeId;
    }

    // For creation mode, use selected config or first available
    if (state.availableConfigs.any((config) => config.id == state.selectedConfigId)) {
      return state.selectedConfigId;
    }

    return state.availableConfigs.isNotEmpty ? state.availableConfigs.first.id : null;
  }

  /// Get the match name to display
  String? _getMatchName() {
    if (isJoining && selectedMatch != null) {
      return selectedMatch!.gameName ?? 'Match ${selectedMatch!.id}';
    }
    return state.gameName;
  }

  /// Build the game type field (dropdown for creation, read-only for joining)
  Widget _buildGameTypeField(BuildContext context, String? gameType) {
    if (isJoining) {
      return TextFormField(
        initialValue: gameType ?? 'Unknown',
        decoration: const InputDecoration(
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          labelText: 'Game Type',
        ),
        readOnly: true,
      );
    }

    return DropdownButtonFormField<String>(
      value: gameType,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        labelText: 'Game Type',
      ),
      items: state.availableConfigs
          .map((config) => DropdownMenuItem(
                value: config.id,
                child: Text(config.name),
              ))
          .toList(),
      onChanged: (value) {
        if (value != null) {
          context.read<MatchManagementBloc>().add(SelectMatchConfigEvent(value));
        }
      },
    );
  }

  /// Build the match name field
  Widget _buildMatchNameField(BuildContext context, String? matchName) {
    return TextFormField(
      initialValue: matchName,
      decoration: const InputDecoration(
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        labelText: 'Match Name',
      ),
      readOnly: isJoining,
      onChanged: isJoining ? null : (value) {
        context.read<MatchManagementBloc>().add(UpdateGameNameEvent(value));
      },
    );
  }

  /// Build the additional match information section for joining mode
  Widget _buildMatchInfoSection() {
    final filledSlots = selectedMatch!.playerSlots.where((slot) => slot.playerId != null).length;
    final totalSlots = selectedMatch!.playerSlots.length;

    return Row(
      children: [
        Expanded(
          child: _buildInfoColumn('Host', selectedMatch!.creatorId),
        ),
        Expanded(
          child: _buildInfoColumn('Players', '$filledSlots / $totalSlots'),
        ),
      ],
    );
  }

  /// Build an information column with label and value
  Widget _buildInfoColumn(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey.shade600,
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}
