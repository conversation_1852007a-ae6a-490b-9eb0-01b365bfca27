import 'package:common/models/game_match.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_event.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_state.dart';
import 'package:dauntless/ui/liberator/screens/match/create/player_slot_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// Player slots section widget
class PlayerSlotsSection extends StatelessWidget {
  final GameMatch? selectedMatch;
  final bool isJoining;

  const PlayerSlotsSection({
    super.key,
    this.selectedMatch,
    this.isJoining = false,
  });

  @override
  Widget build(BuildContext context) =>
      BlocBuilder<MatchManagementBloc, MatchManagementState>(builder: (context, state) {
        // Use selected match slots when joining, otherwise use state slots
        final playerSlots = isJoining && selectedMatch != null
            ? selectedMatch!.playerSlots
            : state.playerSlots;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _PlayerSlotsHeader(
              state: state,
              selectedMatch: selectedMatch,
              isJoining: isJoining,
            ),
            const SizedBox(height: 8),

            // Player slots list
            Card(
              elevation: 0, // No shadow
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(color: Colors.grey.shade300),
              ),
              child: SizedBox(
                height: 200,
                child: ListView.builder(
                  itemCount: playerSlots.length,
                  itemBuilder: (context, index) =>
                      PlayerSlotItem(
                        index: index,
                        slot: playerSlots[index],
                        selectedMatch: selectedMatch,
                        isJoining: isJoining,
                      ),
                ),
              ),
            ),
          ],
        );
      });
}

/// Player slots header widget
class _PlayerSlotsHeader extends StatelessWidget {
  final MatchManagementState state;
  final GameMatch? selectedMatch;
  final bool isJoining;

  const _PlayerSlotsHeader({
    required this.state,
    this.selectedMatch,
    this.isJoining = false,
  });

  @override
  Widget build(BuildContext context) {
    final playerSlots = isJoining && selectedMatch != null
        ? selectedMatch!.playerSlots
        : state.playerSlots;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        const Text('Players', style: TextStyle(fontWeight: FontWeight.bold)),
        if (!isJoining) ...[
          // Only show add/remove controls when creating matches
          Row(
            children: [
              IconButton(
                onPressed: state.playerSlots.length > 1
                    ? () => context
                        .read<MatchManagementBloc>()
                        .add(RemovePlayerSlotEvent(state.playerSlots.length - 1))
                    : null,
                icon: const Icon(Icons.remove_circle_outline),
                tooltip: 'Remove player slot',
              ),
              Text('${state.playerSlots.length}'),
              IconButton(
                onPressed: state.playerSlots.length < 8 // Max 8 players
                    ? () => context
                        .read<MatchManagementBloc>()
                        .add(const AddPlayerSlotEvent())
                    : null,
                icon: const Icon(Icons.add_circle_outline),
                tooltip: 'Add player slot',
              ),
            ],
          ),
        ] else ...[
          // Show player count when joining
          Text(
            '${playerSlots.where((slot) => slot.playerId != null).length} / ${playerSlots.length}',
            style: const TextStyle(color: Colors.grey),
          ),
        ],
      ],
    );
  }
}

// Content moved to player_slot_item.dart
