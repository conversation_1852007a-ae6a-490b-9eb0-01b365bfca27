import 'package:dauntless/ui/blocs/theme/theme_bloc.dart';
import 'package:dauntless/ui/blocs/theme/theme_state.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_event.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_state.dart';
import 'package:dauntless/ui/liberator/screens/match/create/create_match_panel.dart';
import 'package:dauntless/ui/widgets/server_profile_selector.dart';
import 'package:dauntless/ui/widgets/user_name_editor.dart';
import 'package:dauntless/ui/liberator/screens/match/open_matches_panel.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

/// Unified screen for managing matches - both joining existing matches and creating new ones
/// Now uses the consolidated MatchManagementBloc instead of multiple separate BLoCs
class MatchManagementScreen extends StatelessWidget {
  const MatchManagementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: GetIt.I<MatchManagementBloc>()
        ..add(const InitializeMatchManagementEvent())
        ..add(const LoadMatchDataEvent())
        ..add(const SubscribeToMatchUpdatesEvent()),
      child: BlocBuilder<MatchManagementBloc, MatchManagementState>(
        builder: (context, state) {
          return BlocBuilder<ThemeBloc, ThemeState>(
            builder: (context, themeState) => Row(
              children: [
                // Left panel - Available matches list (fixed width)
                SizedBox(
                  width: 400, // Fixed width to prevent overflow
                  child: Column(
                    children: [
                      // Match sources sections
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: _buildMatchSourceSections(state),
                          ),
                        ),
                      ),
                      // Bottom controls
                      UserNameEditorDEV(),
                      NewServerProfileSourceSelector(),
                    ],
                  ),
                ),

                // Divider
                Container(
                  width: 1,
                  color: themeState.theme?.dividerColor ?? Colors.grey.shade600,
                ),

                // Right panel - Create or Edit match form (takes remaining space)
                Expanded(
                  child: CreateMatchPanel(),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Build match source sections from the consolidated state
  List<Widget> _buildMatchSourceSections(MatchManagementState state) {
    return state.availableMatchSources.map((sourceName) {
      final sourceMatches = state.matchesBySource[sourceName] ?? [];

      return OpenMatchesPanel(
        sourceName: sourceName,
        matches: sourceMatches,
        isLoading: state.processingStatus == ProcessingStatus.loading,
        errorMessage: state.errorMessage,
      );
    }).toList();
  }
}
