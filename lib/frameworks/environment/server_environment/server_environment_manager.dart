import 'dart:async';
import 'dart:io';
import 'package:dauntless/di/di.config.dart';
import 'package:dauntless/di/modules/network_modules.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_bloc.dart';
import 'package:dauntless/ui/liberator/blocs/match_management/match_management_event.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/server_environment_use_case.dart';
import 'package:dauntless/use_cases/server_notifications_use_case.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'server_environment_state.dart';

/// Events for ServerEnvironmentManager
abstract class ServerEnvironmentEvent {}

/// Initialize environment from config file
class InitializeEnvironmentEvent extends ServerEnvironmentEvent {}

class ReconnectWebSocket extends ServerEnvironmentEvent {}

/// Configure custom server URLs
class SetSelectedServerProfileEvent extends ServerEnvironmentEvent {
  final String? profileId;
  final bool forceConnection;

  SetSelectedServerProfileEvent(this.profileId, {this.forceConnection = false});
}

/// Test connection to server
class ConnectToServerEvent extends ServerEnvironmentEvent {}

class ServerEnvironmentManager
    extends Bloc<ServerEnvironmentEvent, ServerEnvironmentState> {
  final ServerEnvironmentUseCase _useCase;
  final ServerNotificationsUseCase _serverNotificationsUseCase;

  ServerEnvironmentManager(
    this._useCase,
    this._serverNotificationsUseCase,
  ) : super(const ServerEnvironmentState()) {
    on<InitializeEnvironmentEvent>(_onInitializeEnvironment);
    on<SetSelectedServerProfileEvent>(_onSetSelectedServerProfileEvent);
    on<ConnectToServerEvent>(_onConnectToServerEvent);
    on<ReconnectWebSocket>(_onReconnectWebSocket);
  }

  /// Initialize environment from config file
  FutureOr<void> _onInitializeEnvironment(
    InitializeEnvironmentEvent event,
    Emitter<ServerEnvironmentState> emit,
  ) async {
    print('ServerEnvironmentManager: Initializing environment...');
    emit(state.copyWith(processingStatus: ProcessingStatus.loading));
    try {
      final serverEnvironmentConfig =
          await _useCase.loadServerEnvironmentConfig();

      print('ServerEnvironmentManager: Loaded config with ${serverEnvironmentConfig.profiles.length} profiles');
      print('ServerEnvironmentManager: Selected profile: ${serverEnvironmentConfig.selectedProfileId}');

      emit(state.copyWith(
        profiles: serverEnvironmentConfig.profiles,
        processingStatus: ProcessingStatus.loaded,
        selectedProfileId: serverEnvironmentConfig.selectedProfileId,
      ));

      print('ServerEnvironmentManager: State updated with ${state.profiles.length} profiles');

      if (serverEnvironmentConfig.selectedProfileId != null) {
        // Use the pre-selected profile as-is (respect user's choice)
        print('ServerEnvironmentManager: Using pre-selected profile: ${serverEnvironmentConfig.selectedProfileId}');
        add(SetSelectedServerProfileEvent(
            serverEnvironmentConfig.selectedProfileId, forceConnection: true));
      } else if (serverEnvironmentConfig.profiles.isNotEmpty) {
        // Auto-connect to the first available profile if no profile is pre-selected
        final firstProfile = serverEnvironmentConfig.profiles.first;
        print('ServerEnvironmentManager: Auto-connecting to first available profile: ${firstProfile.name}');
        add(SetSelectedServerProfileEvent(firstProfile.name, forceConnection: true));
      } else {
        print('ServerEnvironmentManager: No server profiles available for auto-connection');
      }
    } catch (e) {
      // If loading fails, use default URLs
      emit(state.copyWith(
        processingStatus: ProcessingStatus.error,
      ));
      print('ServerEnvironmentManager: Failed to load server config: $e');
      debugPrint('Failed to load server config: $e');
    }
  }

  /// Configure custom server URLs
  FutureOr<void> _onSetSelectedServerProfileEvent(
    SetSelectedServerProfileEvent event,
    Emitter<ServerEnvironmentState> emit,
  ) async {
    // Only take action if the profile actually changed or if connection is forced
    if (state.selectedProfileId != event.profileId || event.forceConnection) {
      print(
          'ServerEnvironmentManager: Changing profile from ${state.selectedProfileId} to ${event.profileId}');

      if (GetIt.I.currentScopeName == staticServerConnected) {
        // Notify MatchManagementBloc that server scope is being lost
        try {
          GetIt.I<MatchManagementBloc>().onServerScopeLost();
        } catch (e) {
          // MatchManagementBloc might not be available yet, that's ok
          print('MatchManagementBloc not available for scope lost notification: $e');
        }

        await GetIt.I.popScope();
      }

      _serverNotificationsUseCase.disconnect();

      // Update state - if disconnecting (profileId is null), set status to start
      emit(state.copyWith(
        selectedProfileId: event.profileId,
        selectedProfileConnectionStatus: event.profileId != null
            ? ProcessingStatus.loading
            : ProcessingStatus.start,
        // Clear connection timestamps when disconnecting
        lastSuccessfulConnection: event.profileId != null
            ? state.lastSuccessfulConnection
            : null,
      ));

      if (event.profileId != null) {
        await GetIt.I.initServerConnectedScope();

        // Small delay to ensure scope is fully activated
        await Future.delayed(const Duration(milliseconds: 100));

        // Notify MatchManagementBloc that server scope is now available
        try {
          GetIt.I<MatchManagementBloc>().onServerScopeAvailable();
        } catch (e) {
          // MatchManagementBloc might not be available yet, that's ok
          print('MatchManagementBloc not available for scope notification: $e');
        }

        add(ConnectToServerEvent());
      }

      // Only save config if we have a writable config file or user-specific profiles
      // Don't create a writable config just to save the selected profile ID
      try {
        final writableConfigExists = await File('server_config.json').exists();
        if (writableConfigExists) {
          final updatedConfig = state.config.copyWith(
            selectedProfileId: event.profileId,
          );
          await _useCase.saveServerEnvironmentConfig(updatedConfig);
          print(
              'ServerEnvironmentManager: Saved selected profile ${event.profileId} to existing config');
        } else {
          print(
              'ServerEnvironmentManager: Skipping config save - using asset profiles, selected profile ${event.profileId} stored in memory only');
        }
      } catch (e) {
        print('ServerEnvironmentManager: Failed to save config: $e');
        // Don't fail the operation if save fails, just log the error
      }

      emit(state.copyWith(
          selectedProfileConnectionStatus: ProcessingStatus.loaded));
      // await GetIt.I.reRegisterDauntlessApi(selectedProfileId: event.profileId);
    }
  }

  /// Test connection to server
  FutureOr<void> _onConnectToServerEvent(
    ConnectToServerEvent event,
    Emitter<ServerEnvironmentState> emit,
  ) async {
    final now = DateTime.now();

    // Mark connection attempt
    emit(state.copyWith(
      lastConnectionAttempt: now,
      selectedProfileConnectionStatus: ProcessingStatus.loading,
    ));

    final isConnected = await _useCase.testConnection();
    if (!isConnected) {
      emit(state.copyWith(
        selectedProfileConnectionStatus: ProcessingStatus.error,
      ));
      return;
    }
    emit(state.copyWith(
      lastSuccessfulConnection: now,
      selectedProfileConnectionStatus: ProcessingStatus.loaded,
    ));

    // Also test WebSocket connection
    add(ReconnectWebSocket());
    try {
      GetIt.I<MatchManagementBloc>().add(const LoadMatchDataEvent());
    } catch (e) {
      // MatchManagementBloc might not be available yet, that's ok
      print('MatchManagementBloc not available for match loading: $e');
    }
  }

  /// Helper method to reconnect WebSocket with current configuration
  Future<void> _onReconnectWebSocket(
      ReconnectWebSocket event, Emitter<ServerEnvironmentState> emit) async {
    emit(state.copyWith(
        selectedProfileConnectionStatus: ProcessingStatus.loading));
    try {
      print(
          'ServerEnvironmentManager: Reconnecting WebSocket with profile ${state.selectedProfileId}');
      print(
          'ServerEnvironmentManager: Using WebSocket URL: ${state.profile?.wsUrl}');

      // First disconnect current WebSocket connection
      _serverNotificationsUseCase.disconnect();

      // Small delay to ensure disconnect completes
      await Future.delayed(const Duration(milliseconds: 200));

      final serverEnvironmentConfig = state.profile ??
          (await _useCase.loadServerEnvironmentConfig()).selected;
      final wsUrl = serverEnvironmentConfig?.wsUrl;
      if (wsUrl == null) {
        throw Exception('WebSocket URL is null');
      }

      // Now reconnect with current URL configuration
      try {
        await _serverNotificationsUseCase.connect();
      } catch (e) {
        emit(state.copyWith(
          selectedProfileConnectionStatus: ProcessingStatus.error,
          lastConnectionAttempt: DateTime.now(),
        ));
        print('ServerEnvironmentManager: Error reconnecting WebSocket: $e');
        return;
      }

      try {
        GetIt.I<MatchManagementBloc>().add(const LoadMatchDataEvent());
      } catch (e) {
        // MatchManagementBloc might not be available yet, that's ok
        print('MatchManagementBloc not available for match loading: $e');
      }

      // Update state to reflect successful connection
      emit(state.copyWith(
        selectedProfileConnectionStatus: ProcessingStatus.loaded,
        lastConnectionAttempt: DateTime.now(),
        lastSuccessfulConnection: DateTime.now(),
      ));

      print('ServerEnvironmentManager: WebSocket reconnection successful');
    } catch (e) {
      emit(state.copyWith(
        selectedProfileConnectionStatus: ProcessingStatus.error,
        lastConnectionAttempt: DateTime.now(),
      ));
      print('ServerEnvironmentManager: Error reconnecting WebSocket: $e');
    }
  }
}
