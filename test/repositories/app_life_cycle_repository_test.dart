import 'dart:async';
import 'package:dauntless/repositories/app_life_cycle_repository.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockRemoteLogger extends Mock implements RemoteLogger {}

void main() {
  // Ensure Flutter bindings are initialized for WidgetsBinding tests
  TestWidgetsFlutterBinding.ensureInitialized();

  group('AppLifeCycleRepository', () {
    late MockRemoteLogger mockLogger;
    late AppLifeCycleRepository repository;

    setUp(() {
      mockLogger = MockRemoteLogger();
      repository = AppLifeCycleRepository(mockLogger);
    });

    tearDown(() {
      // Clean up is handled by the repository itself
      // We don't have direct access to the private stream controller
    });

    group('Constructor and Initialization', () {
      test('should initialize with logger dependency', () {
        expect(repository, isNotNull);
        expect(repository, isA<AppLifeCycleRepository>());
        expect(repository, isA<WidgetsBindingObserver>());
      });

      test('should create repository with logger', () {
        final logger = MockRemoteLogger();
        final repo = AppLifeCycleRepository(logger);
        
        expect(repo, isNotNull);
        expect(repo, isA<AppLifeCycleRepository>());
      });

      test('should initialize stream controller', () {
        expect(repository.appLifeCycleStream, isNotNull);
        expect(repository.appLifeCycleStream, isA<Stream<AppLifecycleState>>());
      });

      test('should have null initial lifecycle state', () {
        expect(repository.appLifecycleState, isNull);
      });

      test('should not be foregrounded initially', () {
        expect(repository.isForegrounded, isFalse);
      });
    });

    group('Initialization', () {
      test('should complete init without errors', () async {
        await expectLater(repository.init(), completes);
      });

      test('should set initial lifecycle state during init', () async {
        await repository.init();

        // After init, the lifecycle state might be null in test environment
        // This is acceptable as WidgetsBinding.instance.lifecycleState can be null
        expect(() => repository.appLifecycleState, returnsNormally);
      });

      test('should register as observer during init', () async {
        // The repository should be registered as an observer
        // We can't directly test this, but init should complete successfully
        await expectLater(repository.init(), completes);
      });
    });

    group('Lifecycle State Management', () {
      test('should update state when didChangeAppLifecycleState is called', () {
        // Arrange
        const newState = AppLifecycleState.paused;
        
        // Act
        repository.didChangeAppLifecycleState(newState);
        
        // Assert
        expect(repository.appLifecycleState, equals(newState));
      });

      test('should log state changes', () {
        // Arrange
        const newState = AppLifecycleState.resumed;
        
        // Act
        repository.didChangeAppLifecycleState(newState);
        
        // Assert
        verify(() => mockLogger.info('didChangeAppLifecycleState: $newState')).called(1);
      });

      test('should emit state changes to stream', () async {
        // Arrange
        const newState = AppLifecycleState.inactive;
        final streamFuture = repository.appLifeCycleStream.first;
        
        // Act
        repository.didChangeAppLifecycleState(newState);
        
        // Assert
        final emittedState = await streamFuture;
        expect(emittedState, equals(newState));
      });

      test('should handle multiple state changes', () async {
        // Arrange
        final states = [
          AppLifecycleState.resumed,
          AppLifecycleState.inactive,
          AppLifecycleState.paused,
          AppLifecycleState.detached,
        ];
        final streamStates = <AppLifecycleState>[];
        
        // Listen to stream
        final subscription = repository.appLifeCycleStream.listen((state) {
          streamStates.add(state);
        });
        
        // Act
        for (final state in states) {
          repository.didChangeAppLifecycleState(state);
          await Future.delayed(Duration(milliseconds: 1)); // Allow stream to process
        }
        
        // Assert
        expect(repository.appLifecycleState, equals(AppLifecycleState.detached));
        expect(streamStates, equals(states));
        
        await subscription.cancel();
      });
    });

    group('Manual State Setting', () {
      test('should set state manually', () {
        // Arrange
        const manualState = AppLifecycleState.resumed;
        
        // Act
        repository.manuallySetState(manualState);
        
        // Assert
        expect(repository.appLifecycleState, equals(manualState));
      });

      test('should log manual state changes', () {
        // Arrange
        const manualState = AppLifecycleState.paused;
        
        // Act
        repository.manuallySetState(manualState);
        
        // Assert
        verify(() => mockLogger.info('Manually setting AppLifecycleState to: $manualState')).called(1);
      });

      test('should emit manual state changes to stream', () async {
        // Arrange
        const manualState = AppLifecycleState.inactive;
        final streamFuture = repository.appLifeCycleStream.first;
        
        // Act
        repository.manuallySetState(manualState);
        
        // Assert
        final emittedState = await streamFuture;
        expect(emittedState, equals(manualState));
      });

      test('should handle all lifecycle states manually', () {
        final states = AppLifecycleState.values;
        
        for (final state in states) {
          repository.manuallySetState(state);
          expect(repository.appLifecycleState, equals(state));
        }
        
        verify(() => mockLogger.info(any())).called(states.length);
      });
    });

    group('Foreground Detection', () {
      test('should be foregrounded when resumed', () {
        // Act
        repository.manuallySetState(AppLifecycleState.resumed);
        
        // Assert
        expect(repository.isForegrounded, isTrue);
      });

      test('should not be foregrounded when paused', () {
        // Act
        repository.manuallySetState(AppLifecycleState.paused);
        
        // Assert
        expect(repository.isForegrounded, isFalse);
      });

      test('should not be foregrounded when inactive', () {
        // Act
        repository.manuallySetState(AppLifecycleState.inactive);
        
        // Assert
        expect(repository.isForegrounded, isFalse);
      });

      test('should not be foregrounded when detached', () {
        // Act
        repository.manuallySetState(AppLifecycleState.detached);
        
        // Assert
        expect(repository.isForegrounded, isFalse);
      });

      test('should update foreground status with state changes', () {
        // Initially not foregrounded
        expect(repository.isForegrounded, isFalse);
        
        // Set to resumed
        repository.manuallySetState(AppLifecycleState.resumed);
        expect(repository.isForegrounded, isTrue);
        
        // Set to paused
        repository.manuallySetState(AppLifecycleState.paused);
        expect(repository.isForegrounded, isFalse);
        
        // Set back to resumed
        repository.manuallySetState(AppLifecycleState.resumed);
        expect(repository.isForegrounded, isTrue);
      });
    });

    group('Stream Broadcasting', () {
      test('should support multiple stream listeners', () async {
        // Arrange
        final listener1States = <AppLifecycleState>[];
        final listener2States = <AppLifecycleState>[];
        
        final subscription1 = repository.appLifeCycleStream.listen((state) {
          listener1States.add(state);
        });
        
        final subscription2 = repository.appLifeCycleStream.listen((state) {
          listener2States.add(state);
        });
        
        // Act
        repository.manuallySetState(AppLifecycleState.resumed);
        repository.manuallySetState(AppLifecycleState.paused);
        
        await Future.delayed(Duration(milliseconds: 10)); // Allow streams to process
        
        // Assert
        expect(listener1States, equals([AppLifecycleState.resumed, AppLifecycleState.paused]));
        expect(listener2States, equals([AppLifecycleState.resumed, AppLifecycleState.paused]));
        
        await subscription1.cancel();
        await subscription2.cancel();
      });

      test('should handle stream listener errors gracefully', () async {
        // Arrange
        var errorCount = 0;
        late StreamSubscription subscription;

        subscription = repository.appLifeCycleStream.listen(
          (state) {
            // Don't throw in the listener as it would be uncaught
            // Instead, just track that we received the state
          },
          onError: (error) {
            errorCount++;
          },
        );

        // Act
        repository.manuallySetState(AppLifecycleState.resumed);
        await Future.delayed(Duration(milliseconds: 10));

        // Assert - Repository should continue working
        expect(repository.appLifecycleState, equals(AppLifecycleState.resumed));

        await subscription.cancel();
      });

      test('should continue broadcasting after listener cancellation', () async {
        // Arrange
        final states = <AppLifecycleState>[];
        final subscription = repository.appLifeCycleStream.listen((state) {
          states.add(state);
        });

        // Act
        repository.manuallySetState(AppLifecycleState.resumed);
        await Future.delayed(Duration(milliseconds: 10)); // Allow state to be captured
        await subscription.cancel();
        repository.manuallySetState(AppLifecycleState.paused);

        await Future.delayed(Duration(milliseconds: 10));

        // Assert - Only the first state should be captured
        expect(states, equals([AppLifecycleState.resumed]));
        expect(repository.appLifecycleState, equals(AppLifecycleState.paused));
      });
    });

    group('Error Handling', () {
      test('should handle logger errors gracefully', () {
        // Arrange
        when(() => mockLogger.info(any())).thenThrow(Exception('Logger error'));

        // Act & Assert - The repository doesn't catch logger errors, so they will propagate
        // This is actually the expected behavior - logging errors should be visible
        expect(() => repository.manuallySetState(AppLifecycleState.resumed), throwsException);

        // The state won't be set because the exception occurs during the logging call
        // which happens before the state assignment in the current implementation
        expect(repository.appLifecycleState, isNull);
      });

      test('should handle init errors gracefully', () async {
        // The init method should handle any potential errors gracefully
        await expectLater(repository.init(), completes);
      });

      test('should handle rapid state changes', () {
        // Act - Rapid state changes should not cause issues
        expect(() {
          for (int i = 0; i < 100; i++) {
            final state = AppLifecycleState.values[i % AppLifecycleState.values.length];
            repository.manuallySetState(state);
          }
        }, returnsNormally);
      });
    });

    group('Integration Scenarios', () {
      test('should handle complete lifecycle workflow', () async {
        // Arrange
        final capturedStates = <AppLifecycleState>[];
        final subscription = repository.appLifeCycleStream.listen((state) {
          capturedStates.add(state);
        });
        
        // Act - Simulate complete app lifecycle
        await repository.init();
        
        repository.didChangeAppLifecycleState(AppLifecycleState.resumed);
        expect(repository.isForegrounded, isTrue);
        
        repository.didChangeAppLifecycleState(AppLifecycleState.inactive);
        expect(repository.isForegrounded, isFalse);
        
        repository.didChangeAppLifecycleState(AppLifecycleState.paused);
        expect(repository.isForegrounded, isFalse);
        
        repository.didChangeAppLifecycleState(AppLifecycleState.resumed);
        expect(repository.isForegrounded, isTrue);
        
        await Future.delayed(Duration(milliseconds: 10));
        
        // Assert
        expect(capturedStates, contains(AppLifecycleState.resumed));
        expect(capturedStates, contains(AppLifecycleState.inactive));
        expect(capturedStates, contains(AppLifecycleState.paused));
        
        await subscription.cancel();
      });

      test('should handle Chrome extension manual override scenario', () async {
        // Arrange
        final capturedStates = <AppLifecycleState>[];
        final subscription = repository.appLifeCycleStream.listen((state) {
          capturedStates.add(state);
        });
        
        // Act - Simulate Chrome extension manually setting state
        repository.manuallySetState(AppLifecycleState.resumed);
        repository.manuallySetState(AppLifecycleState.paused);
        
        await Future.delayed(Duration(milliseconds: 10));
        
        // Assert
        expect(repository.appLifecycleState, equals(AppLifecycleState.paused));
        expect(capturedStates, equals([AppLifecycleState.resumed, AppLifecycleState.paused]));
        
        verify(() => mockLogger.info('Manually setting AppLifecycleState to: ${AppLifecycleState.resumed}')).called(1);
        verify(() => mockLogger.info('Manually setting AppLifecycleState to: ${AppLifecycleState.paused}')).called(1);
        
        await subscription.cancel();
      });

      test('should handle mixed manual and automatic state changes', () async {
        // Arrange
        final capturedStates = <AppLifecycleState>[];
        final subscription = repository.appLifeCycleStream.listen((state) {
          capturedStates.add(state);
        });
        
        // Act - Mix manual and automatic state changes
        repository.manuallySetState(AppLifecycleState.resumed);
        repository.didChangeAppLifecycleState(AppLifecycleState.inactive);
        repository.manuallySetState(AppLifecycleState.paused);
        repository.didChangeAppLifecycleState(AppLifecycleState.resumed);
        
        await Future.delayed(Duration(milliseconds: 10));
        
        // Assert
        expect(repository.appLifecycleState, equals(AppLifecycleState.resumed));
        expect(capturedStates, hasLength(4));
        expect(repository.isForegrounded, isTrue);
        
        await subscription.cancel();
      });
    });
  });
}
