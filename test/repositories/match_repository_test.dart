import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/targeted_action.dart';
import 'package:dauntless/repositories/match_repository.dart';
import 'package:dauntless/use_cases/players_use_case.dart';
import 'package:test/test.dart';

void main() {
  group('MatchRepository', () {
    late MatchRepository repository;

    setUp(() {
      repository = MatchRepository();
    });

    group('Constructor and Initial State', () {
      test('should initialize with empty submitted turns post state', () {
        expect(repository.submittedActiveTurnsPostState, isEmpty);
      });

      test('should initialize with empty submitted active actions', () {
        expect(repository.submittedActiveActions, isEmpty);
      });
    });

    group('Player Turn Post State Management', () {
      test('should submit player turn post state successfully', () {
        // Arrange
        const playerId = 'player1';
        final playerHand = <GameCard>[];

        // Act
        repository.submitPlayerTurnPostState(playerId, playerHand);

        // Assert
        expect(repository.submittedActiveTurnsPostState, containsPair(playerId, playerHand));
        expect(repository.submittedActiveTurnsPostState.length, equals(1));
      });

      test('should allow overwriting player turn post state', () {
        // Arrange
        const playerId = 'player1';
        final firstHand = <GameCard>[];
        final secondHand = <GameCard>[];

        // Act
        repository.submitPlayerTurnPostState(playerId, firstHand);
        repository.submitPlayerTurnPostState(playerId, secondHand);

        // Assert
        expect(repository.submittedActiveTurnsPostState[playerId], equals(secondHand));
        expect(repository.submittedActiveTurnsPostState.length, equals(1));
      });

      test('should handle multiple players turn post state', () {
        // Arrange
        const player1 = 'player1';
        const player2 = 'player2';
        final hand1 = <GameCard>[];
        final hand2 = <GameCard>[];

        // Act
        repository.submitPlayerTurnPostState(player1, hand1);
        repository.submitPlayerTurnPostState(player2, hand2);

        // Assert
        expect(repository.submittedActiveTurnsPostState.length, equals(2));
        expect(repository.submittedActiveTurnsPostState[player1], equals(hand1));
        expect(repository.submittedActiveTurnsPostState[player2], equals(hand2));
      });
    });

    group('Player Turn Actions Management', () {
      test('should submit player turn actions successfully', () {
        // Arrange
        const playerId = 'player1';
        final actions = <TargetedAction>[];

        // Act
        repository.submitPlayerTurnActions(playerId, actions);

        // Assert
        expect(repository.submittedActiveActions, containsPair(playerId, actions));
        expect(repository.submittedActiveActions.length, equals(1));
      });

      test('should throw exception when submitting actions for same player twice', () {
        // Arrange
        const playerId = 'player1';
        final firstActions = <TargetedAction>[];
        final secondActions = <TargetedAction>[];

        // Act
        repository.submitPlayerTurnActions(playerId, firstActions);

        // Assert
        expect(
          () => repository.submitPlayerTurnActions(playerId, secondActions),
          throwsA(isA<Exception>().having(
            (e) => e.toString(),
            'message',
            contains('Player $playerId has already submitted actions for this turn'),
          )),
        );
      });

      test('should handle multiple players turn actions', () {
        // Arrange
        const player1 = 'player1';
        const player2 = 'player2';
        final actions1 = <TargetedAction>[];
        final actions2 = <TargetedAction>[];

        // Act
        repository.submitPlayerTurnActions(player1, actions1);
        repository.submitPlayerTurnActions(player2, actions2);

        // Assert
        expect(repository.submittedActiveActions.length, equals(2));
        expect(repository.submittedActiveActions[player1], equals(actions1));
        expect(repository.submittedActiveActions[player2], equals(actions2));
      });
    });

    group('Development Player Turn Actions', () {
      test('should allow dev submission for other players', () {
        // Arrange
        const playerId = 'player1';
        final actions = <TargetedAction>[];

        // Act
        repository.submitDevOtherPlayerTurnActions(playerId, actions);

        // Assert
        expect(repository.submittedActiveActions, containsPair(playerId, actions));
        expect(repository.submittedActiveActions.length, equals(1));
      });

      test('should allow overwriting existing actions in dev mode', () {
        // Arrange
        const playerId = 'player1';
        final firstActions = <TargetedAction>[];
        final secondActions = <TargetedAction>[];

        // Act
        repository.submitPlayerTurnActions(playerId, firstActions);
        repository.submitDevOtherPlayerTurnActions(playerId, secondActions);

        // Assert
        expect(repository.submittedActiveActions[playerId], equals(secondActions));
        expect(repository.submittedActiveActions.length, equals(1));
      });

      test('should handle multiple dev submissions for different players', () {
        // Arrange
        const player1 = 'player1';
        const player2 = 'player2';
        final actions1 = <TargetedAction>[];
        final actions2 = <TargetedAction>[];

        // Act
        repository.submitDevOtherPlayerTurnActions(player1, actions1);
        repository.submitDevOtherPlayerTurnActions(player2, actions2);

        // Assert
        expect(repository.submittedActiveActions.length, equals(2));
        expect(repository.submittedActiveActions[player1], equals(actions1));
        expect(repository.submittedActiveActions[player2], equals(actions2));
      });
    });

    group('Clear Operations', () {
      test('should clear all submitted active actions', () {
        // Arrange
        const player1 = 'player1';
        const player2 = 'player2';
        final actions1 = <TargetedAction>[];
        final actions2 = <TargetedAction>[];

        repository.submitPlayerTurnActions(player1, actions1);
        repository.submitPlayerTurnActions(player2, actions2);

        // Act
        repository.clearSubmittedActiveActions();

        // Assert
        expect(repository.submittedActiveActions, isEmpty);
      });

      test('should not affect turn post state when clearing actions', () {
        // Arrange
        const playerId = 'player1';
        final playerHand = <GameCard>[];
        final actions = <TargetedAction>[];

        repository.submitPlayerTurnPostState(playerId, playerHand);
        repository.submitPlayerTurnActions(playerId, actions);

        // Act
        repository.clearSubmittedActiveActions();

        // Assert
        expect(repository.submittedActiveActions, isEmpty);
        expect(repository.submittedActiveTurnsPostState, isNotEmpty);
        expect(repository.submittedActiveTurnsPostState[playerId], equals(playerHand));
      });
    });

    group('Integration Scenarios', () {
      test('should handle complete turn submission workflow', () {
        // Arrange
        const playerId = 'player1';
        final playerHand = <GameCard>[];
        final actions = <TargetedAction>[];

        // Act & Assert - Complete workflow should work
        expect(() {
          repository.submitPlayerTurnPostState(playerId, playerHand);
          repository.submitPlayerTurnActions(playerId, actions);

          // Verify both are stored
          expect(repository.submittedActiveTurnsPostState[playerId], equals(playerHand));
          expect(repository.submittedActiveActions[playerId], equals(actions));

          // Clear actions for next turn
          repository.clearSubmittedActiveActions();

          // Verify only actions are cleared
          expect(repository.submittedActiveActions, isEmpty);
          expect(repository.submittedActiveTurnsPostState, isNotEmpty);
        }, returnsNormally);
      });

      test('should handle mixed normal and dev submissions', () {
        // Arrange
        const player1 = 'player1';
        const player2 = 'player2';
        final actions1 = <TargetedAction>[];
        final actions2 = <TargetedAction>[];

        // Act
        repository.submitPlayerTurnActions(player1, actions1);
        repository.submitDevOtherPlayerTurnActions(player2, actions2);

        // Assert
        expect(repository.submittedActiveActions.length, equals(2));
        expect(repository.submittedActiveActions[player1], equals(actions1));
        expect(repository.submittedActiveActions[player2], equals(actions2));
      });
    });
  });
}
