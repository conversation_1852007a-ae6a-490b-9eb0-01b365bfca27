import 'dart:io';
import 'package:dauntless/data_services/json_read_write_data_service.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/match_config.dart';
import 'package:dauntless/models/base/saved_game_state.dart';
import 'package:dauntless/repositories/save_state_repository.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockJsonReadWriteDataService extends Mock {}
class MockDirectory extends Mock implements Directory {}
class MockFile extends Mock implements File {}
class MockFileSystemEntity extends Mock implements FileSystemEntity {}

void main() {
  group('SaveStateRepository', () {
    late SaveStateRepository repository;
    late MockDirectory mockDirectory;
    late MockFile mockFile;
    late List<MockFileSystemEntity> mockFileEntities;

    setUp(() {
      repository = SaveStateRepository();
      mockDirectory = MockDirectory();
      mockFile = MockFile();
      mockFileEntities = [
        MockFileSystemEntity(),
        MockFileSystemEntity(),
        MockFileSystemEntity(),
      ];
    });

    group('Constructor and Initial State', () {
      test('should initialize successfully', () {
        expect(repository, isNotNull);
        expect(repository, isA<SaveStateRepository>());
      });
    });

    group('saveSavedGameState()', () {
      test('should save game state to specified path', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('save_state_test');
        final savePath = '${tempDir.path}/save.json';
        final savedGameState = SavedGameState(
          matchState: createMockGameMatchState(),
        );

        try {
          // Act
          await repository.saveSavedGameState(savePath, savedGameState);

          // Assert - File should be created
          final saveFile = File(savePath);
          expect(await saveFile.exists(), isTrue);
        } finally {
          // Cleanup
          await tempDir.delete(recursive: true);
        }
      });

      test('should handle save errors gracefully', () async {
        // Arrange
        const invalidPath = '/invalid/path/save.json';
        final savedGameState = SavedGameState(
          matchState: createMockGameMatchState(),
        );

        // Act & Assert - Should handle file system errors
        expect(
          () async => await repository.saveSavedGameState(invalidPath, savedGameState),
          throwsA(isA<Exception>()),
        );
      });

      test('should call JsonReadWriteDataService with correct parameters', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('save_state_test');
        final savePath = '${tempDir.path}/save.json';
        final savedGameState = SavedGameState(
          matchState: createMockGameMatchState(),
        );

        try {
          // Act - This tests the integration with JsonReadWriteDataService
          await repository.saveSavedGameState(savePath, savedGameState);

          // Assert - File should exist and contain JSON
          final saveFile = File(savePath);
          expect(await saveFile.exists(), isTrue);
          final content = await saveFile.readAsString();
          expect(content, isNotEmpty);
        } finally {
          // Cleanup
          await tempDir.delete(recursive: true);
        }
      });
    });

    group('getGameSaves()', () {
      test('should return list of file system entities from existing directory', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('save_state_test');
        final saveDirectory = tempDir.path;

        try {
          // Act - This will create directory if it doesn't exist
          final result = await repository.getGameSaves(saveDirectory);

          // Assert
          expect(result, isA<List<FileSystemEntity>>());
          expect(result, isNotNull);
        } finally {
          // Cleanup
          await tempDir.delete(recursive: true);
        }
      });

      test('should create directory if it does not exist', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('save_state_test');
        final nonExistentDirectory = '${tempDir.path}/new_saves';

        try {
          // Act
          final result = await repository.getGameSaves(nonExistentDirectory);

          // Assert
          expect(result, isA<List<FileSystemEntity>>());
          // Directory should be created and return empty list initially
          expect(result, isEmpty);
        } finally {
          // Cleanup
          await tempDir.delete(recursive: true);
        }
      });

      test('should handle directory creation recursively', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('save_state_test');
        final deepDirectory = '${tempDir.path}/deep/nested/saves';

        try {
          // Act
          final result = await repository.getGameSaves(deepDirectory);

          // Assert
          expect(result, isA<List<FileSystemEntity>>());
          expect(result, isNotNull);
        } finally {
          // Cleanup
          await tempDir.delete(recursive: true);
        }
      });

      test('should list files in existing directory', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('save_state_test');
        final saveDirectory = '${tempDir.path}/existing_saves';

        try {
          // Create directory and add some test files
          final dir = Directory(saveDirectory);
          await dir.create(recursive: true);

          // Create test files
          await File('$saveDirectory/save1.json').writeAsString('{}');
          await File('$saveDirectory/save2.json').writeAsString('{}');

          // Act
          final result = await repository.getGameSaves(saveDirectory);

          // Assert
          expect(result, isA<List<FileSystemEntity>>());
          expect(result.length, greaterThanOrEqualTo(2));
        } finally {
          // Cleanup
          await tempDir.delete(recursive: true);
        }
      });
    });

    group('loadSavedGameState()', () {
      test('should load saved game state from valid file path', () async {
        // Arrange
        const testPath = '/test/saves/test_save.json';
        
        // Act & Assert - This tests integration with JsonReadWriteDataService
        try {
          final result = await repository.loadSavedGameState(testPath);
          // If file doesn't exist, result should be null or throw
          expect(result, anyOf(isNull, isA<SavedGameState>()));
        } catch (e) {
          // Expected to fail if file doesn't exist
          expect(e, isA<Exception>());
        }
      });

      test('should return null for non-existent file', () async {
        // Arrange
        const nonExistentPath = '/test/saves/non_existent.json';
        
        // Act & Assert
        expect(
          () async => await repository.loadSavedGameState(nonExistentPath),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle malformed JSON gracefully', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('save_state_test');
        final testPath = '${tempDir.path}/malformed.json';
        final testFile = File(testPath);

        try {
          // Create malformed JSON file
          await testFile.writeAsString('{ invalid json }');

          // Act & Assert
          expect(
            () async => await repository.loadSavedGameState(testPath),
            throwsA(isA<Exception>()),
          );
        } finally {
          // Cleanup
          await tempDir.delete(recursive: true);
        }
      });
    });

    group('Integration Scenarios', () {
      test('should handle complete save and load workflow', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('save_state_test');
        final saveDirectory = '${tempDir.path}/integration_saves';
        final savePath = '$saveDirectory/integration_test.json';
        final savedGameState = SavedGameState(
          matchState: createMockGameMatchState(),
        );

        try {
          // Create directory
          await Directory(saveDirectory).create(recursive: true);

          // Act - Save game state
          await repository.saveSavedGameState(savePath, savedGameState);

          // Verify file was created
          final saveFile = File(savePath);
          expect(await saveFile.exists(), isTrue);

          // Act - Get game saves
          final saves = await repository.getGameSaves(saveDirectory);
          expect(saves, isNotEmpty);

          // Act - Load saved game state
          final loadedState = await repository.loadSavedGameState(savePath);
          expect(loadedState, isNotNull);
          expect(loadedState, isA<SavedGameState>());

        } finally {
          // Cleanup
          await tempDir.delete(recursive: true);
        }
      });

      test('should handle multiple save files in directory', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('save_state_test');
        final saveDirectory = '${tempDir.path}/multiple_saves';

        try {
          final dir = Directory(saveDirectory);
          await dir.create(recursive: true);

          // Create multiple save files
          for (int i = 1; i <= 3; i++) {
            final savePath = '$saveDirectory/save_$i.json';
            final savedGameState = SavedGameState(
              matchState: createMockGameMatchState(),
            );
            await repository.saveSavedGameState(savePath, savedGameState);
          }

          // Act
          final saves = await repository.getGameSaves(saveDirectory);

          // Assert
          expect(saves.length, greaterThanOrEqualTo(3));
        } finally {
          // Cleanup
          await tempDir.delete(recursive: true);
        }
      });
    });

    group('Error Handling', () {
      test('should handle permission errors gracefully', () async {
        // Arrange
        const restrictedPath = '/root/restricted/save.json';
        final savedGameState = SavedGameState(
          matchState: createMockGameMatchState(),
        );

        // Act & Assert
        expect(
          () async => await repository.saveSavedGameState(restrictedPath, savedGameState),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle invalid directory paths', () async {
        // Arrange
        const invalidDirectory = '/invalid\x00/path';
        
        // Act & Assert
        expect(
          () async => await repository.getGameSaves(invalidDirectory),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle empty file paths', () async {
        // Arrange
        const emptyPath = '';
        
        // Act & Assert
        expect(
          () async => await repository.loadSavedGameState(emptyPath),
          throwsA(isA<Exception>()),
        );
      });
    });
  });
}

// Helper function to create mock GameMatchState
GameMatchState createMockGameMatchState() {
  return const GameMatchState(
    matchConfig: MatchConfig(
      gameId: 'test-game-id',
      selectedGameMode: GameMode.hotSeat,
      hostId: 'test-host-id',
      matchId: 'test-match-id',
    ),
    userPlayerId: 'test-player-id',
    turnCount: 1,
    hands: {},
    resources: {},
    loadedCardClasses: {},
    currentTurnActions: [],
    processingStatus: ProcessingStatus.start,
  );
}
