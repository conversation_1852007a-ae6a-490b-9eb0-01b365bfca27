import 'package:common/models/player.dart';
import 'package:dauntless/dev/dev_initial_state.dart';
import 'package:dauntless/repositories/players_repository.dart';
import 'package:test/test.dart';

void main() {
  group('PlayersRepository', () {
    late PlayersRepository repository;

    setUp(() {
      repository = PlayersRepository();
    });

    group('Constructor and Initial State', () {
      test('should initialize with players from DevInitialState', () {
        expect(repository.players, isNotNull);
        expect(repository.players, isA<List<Player>>());
        expect(repository.players, equals(DevInitialState.players));
      });

      test('should provide access to playable players', () {
        expect(repository.playablePlayers, isNotNull);
        expect(repository.playablePlayers, equals(repository.players));
        expect(repository.playablePlayers, equals(DevInitialState.players));
      });
    });

    group('Players Data Validation', () {
      test('should have non-empty players list', () {
        expect(repository.players, isNotEmpty);
      });

      test('should contain valid Player objects', () {
        for (final player in repository.players) {
          expect(player, isA<Player>());
          expect(player.id, isNotNull);
          expect(player.id, isNotEmpty);
        }
      });

      test('should have unique player IDs', () {
        final playerIds = repository.players.map((p) => p.id).toList();
        final uniqueIds = playerIds.toSet();
        expect(playerIds.length, equals(uniqueIds.length));
      });
    });

    group('Playable Players Functionality', () {
      test('should return same reference as players list', () {
        expect(identical(repository.playablePlayers, repository.players), isTrue);
      });

      test('should maintain consistency between players and playablePlayers', () {
        expect(repository.playablePlayers.length, equals(repository.players.length));
        
        for (int i = 0; i < repository.players.length; i++) {
          expect(repository.playablePlayers[i], equals(repository.players[i]));
        }
      });
    });

    group('Data Integrity', () {
      test('should maintain reference to DevInitialState.players', () {
        expect(identical(repository.players, DevInitialState.players), isTrue);
      });

      test('should reflect changes in DevInitialState.players', () {
        // This test verifies that the repository maintains a reference
        // to the original DevInitialState.players list
        final originalLength = repository.players.length;
        final originalPlayers = List<Player>.from(repository.players);
        
        // Verify we have the expected reference
        expect(repository.players, equals(originalPlayers));
        expect(repository.players.length, equals(originalLength));
      });

      test('should provide consistent data across multiple access', () {
        final firstAccess = repository.players;
        final secondAccess = repository.players;
        final thirdAccess = repository.playablePlayers;
        
        expect(identical(firstAccess, secondAccess), isTrue);
        expect(identical(secondAccess, thirdAccess), isTrue);
        expect(firstAccess.length, equals(secondAccess.length));
        expect(secondAccess.length, equals(thirdAccess.length));
      });
    });

    group('Player Data Structure Validation', () {
      test('should have players with required properties', () {
        for (final player in repository.players) {
          // Verify basic Player structure
          expect(player.id, isA<String>());
          expect(player.id.trim(), isNotEmpty);
          
          // Additional validation based on Player model structure
          // These tests ensure the Player objects are properly formed
          expect(player.toString(), isNotEmpty);
        }
      });

      test('should handle empty scenarios gracefully', () {
        // Even if DevInitialState.players were empty, repository should handle it
        expect(() => repository.players.isEmpty, returnsNormally);
        expect(() => repository.playablePlayers.isEmpty, returnsNormally);
      });
    });

    group('Integration Scenarios', () {
      test('should work in typical player selection workflow', () {
        // Arrange & Act - Simulate typical usage
        final availablePlayers = repository.playablePlayers;
        
        // Assert - Should be able to work with the players
        expect(availablePlayers, isNotNull);
        expect(availablePlayers, isNotEmpty);
        
        // Should be able to select first player
        final firstPlayer = availablePlayers.first;
        expect(firstPlayer, isA<Player>());
        expect(firstPlayer.id, isNotEmpty);
        
        // Should be able to find player by ID
        final foundPlayers = availablePlayers.where((p) => p.id == firstPlayer.id);
        expect(foundPlayers, isNotEmpty);
        final foundPlayer = foundPlayers.first;
        expect(foundPlayer, equals(firstPlayer));
      });

      test('should support player enumeration and filtering', () {
        // Arrange
        final players = repository.players;
        
        // Act & Assert - Should support common operations
        expect(() {
          // Enumeration
          for (final player in players) {
            expect(player.id, isNotEmpty);
          }
          
          // Filtering (example: by ID length)
          final filteredPlayers = players.where((p) => p.id.length > 0).toList();
          expect(filteredPlayers.length, equals(players.length));
          
          // Mapping
          final playerIds = players.map((p) => p.id).toList();
          expect(playerIds.length, equals(players.length));
          
          // Finding
          if (players.isNotEmpty) {
            final firstPlayerId = players.first.id;
            final foundPlayers = players.where((p) => p.id == firstPlayerId);
            expect(foundPlayers, isNotEmpty);
            final foundPlayer = foundPlayers.first;
            expect(foundPlayer, isNotNull);
          }
        }, returnsNormally);
      });

      test('should maintain data consistency during multiple repository instances', () {
        // Arrange
        final repository1 = PlayersRepository();
        final repository2 = PlayersRepository();
        
        // Act & Assert
        expect(repository1.players.length, equals(repository2.players.length));
        expect(repository1.playablePlayers.length, equals(repository2.playablePlayers.length));
        
        // Both should reference the same underlying data
        expect(identical(repository1.players, repository2.players), isTrue);
        expect(identical(repository1.playablePlayers, repository2.playablePlayers), isTrue);
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle list operations safely', () {
        // Arrange
        final players = repository.players;
        
        // Act & Assert - Common list operations should work
        expect(() => players.length, returnsNormally);
        expect(() => players.isEmpty, returnsNormally);
        expect(() => players.isNotEmpty, returnsNormally);
        
        if (players.isNotEmpty) {
          expect(() => players.first, returnsNormally);
          expect(() => players.last, returnsNormally);
          expect(() => players[0], returnsNormally);
        }
        
        expect(() => players.toList(), returnsNormally);
        expect(() => players.map((p) => p.id).toList(), returnsNormally);
      });

      test('should provide stable references', () {
        // Arrange
        final firstReference = repository.players;
        final secondReference = repository.players;
        final thirdReference = repository.playablePlayers;
        
        // Act & Assert - References should be stable
        expect(identical(firstReference, secondReference), isTrue);
        expect(identical(secondReference, thirdReference), isTrue);
        
        // Content should be identical
        expect(firstReference.length, equals(secondReference.length));
        expect(secondReference.length, equals(thirdReference.length));
      });
    });
  });
}
