import 'package:flutter_test/flutter_test.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/repositories/interfaces/match_source_repository_interface.dart';
import 'package:dauntless/repositories/interfaces/match_persistence_repository_interface.dart';
import 'package:dauntless/repositories/implementations/local_match_repository.dart';
import 'package:dauntless/repositories/implementations/network_match_repository.dart';
import 'package:dauntless/repositories/implementations/file_match_repository.dart';
import 'package:dauntless/use_cases/match_selection_use_case.dart';
import 'package:common/models/game_match.dart';
import 'package:dauntless/models/base/game_config.dart';

void main() {
  group('Phase 1 Repository Interfaces', () {
    test('LocalMatchRepository implements MatchRepositoryInterface', () {
      // Arrange
      final repository = LocalMatchRepository(null);

      // Assert
      expect(repository, isA<MatchRepositoryInterface>());
      expect(repository.sourceName, equals('Local'));
      expect(repository.supportsRealTimeUpdates, isFalse);
    });

    test('NetworkMatchRepository implements MatchRepositoryInterface', () {
      // Note: This test would require mocking ServerRepository
      // For now, just verify the interface contract
      expect(NetworkMatchRepository, isA<Type>());
    });

    test('FileMatchRepository implements MatchPersistenceRepositoryInterface', () {
      // Arrange
      final repository = FileMatchRepository(null);

      // Assert
      expect(repository, isA<MatchPersistenceRepositoryInterface>());
      expect(repository.persistenceLayerName, equals('File System'));
    });

    test('RepositoryBasedMatchSelectionUseCase implements MatchSelectionUseCase', () {
      // Arrange
      final mockRepository = MockMatchRepository();
      final useCase = RepositoryBasedMatchSelectionUseCase(mockRepository, null);

      // Assert
      expect(useCase, isA<MatchSelectionUseCase>());
      expect(useCase.name, equals('Mock'));
      expect(useCase.isServerSource, isFalse);
    });

    test('RepositoryBasedMatchSelectionUseCase delegates to repository', () async {
      // Arrange
      final mockRepository = MockMatchRepository();
      final useCase = RepositoryBasedMatchSelectionUseCase(mockRepository, null);

      // Act
      final matches = await useCase.fetchOpenMatches('testGame');

      // Assert
      expect(matches, isEmpty);
      expect(mockRepository.fetchOpenMatchesCalled, isTrue);
    });
  });
}

/// Mock implementation for testing
class MockMatchRepository implements MatchRepositoryInterface {
  bool fetchOpenMatchesCalled = false;

  @override
  String get sourceName => 'Mock';

  @override
  bool get supportsRealTimeUpdates => false;

  @override
  Future<List<GameMatch>> fetchOpenMatches(String gameName) async {
    fetchOpenMatchesCalled = true;
    return [];
  }

  @override
  Future<GameMatch?> createMatch(GameMatch newMatch, GameConfig gameConfig) async {
    return null;
  }

  @override
  Future<bool> joinMatch(String matchId, {String? playerId}) async {
    return true;
  }

  @override
  Future<bool> leaveMatch(String matchId, {String? playerId}) async {
    return true;
  }

  @override
  Future<bool> deleteMatch(String matchId) async {
    return true;
  }

  @override
  Future<GameMatch?> getMatch(String matchId) async {
    return null;
  }

  @override
  Future<bool> updateMatch(GameMatch match) async {
    return true;
  }
}
