import 'package:test/test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:get_it/get_it.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:common/models/user.dart';
import 'package:common/models/player_class.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/frameworks/user/user_state.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/repositories/server_repository.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/player_slot_management_use_case.dart';

// Mock classes
class MockMatchRepository extends Mock implements MatchRepositoryInterface {}
class MockServerRepository extends Mock implements ServerRepository {}
class MockUserManager extends Mock implements UserManager {}
class MockRemoteLogger extends Mock implements RemoteLogger {}

// Register fallback values for mocktail
void _registerFallbackValues() {
  registerFallbackValue(const GameConfig(id: 'test', name: 'Test'));
  registerFallbackValue(const PlayerSlot(
    id: 'test',
    playerId: 'test',
    type: PlayerType.humanLocal,
    playerClassId: 'test',
    name: 'Test',
  ));
  registerFallbackValue(<PlayerSlot>[]);
}

void main() {
  _registerFallbackValues();

  group('PlayerSlotManagementUseCase', () {
    late PlayerSlotManagementUseCase useCase;
    late MockMatchRepository mockLocalRepository;
    late MockMatchRepository mockNetworkRepository;
    late MockServerRepository mockServerRepository;
    late MockUserManager mockUserManager;
    late MockRemoteLogger mockLogger;
    late Map<String, MatchRepositoryInterface> repositories;

    setUp(() {
      mockLocalRepository = MockMatchRepository();
      mockNetworkRepository = MockMatchRepository();
      mockServerRepository = MockServerRepository();
      mockUserManager = MockUserManager();
      mockLogger = MockRemoteLogger();

      repositories = {
        'local': mockLocalRepository,
        'network': mockNetworkRepository,
      };

      // Setup GetIt mock
      GetIt.instance.reset();
      GetIt.instance.registerSingleton<ServerRepository>(mockServerRepository);

      useCase = PlayerSlotManagementUseCase(
        repositories,
        mockUserManager,
        mockLogger,
      );
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    group('addPlayerSlot', () {
      test('should add player slot successfully', () {
        // Arrange
        final currentSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
          playerClasses: [
            PlayerClass(
              id: 'class1',
              name: 'Class 1',
              defaultPlayerName: 'Player',
              numAllowed: 4,
              numRequired: 1,
            ),
          ],
        );

        // Act
        final result = useCase.addPlayerSlot(currentSlots, config);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value!.length, equals(2));
        expect(result.value![1].name, equals('Player 2'));
        expect(result.value![1].type, equals(PlayerType.humanLocal));
      });

      test('should fail when maximum slots reached', () {
        // Arrange
        final currentSlots = List.generate(8, (index) => PlayerSlot(
          id: 'slot_$index',
          playerId: 'player$index',
          type: PlayerType.humanLocal,
          playerClassId: 'class1',
          name: 'Player ${index + 1}',
        ));

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
          playerClasses: [
            PlayerClass(
              id: 'class1',
              name: 'Class 1',
              defaultPlayerName: 'Player',
              numAllowed: 4,
              numRequired: 1,
            ),
          ],
        );

        // Act
        final result = useCase.addPlayerSlot(currentSlots, config);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Maximum number of player slots reached'));
      });

      test('should fail when no player classes available', () {
        // Arrange
        final currentSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
          playerClasses: [], // No player classes
        );

        // Act
        final result = useCase.addPlayerSlot(currentSlots, config);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('No player classes available'));
      });
    });

    group('removePlayerSlot', () {
      test('should remove player slot successfully', () {
        // Arrange
        final currentSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
          PlayerSlot(
            id: 'slot_1',
            playerId: 'player2',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 2',
          ),
        ];

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        // Act
        final result = useCase.removePlayerSlot(currentSlots, 1);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value!.length, equals(1));
        expect(result.value![0].id, equals('slot_0'));
      });

      test('should fail with invalid index', () {
        // Arrange
        final currentSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        // Act
        final result = useCase.removePlayerSlot(currentSlots, 5);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Invalid slot index'));
      });
    });

    group('updatePlayerType', () {
      test('should update player type successfully', () async {
        // Arrange
        final originalSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        final request = UpdatePlayerTypeRequest(
          slotIndex: 0,
          newType: PlayerType.humanNetwork,
          currentSlots: originalSlots,
          gameConfig: config,
          isSelectedMatch: false,
          matchId: null,
        );

        // Act
        final result = await useCase.updatePlayerType(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value!.updatedSlots[0].type, equals(PlayerType.humanNetwork));
        expect(result.value!.requiresNetworkMatch, isTrue);
      });

      test('should handle invalid slot index', () async {
        // Arrange
        final originalSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        final request = UpdatePlayerTypeRequest(
          slotIndex: 5, // Invalid index
          newType: PlayerType.humanNetwork,
          currentSlots: originalSlots,
          gameConfig: config,
          isSelectedMatch: false,
          matchId: null,
        );

        // Act
        final result = await useCase.updatePlayerType(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Invalid slot index'));
      });

      test('should determine network match requirement correctly', () async {
        // Arrange
        final user = User(id: 'user123');
        final userState = UserState(user: user);
        when(() => mockUserManager.state).thenReturn(userState);

        final originalSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        final request = UpdatePlayerTypeRequest(
          slotIndex: 0,
          newType: PlayerType.humanNetwork,
          currentSlots: originalSlots,
          gameConfig: config,
          isSelectedMatch: false, // Not a selected match to avoid server calls
          matchId: null,
        );

        // Act
        final result = await useCase.updatePlayerType(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value!.requiresNetworkMatch, isTrue);
        expect(result.value!.updatedSlots[0].type, equals(PlayerType.humanNetwork));
      });
    });

    group('joinPlayerSlot', () {
      test('should join player slot successfully', () async {
        // Arrange
        final user = User(id: 'user123');
        final userState = UserState(user: user);
        when(() => mockUserManager.state).thenReturn(userState);

        final originalSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: null,
            type: PlayerType.humanNetwork,
            playerClassId: 'class1',
            name: 'Empty Slot',
          ),
        ];

        final request = JoinSlotRequest(
          slotIndex: 0,
          currentSlots: originalSlots,
          playerId: 'user123',
          matchId: null,
        );

        // Act
        final result = await useCase.joinPlayerSlot(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value, isTrue);
      });

      test('should fail when no user is logged in', () async {
        // Arrange
        final userState = UserState(user: null);
        when(() => mockUserManager.state).thenReturn(userState);

        final originalSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: null,
            type: PlayerType.humanNetwork,
            playerClassId: 'class1',
            name: 'Empty Slot',
          ),
        ];

        final request = JoinSlotRequest(
          slotIndex: 0,
          currentSlots: originalSlots,
          playerId: null,
          matchId: null,
        );

        // Act
        final result = await useCase.joinPlayerSlot(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('No user logged in'));
      });

      test('should fail with invalid slot index', () async {
        // Arrange
        final user = User(id: 'user123');
        final userState = UserState(user: user);
        when(() => mockUserManager.state).thenReturn(userState);

        final originalSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: null,
            type: PlayerType.humanNetwork,
            playerClassId: 'class1',
            name: 'Empty Slot',
          ),
        ];

        final request = JoinSlotRequest(
          slotIndex: 5, // Invalid index
          currentSlots: originalSlots,
          playerId: 'user123',
          matchId: null,
        );

        // Act
        final result = await useCase.joinPlayerSlot(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Invalid slot index'));
      });

      test('should join slot without match ID', () async {
        // Arrange
        final user = User(id: 'user123');
        final userState = UserState(user: user);
        when(() => mockUserManager.state).thenReturn(userState);

        final originalSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: null,
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Empty Slot',
          ),
        ];

        final request = JoinSlotRequest(
          slotIndex: 0,
          currentSlots: originalSlots,
          playerId: 'user123',
          matchId: null, // No match ID - just local operation
        );

        // Act
        final result = await useCase.joinPlayerSlot(request);

        // Assert
        expect(result.isSuccess, isTrue);
      });
    });

    group('createDefaultSlots', () {
      test('should create default slots for game config', () {
        // Arrange
        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
          playerClasses: [
            PlayerClass(
              id: 'class1',
              name: 'Class 1',
              defaultPlayerName: 'Player',
              numAllowed: 4,
              numRequired: 1,
            ),
            PlayerClass(
              id: 'class2',
              name: 'Class 2',
              defaultPlayerName: 'Player',
              numAllowed: 4,
              numRequired: 1,
            ),
          ],
        );

        // Act
        final result = useCase.createDefaultSlots(config);

        // Assert
        expect(result, isNotEmpty);
        expect(result.length, equals(2)); // Default minimum slots
        expect(result[0].playerClassId, equals('class1'));
        expect(result[1].playerClassId, equals('class2'));
      });

      test('should return empty list when no player classes', () {
        // Arrange
        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
          playerClasses: [],
        );

        // Act
        final result = useCase.createDefaultSlots(config);

        // Assert
        expect(result, isEmpty); // No slots can be created without player classes
      });
    });

    group('validatePlayerSlots', () {
      test('should validate successfully with valid slots', () {
        // Arrange
        final slots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
          PlayerSlot(
            id: 'slot_1',
            playerId: 'player2',
            type: PlayerType.humanLocal,
            playerClassId: 'class2',
            name: 'Player 2',
          ),
        ];

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        // Act
        final result = useCase.validatePlayerSlots(slots, config);

        // Assert
        expect(result.isSuccess, isTrue);
      });

      test('should fail validation with empty slots', () {
        // Arrange
        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        // Act
        final result = useCase.validatePlayerSlots([], config);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('At least one player slot is required'));
      });

      test('should fail validation with too many slots', () {
        // Arrange
        final slots = List.generate(10, (index) => PlayerSlot(
          id: 'slot_$index',
          playerId: 'player$index',
          type: PlayerType.humanLocal,
          playerClassId: 'class1',
          name: 'Player ${index + 1}',
        ));

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        // Act
        final result = useCase.validatePlayerSlots(slots, config);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Maximum 8 players allowed'));
      });
    });

    group('canAddPlayerSlot', () {
      test('should allow adding slot when under maximum', () {
        // Arrange
        final currentSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        // Act
        final result = useCase.canAddPlayerSlot(currentSlots, config);

        // Assert
        expect(result, isTrue);
      });

      test('should not allow adding slot when at maximum', () {
        // Arrange
        final currentSlots = List.generate(8, (index) => PlayerSlot(
          id: 'slot_$index',
          playerId: 'player$index',
          type: PlayerType.humanLocal,
          playerClassId: 'class1',
          name: 'Player ${index + 1}',
        ));

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        // Act
        final result = useCase.canAddPlayerSlot(currentSlots, config);

        // Assert
        expect(result, isFalse);
      });
    });

    group('canRemovePlayerSlot', () {
      test('should allow removing slot when above minimum', () {
        // Arrange
        final currentSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
          PlayerSlot(
            id: 'slot_1',
            playerId: 'player2',
            type: PlayerType.humanLocal,
            playerClassId: 'class2',
            name: 'Player 2',
          ),
        ];

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        // Act
        final result = useCase.canRemovePlayerSlot(currentSlots, 1, config);

        // Assert
        expect(result, isTrue);
      });

      test('should not allow removing slot with invalid index', () {
        // Arrange
        final currentSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        // Act
        final result = useCase.canRemovePlayerSlot(currentSlots, 5, config);

        // Assert
        expect(result, isFalse);
      });

      test('should not allow removing slot when at minimum', () {
        // Arrange
        final currentSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        final config = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        // Act
        final result = useCase.canRemovePlayerSlot(currentSlots, 0, config);

        // Assert
        expect(result, isFalse);
      });
    });

    group('updatePlayerName', () {
      test('should update player name successfully', () {
        // Arrange
        final currentSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        // Act
        final result = useCase.updatePlayerName(currentSlots, 0, 'New Name');

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value![0].name, equals('New Name'));
      });

      test('should fail with invalid slot index', () {
        // Arrange
        final currentSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        // Act
        final result = useCase.updatePlayerName(currentSlots, 5, 'New Name');

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Invalid slot index'));
      });
    });
  });
}
