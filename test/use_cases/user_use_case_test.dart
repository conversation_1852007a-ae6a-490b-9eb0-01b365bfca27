import 'dart:io';
import 'package:dauntless/data_services/json_read_write_data_service.dart';
import 'package:dauntless/frameworks/user/user_config.dart';
import 'package:dauntless/frameworks/user/user_profile.dart';
import 'package:dauntless/use_cases/user_use_case.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockJsonReadWriteDataService extends Mock implements JsonReadWriteDataService {}
class MockFile extends Mock implements File {}

void main() {
  group('UserUseCase', () {
    late UserUseCase userUseCase;

    setUp(() {
      userUseCase = UserUseCase();
    });

    group('Constructor and Initialization', () {
      test('should initialize without dependencies', () {
        expect(userUseCase, isNotNull);
        expect(userUseCase, isA<UserUseCase>());
      });

      test('should be instantiable multiple times', () {
        final useCase1 = UserUseCase();
        final useCase2 = UserUseCase();
        
        expect(useCase1, isNot(same(useCase2)));
        expect(useCase1, isA<UserUseCase>());
        expect(useCase2, isA<UserUseCase>());
      });
    });

    group('loadUserConfig', () {
      test('should return default config when file does not exist', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        final nonExistentPath = '${tempDir.path}/non_existent.json';
        
        try {
          // Act
          final result = await userUseCase.loadUserConfig(nonExistentPath);
          
          // Assert
          expect(result, isA<UserConfig>());
          expect(result.profiles, isEmpty);
          expect(result.selectedProfileId, isNull);
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should load existing config file successfully', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        final configPath = '${tempDir.path}/test_config.json';
        
        // Create a test config file
        final testConfig = UserConfig(
          profiles: [
            UserProfile(id: 'test1', name: 'Test User 1'),
            UserProfile(id: 'test2', name: 'Test User 2'),
          ],
          selectedProfileId: 'test1',
        );
        
        // Save the config using the actual use case to ensure proper JSON format
        await userUseCase.saveUserConfig(testConfig, configPath);
        
        try {
          // Act
          final result = await userUseCase.loadUserConfig(configPath);
          
          // Assert
          expect(result, isA<UserConfig>());
          expect(result.profiles, hasLength(2));
          expect(result.selectedProfileId, equals('test1'));
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should return default config on file parsing error', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        final configPath = '${tempDir.path}/invalid_config.json';
        
        // Create an invalid JSON file
        final configFile = File(configPath);
        await configFile.writeAsString('invalid json content');
        
        try {
          // Act
          final result = await userUseCase.loadUserConfig(configPath);
          
          // Assert
          expect(result, isA<UserConfig>());
          expect(result.profiles, isEmpty);
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should use default path when no path provided', () async {
        // Act & Assert - Should not throw
        final result = await userUseCase.loadUserConfig();
        expect(result, isA<UserConfig>());
      });

      test('should handle file system errors gracefully', () async {
        // Arrange - Use a path that would cause permission errors
        const invalidPath = '/root/invalid_path/config.json';
        
        // Act
        final result = await userUseCase.loadUserConfig(invalidPath);
        
        // Assert - Should return default config instead of throwing
        expect(result, isA<UserConfig>());
        expect(result.profiles, isEmpty);
      });
    });

    group('saveUserConfig', () {
      test('should save config to specified path', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        final configPath = '${tempDir.path}/save_test.json';
        
        final testConfig = UserConfig(
          profiles: [UserProfile(id: 'save_test', name: 'Save Test User')],
          selectedProfileId: 'save_test',
        );
        
        try {
          // Act
          await userUseCase.saveUserConfig(testConfig, configPath);
          
          // Assert
          final savedFile = File(configPath);
          expect(await savedFile.exists(), isTrue);
          
          final content = await savedFile.readAsString();
          expect(content, isNotEmpty);
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should throw exception on save error', () async {
        // Arrange - Use invalid path that would cause write error
        const invalidPath = '/root/invalid_path/config.json';
        final testConfig = UserConfig();
        
        // Act & Assert
        expect(
          () => userUseCase.saveUserConfig(testConfig, invalidPath),
          throwsException,
        );
      });

      test('should use default path when no path provided', () async {
        // Arrange
        final testConfig = UserConfig();
        
        // Act & Assert - Should complete (may fail due to permissions, but shouldn't crash)
        try {
          await userUseCase.saveUserConfig(testConfig);
        } catch (e) {
          // Expected if we don't have write permissions to default location
          expect(e, isA<Exception>());
        }
      });
    });

    group('addUserProfile', () {
      test('should add new profile successfully', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        final configPath = '${tempDir.path}/add_profile_test.json';
        
        final initialConfig = UserConfig();
        final newProfile = UserProfile(id: 'new_user', name: 'New User');
        
        try {
          // Act
          final result = await userUseCase.addUserProfile(initialConfig, newProfile);
          
          // Assert
          expect(result.profiles, hasLength(1));
          expect(result.profiles.first.id, equals('new_user'));
          expect(result.profiles.first.name, equals('New User'));
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should throw exception when profile ID already exists', () async {
        // Arrange
        final existingProfile = UserProfile(id: 'existing', name: 'Existing User');
        final config = UserConfig(profiles: [existingProfile]);
        final duplicateProfile = UserProfile(id: 'existing', name: 'Duplicate User');
        
        // Act & Assert
        expect(
          () => userUseCase.addUserProfile(config, duplicateProfile),
          throwsException,
        );
      });

      test('should add profile to existing profiles list', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        
        final existingProfile = UserProfile(id: 'existing', name: 'Existing User');
        final config = UserConfig(profiles: [existingProfile]);
        final newProfile = UserProfile(id: 'new', name: 'New User');
        
        try {
          // Act
          final result = await userUseCase.addUserProfile(config, newProfile);
          
          // Assert
          expect(result.profiles, hasLength(2));
          expect(result.profiles.map((p) => p.id), containsAll(['existing', 'new']));
        } finally {
          await tempDir.delete(recursive: true);
        }
      });
    });

    group('updateUserProfile', () {
      test('should update existing profile successfully', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        
        final originalProfile = UserProfile(id: 'update_test', name: 'Original Name');
        final config = UserConfig(profiles: [originalProfile]);
        final updatedProfile = UserProfile(id: 'update_test', name: 'Updated Name');
        
        try {
          // Act
          final result = await userUseCase.updateUserProfile(config, updatedProfile);
          
          // Assert
          expect(result.profiles, hasLength(1));
          expect(result.profiles.first.name, equals('Updated Name'));
          expect(result.profiles.first.id, equals('update_test'));
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should throw exception when profile not found', () async {
        // Arrange
        final config = UserConfig();
        final nonExistentProfile = UserProfile(id: 'non_existent', name: 'Non Existent');
        
        // Act & Assert
        expect(
          () => userUseCase.updateUserProfile(config, nonExistentProfile),
          throwsException,
        );
      });

      test('should update correct profile in multi-profile config', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        
        final profile1 = UserProfile(id: 'user1', name: 'User 1');
        final profile2 = UserProfile(id: 'user2', name: 'User 2');
        final config = UserConfig(profiles: [profile1, profile2]);
        final updatedProfile2 = UserProfile(id: 'user2', name: 'Updated User 2');
        
        try {
          // Act
          final result = await userUseCase.updateUserProfile(config, updatedProfile2);
          
          // Assert
          expect(result.profiles, hasLength(2));
          expect(result.profiles.first.name, equals('User 1')); // Unchanged
          expect(result.profiles.last.name, equals('Updated User 2')); // Updated
        } finally {
          await tempDir.delete(recursive: true);
        }
      });
    });

    group('removeUserProfile', () {
      test('should remove profile successfully', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        
        final profile = UserProfile(id: 'remove_test', name: 'Remove Test');
        final config = UserConfig(profiles: [profile]);
        
        try {
          // Act
          final result = await userUseCase.removeUserProfile(config, 'remove_test');
          
          // Assert
          expect(result.profiles, isEmpty);
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should throw exception when profile not found', () async {
        // Arrange
        final config = UserConfig();
        
        // Act & Assert
        expect(
          () => userUseCase.removeUserProfile(config, 'non_existent'),
          throwsException,
        );
      });

      test('should clear selected profile when removing selected profile', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        
        final profile = UserProfile(id: 'selected', name: 'Selected User');
        final config = UserConfig(
          profiles: [profile],
          selectedProfileId: 'selected',
        );
        
        try {
          // Act
          final result = await userUseCase.removeUserProfile(config, 'selected');
          
          // Assert
          expect(result.profiles, isEmpty);
          expect(result.selectedProfileId, isNull);
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should select first remaining profile when removing selected profile', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        
        final profile1 = UserProfile(id: 'user1', name: 'User 1');
        final profile2 = UserProfile(id: 'user2', name: 'User 2');
        final config = UserConfig(
          profiles: [profile1, profile2],
          selectedProfileId: 'user1',
        );
        
        try {
          // Act
          final result = await userUseCase.removeUserProfile(config, 'user1');
          
          // Assert
          expect(result.profiles, hasLength(1));
          expect(result.profiles.first.id, equals('user2'));
          expect(result.selectedProfileId, equals('user2'));
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should not change selection when removing non-selected profile', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        
        final profile1 = UserProfile(id: 'user1', name: 'User 1');
        final profile2 = UserProfile(id: 'user2', name: 'User 2');
        final config = UserConfig(
          profiles: [profile1, profile2],
          selectedProfileId: 'user1',
        );
        
        try {
          // Act
          final result = await userUseCase.removeUserProfile(config, 'user2');
          
          // Assert
          expect(result.profiles, hasLength(1));
          expect(result.profiles.first.id, equals('user1'));
          expect(result.selectedProfileId, equals('user1'));
        } finally {
          await tempDir.delete(recursive: true);
        }
      });
    });

    group('setSelectedProfile', () {
      test('should set selected profile successfully', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        
        final profile = UserProfile(id: 'select_test', name: 'Select Test');
        final config = UserConfig(profiles: [profile]);
        
        try {
          // Act
          final result = await userUseCase.setSelectedProfile(config, 'select_test');
          
          // Assert
          expect(result.selectedProfileId, equals('select_test'));
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should clear selection when setting to null', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        
        final profile = UserProfile(id: 'test', name: 'Test');
        final config = UserConfig(
          profiles: [profile],
          selectedProfileId: 'test',
        );
        
        try {
          // Act
          final result = await userUseCase.setSelectedProfile(config, null);
          
          // Assert
          expect(result.selectedProfileId, isNull);
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should throw exception when profile not found', () async {
        // Arrange
        final config = UserConfig();
        
        // Act & Assert
        expect(
          () => userUseCase.setSelectedProfile(config, 'non_existent'),
          throwsException,
        );
      });

      test('should change selection from one profile to another', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        
        final profile1 = UserProfile(id: 'user1', name: 'User 1');
        final profile2 = UserProfile(id: 'user2', name: 'User 2');
        final config = UserConfig(
          profiles: [profile1, profile2],
          selectedProfileId: 'user1',
        );
        
        try {
          // Act
          final result = await userUseCase.setSelectedProfile(config, 'user2');
          
          // Assert
          expect(result.selectedProfileId, equals('user2'));
          expect(result.profiles, hasLength(2)); // Profiles unchanged
        } finally {
          await tempDir.delete(recursive: true);
        }
      });
    });

    group('Error Handling and Edge Cases', () {
      test('should handle empty profile list operations', () async {
        // Arrange
        final emptyConfig = UserConfig();

        // Act & Assert - Operations on empty config should behave correctly
        expect(
          () => userUseCase.removeUserProfile(emptyConfig, 'any_id'),
          throwsException,
        );

        expect(
          () => userUseCase.updateUserProfile(emptyConfig, UserProfile(id: 'any', name: 'Any')),
          throwsException,
        );

        expect(
          () => userUseCase.setSelectedProfile(emptyConfig, 'any_id'),
          throwsException,
        );
      });

      test('should handle concurrent operations gracefully', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');

        final profile = UserProfile(id: 'concurrent', name: 'Concurrent Test');
        final config = UserConfig(profiles: [profile]);

        try {
          // Act - Multiple concurrent operations
          final futures = [
            userUseCase.setSelectedProfile(config, 'concurrent'),
            userUseCase.setSelectedProfile(config, null),
            userUseCase.setSelectedProfile(config, 'concurrent'),
          ];

          final results = await Future.wait(futures);

          // Assert - All operations should complete
          expect(results, hasLength(3));
          for (final result in results) {
            expect(result, isA<UserConfig>());
          }
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should handle special characters in profile data', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');

        final specialProfile = UserProfile(
          id: 'special_chars_测试_🎮',
          name: 'User with émojis 🎉 and 特殊字符',
        );
        final config = UserConfig();

        try {
          // Act
          final result = await userUseCase.addUserProfile(config, specialProfile);

          // Assert
          expect(result.profiles, hasLength(1));
          expect(result.profiles.first.id, equals('special_chars_测试_🎮'));
          expect(result.profiles.first.name, equals('User with émojis 🎉 and 特殊字符'));
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should handle very long profile names and IDs', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');

        final longId = 'a' * 1000;
        final longName = 'Very Long Name ' * 100;
        final longProfile = UserProfile(id: longId, name: longName);
        final config = UserConfig();

        try {
          // Act
          final result = await userUseCase.addUserProfile(config, longProfile);

          // Assert
          expect(result.profiles, hasLength(1));
          expect(result.profiles.first.id, equals(longId));
          expect(result.profiles.first.name, equals(longName));
        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should handle null and empty string edge cases', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');

        final emptyIdProfile = UserProfile(id: '', name: 'Empty ID');
        final emptyNameProfile = UserProfile(id: 'empty_name', name: '');
        final config = UserConfig();

        try {
          // Act & Assert - Should handle empty strings gracefully
          final result1 = await userUseCase.addUserProfile(config, emptyIdProfile);
          expect(result1.profiles, hasLength(1));

          final result2 = await userUseCase.addUserProfile(result1, emptyNameProfile);
          expect(result2.profiles, hasLength(2));
        } finally {
          await tempDir.delete(recursive: true);
        }
      });
    });

    group('Integration Scenarios', () {
      test('should handle complete user management workflow', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        final configPath = '${tempDir.path}/workflow_test.json';

        try {
          // Act - Complete workflow
          var config = await userUseCase.loadUserConfig(configPath);
          expect(config.profiles, isEmpty);

          // Add first user
          final user1 = UserProfile(id: 'user1', name: 'First User');
          config = await userUseCase.addUserProfile(config, user1);
          expect(config.profiles, hasLength(1));

          // Add second user
          final user2 = UserProfile(id: 'user2', name: 'Second User');
          config = await userUseCase.addUserProfile(config, user2);
          expect(config.profiles, hasLength(2));

          // Select first user
          config = await userUseCase.setSelectedProfile(config, 'user1');
          expect(config.selectedProfileId, equals('user1'));

          // Update first user
          final updatedUser1 = UserProfile(id: 'user1', name: 'Updated First User');
          config = await userUseCase.updateUserProfile(config, updatedUser1);
          expect(config.profiles.first.name, equals('Updated First User'));

          // Remove second user
          config = await userUseCase.removeUserProfile(config, 'user2');
          expect(config.profiles, hasLength(1));
          expect(config.selectedProfileId, equals('user1')); // Should remain selected

          // Clear selection
          config = await userUseCase.setSelectedProfile(config, null);
          expect(config.selectedProfileId, isNull);

          // Remove last user
          config = await userUseCase.removeUserProfile(config, 'user1');
          expect(config.profiles, isEmpty);
          expect(config.selectedProfileId, isNull);

        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should persist and reload configuration correctly', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        final configPath = '${tempDir.path}/persistence_test.json';

        try {
          // Act - Create and save config using custom path
          var config = UserConfig();
          final profile = UserProfile(id: 'persist_test', name: 'Persistence Test');

          // Add profile and save to custom path
          config = UserConfig(profiles: [profile]);
          await userUseCase.saveUserConfig(config, configPath);

          // Set selected profile and save again
          config = config.copyWith(selectedProfileId: 'persist_test');
          await userUseCase.saveUserConfig(config, configPath);

          // Reload config
          final reloadedConfig = await userUseCase.loadUserConfig(configPath);

          // Assert - Reloaded config should match saved config
          expect(reloadedConfig.profiles, hasLength(1));
          expect(reloadedConfig.profiles.first.id, equals('persist_test'));
          expect(reloadedConfig.profiles.first.name, equals('Persistence Test'));
          expect(reloadedConfig.selectedProfileId, equals('persist_test'));

        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should handle profile management with selection edge cases', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');

        try {
          // Start with multiple profiles
          var config = UserConfig();
          final profiles = [
            UserProfile(id: 'user1', name: 'User 1'),
            UserProfile(id: 'user2', name: 'User 2'),
            UserProfile(id: 'user3', name: 'User 3'),
          ];

          for (final profile in profiles) {
            config = await userUseCase.addUserProfile(config, profile);
          }

          // Select middle user
          config = await userUseCase.setSelectedProfile(config, 'user2');
          expect(config.selectedProfileId, equals('user2'));

          // Remove selected user - should select first remaining
          config = await userUseCase.removeUserProfile(config, 'user2');
          expect(config.selectedProfileId, equals('user1'));

          // Remove first user - should select next available
          config = await userUseCase.removeUserProfile(config, 'user1');
          expect(config.selectedProfileId, equals('user3'));

          // Remove last user - should clear selection
          config = await userUseCase.removeUserProfile(config, 'user3');
          expect(config.selectedProfileId, isNull);
          expect(config.profiles, isEmpty);

        } finally {
          await tempDir.delete(recursive: true);
        }
      });
    });

    group('Performance and Stress Tests', () {
      test('should handle large number of profiles', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        var config = UserConfig();

        try {
          // Act - Add many profiles
          for (int i = 0; i < 100; i++) {
            final profile = UserProfile(id: 'user_$i', name: 'User $i');
            config = await userUseCase.addUserProfile(config, profile);
          }

          // Assert
          expect(config.profiles, hasLength(100));

          // Test operations on large config
          config = await userUseCase.setSelectedProfile(config, 'user_50');
          expect(config.selectedProfileId, equals('user_50'));

          final updatedProfile = UserProfile(id: 'user_50', name: 'Updated User 50');
          config = await userUseCase.updateUserProfile(config, updatedProfile);
          expect(config.profiles[50].name, equals('Updated User 50'));

        } finally {
          await tempDir.delete(recursive: true);
        }
      });

      test('should handle rapid successive operations', () async {
        // Arrange
        final tempDir = Directory.systemTemp.createTempSync('user_test');
        var config = UserConfig();

        try {
          // Act - Rapid operations
          for (int i = 0; i < 10; i++) {
            final profile = UserProfile(id: 'rapid_$i', name: 'Rapid $i');
            config = await userUseCase.addUserProfile(config, profile);
            config = await userUseCase.setSelectedProfile(config, 'rapid_$i');
          }

          // Assert
          expect(config.profiles, hasLength(10));
          expect(config.selectedProfileId, equals('rapid_9'));

        } finally {
          await tempDir.delete(recursive: true);
        }
      });
    });
  });
}
