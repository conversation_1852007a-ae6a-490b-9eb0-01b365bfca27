import 'dart:io';
import 'package:dauntless/frameworks/game_config/game_config_state.dart';
import 'package:dauntless/frameworks/game_match/game_match_manager.dart';
import 'package:dauntless/frameworks/game_match/game_match_state.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/models/base/match_config.dart';
import 'package:dauntless/models/base/saved_game_state.dart';
import 'package:dauntless/repositories/save_state_repository.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:dauntless/use_cases/save_state_use_case.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockSaveStateRepository extends Mock implements SaveStateRepository {}
class MockGameMatchManager extends Mock implements GameMatchManager {}
class MockGameConfigState extends Mock implements GameConfigState {}
class MockSavedGameState extends Mock implements SavedGameState {}
class MockFileSystemEntity extends Mock implements FileSystemEntity {
  @override
  final String path;
  MockFileSystemEntity(this.path);
}

void main() {
  setUpAll(() {
    // Register fallback values for mocktail
    registerFallbackValue(createTestSavedGameState());
    registerFallbackValue(createTestGameMatchState());
  });

  group('MatchSaveUseCase', () {
    late MatchSaveUseCase useCase;
    late MockSaveStateRepository mockRepository;
    late MockGameMatchManager mockGameMatchManager;

    setUp(() {
      mockRepository = MockSaveStateRepository();
      mockGameMatchManager = MockGameMatchManager();
      useCase = MatchSaveUseCase(mockRepository);

      // Register mock GameMatchManager in GetIt
      if (GetIt.I.isRegistered<GameMatchManager>()) {
        GetIt.I.unregister<GameMatchManager>();
      }
      GetIt.I.registerSingleton<GameMatchManager>(mockGameMatchManager);

      // Set up default mock behavior
      when(() => mockGameMatchManager.state).thenReturn(createTestGameMatchState());
    });

    tearDown(() {
      GetIt.I.reset();
    });

    group('Constructor and Initial State', () {
      test('should initialize with repository dependency', () {
        expect(useCase, isNotNull);
        expect(useCase, isA<MatchSaveUseCase>());
      });
    });

    group('getSavePath()', () {
      test('should return null when basePath is null', () {
        // Arrange
        final gameConfigState = createGameConfigState(basePath: null);

        // Act
        final result = useCase.getSavePath(gameConfigState);

        // Assert
        expect(result, isNull);
      });

      test('should return null when loadedGameConfig is null', () {
        // Arrange
        final gameConfigState = createGameConfigState(
          basePath: '/test/path',
          loadedGameConfig: null,
        );

        // Act
        final result = useCase.getSavePath(gameConfigState);

        // Assert
        expect(result, isNull);
      });

      test('should return valid path when both basePath and loadedGameConfig are set', () {
        // Arrange
        final gameConfigState = createGameConfigState(
          basePath: '/test/path',
          loadedGameConfig: createTestGameConfig(),
        );

        // Act
        final result = useCase.getSavePath(gameConfigState);

        // Assert
        expect(result, isNotNull);
        expect(result, contains('/test/path'));
        expect(result, contains('test-match-id'));
      });

      test('should include match ID from GameMatchManager in path', () {
        // Arrange
        final gameConfigState = createGameConfigState(
          basePath: '/games/test',
          loadedGameConfig: createTestGameConfig(),
        );

        // Act
        final result = useCase.getSavePath(gameConfigState);

        // Assert
        expect(result, isNotNull);
        expect(result, contains('test-match-id'));
      });
    });

    group('saveSavedGameState()', () {
      test('should save game state when valid path is available', () async {
        // Arrange
        final gameConfigState = createGameConfigState(
          basePath: '/test/saves',
          loadedGameConfig: createTestGameConfig(),
        );
        final savedGameState = createTestSavedGameState();

        when(() => mockRepository.saveSavedGameState(any(), any()))
            .thenAnswer((_) async {});

        // Act
        await useCase.saveSavedGameState(gameConfigState, savedGameState);

        // Assert
        verify(() => mockRepository.saveSavedGameState(any(), savedGameState))
            .called(1);
      });

      test('should throw exception when save path is null', () async {
        // Arrange
        final gameConfigState = createGameConfigState(basePath: null);
        final savedGameState = createTestSavedGameState();

        // Act & Assert
        expect(
          () async => await useCase.saveSavedGameState(gameConfigState, savedGameState),
          throwsA(isA<Exception>()),
        );
      });

      test('should include turn count in save file name', () async {
        // Arrange
        final gameConfigState = createGameConfigState(
          basePath: '/test/saves',
          loadedGameConfig: createTestGameConfig(),
        );
        final savedGameState = createTestSavedGameState(turnCount: 5);

        when(() => mockRepository.saveSavedGameState(any(), any()))
            .thenAnswer((_) async {});

        // Act
        await useCase.saveSavedGameState(gameConfigState, savedGameState);

        // Assert
        final captured = verify(() => mockRepository.saveSavedGameState(
          captureAny(),
          savedGameState,
        )).captured;
        
        expect(captured.first, contains('0005.json'));
      });

      test('should handle repository save errors', () async {
        // Arrange
        final gameConfigState = createGameConfigState(
          basePath: '/test/saves',
          loadedGameConfig: createTestGameConfig(),
        );
        final savedGameState = createTestSavedGameState();

        when(() => mockRepository.saveSavedGameState(any(), any()))
            .thenThrow(Exception('Save failed'));

        // Act & Assert
        expect(
          () async => await useCase.saveSavedGameState(gameConfigState, savedGameState),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('getMostRecentSaveForGame()', () {
      test('should return null when save path is null', () async {
        // Arrange
        final gameConfigState = createGameConfigState(basePath: null);

        // Act & Assert
        expect(
          () async => await useCase.getMostRecentSaveForGame(gameConfigState),
          throwsA(isA<Exception>()),
        );
      });

      test('should return null when no save files exist', () async {
        // Arrange
        final gameConfigState = createGameConfigState(
          basePath: '/test/saves',
          loadedGameConfig: createTestGameConfig(),
        );

        when(() => mockRepository.getGameSaves(any()))
            .thenAnswer((_) async => []);

        // Act
        final result = await useCase.getMostRecentSaveForGame(gameConfigState);

        // Assert
        expect(result, isNull);
      });

      test('should return most recent save when files exist', () async {
        // Arrange
        final gameConfigState = createGameConfigState(
          basePath: '/test/saves',
          loadedGameConfig: createTestGameConfig(),
        );
        final mockSaves = [
          MockFileSystemEntity('/test/saves/0001.json'),
          MockFileSystemEntity('/test/saves/0003.json'),
          MockFileSystemEntity('/test/saves/0002.json'),
        ];
        final expectedSave = createTestSavedGameState();

        when(() => mockRepository.getGameSaves(any()))
            .thenAnswer((_) async => mockSaves);
        when(() => mockRepository.loadSavedGameState(any()))
            .thenAnswer((_) async => expectedSave);

        // Act
        final result = await useCase.getMostRecentSaveForGame(gameConfigState);

        // Assert
        expect(result, equals(expectedSave));
        verify(() => mockRepository.loadSavedGameState('/test/saves/0003.json'))
            .called(1);
      });

      test('should sort save files by path for most recent', () async {
        // Arrange
        final gameConfigState = createGameConfigState(
          basePath: '/test/saves',
          loadedGameConfig: createTestGameConfig(),
        );
        final mockSaves = [
          MockFileSystemEntity('/test/saves/0001.json'),
          MockFileSystemEntity('/test/saves/0005.json'),
          MockFileSystemEntity('/test/saves/0003.json'),
        ];

        when(() => mockRepository.getGameSaves(any()))
            .thenAnswer((_) async => mockSaves);
        when(() => mockRepository.loadSavedGameState(any()))
            .thenAnswer((_) async => createTestSavedGameState());

        // Act
        await useCase.getMostRecentSaveForGame(gameConfigState);

        // Assert - Should load the highest numbered file (0005.json)
        verify(() => mockRepository.loadSavedGameState('/test/saves/0005.json'))
            .called(1);
      });

      test('should handle repository load errors', () async {
        // Arrange
        final gameConfigState = createGameConfigState(
          basePath: '/test/saves',
          loadedGameConfig: createTestGameConfig(),
        );
        final mockSaves = [MockFileSystemEntity('/test/saves/0001.json')];

        when(() => mockRepository.getGameSaves(any()))
            .thenAnswer((_) async => mockSaves);
        when(() => mockRepository.loadSavedGameState(any()))
            .thenThrow(Exception('Load failed'));

        // Act & Assert
        expect(
          () async => await useCase.getMostRecentSaveForGame(gameConfigState),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('Integration Scenarios', () {
      test('should handle complete save and load workflow', () async {
        // Arrange
        final gameConfigState = createGameConfigState(
          basePath: '/test/integration',
          loadedGameConfig: createTestGameConfig(),
        );
        final savedGameState = createTestSavedGameState();
        final mockSaves = [MockFileSystemEntity('/test/integration/0001.json')];

        when(() => mockRepository.saveSavedGameState(any(), any()))
            .thenAnswer((_) async {});
        when(() => mockRepository.getGameSaves(any()))
            .thenAnswer((_) async => mockSaves);
        when(() => mockRepository.loadSavedGameState(any()))
            .thenAnswer((_) async => savedGameState);

        // Act - Save
        await useCase.saveSavedGameState(gameConfigState, savedGameState);

        // Act - Load
        final result = await useCase.getMostRecentSaveForGame(gameConfigState);

        // Assert
        expect(result, equals(savedGameState));
        verify(() => mockRepository.saveSavedGameState(any(), savedGameState))
            .called(1);
        verify(() => mockRepository.loadSavedGameState(any())).called(1);
      });

      test('should handle multiple save files correctly', () async {
        // Arrange
        final gameConfigState = createGameConfigState(
          basePath: '/test/multiple',
          loadedGameConfig: createTestGameConfig(),
        );
        final mockSaves = [
          MockFileSystemEntity('/test/multiple/0001.json'),
          MockFileSystemEntity('/test/multiple/0002.json'),
          MockFileSystemEntity('/test/multiple/0003.json'),
        ];

        when(() => mockRepository.getGameSaves(any()))
            .thenAnswer((_) async => mockSaves);
        when(() => mockRepository.loadSavedGameState(any()))
            .thenAnswer((_) async => createTestSavedGameState());

        // Act
        final result = await useCase.getMostRecentSaveForGame(gameConfigState);

        // Assert
        expect(result, isNotNull);
        verify(() => mockRepository.loadSavedGameState('/test/multiple/0003.json'))
            .called(1);
      });
    });

    group('Error Handling', () {
      test('should handle GetIt dependency errors gracefully', () {
        // Arrange
        GetIt.I.unregister<GameMatchManager>();
        final gameConfigState = createGameConfigState(
          basePath: '/test/saves',
          loadedGameConfig: createTestGameConfig(),
        );

        // Act & Assert
        expect(
          () => useCase.getSavePath(gameConfigState),
          throwsA(isA<StateError>()),
        );
      });

      test('should handle invalid game config states', () async {
        // Arrange - null basePath should cause getSavePath to return null
        final gameConfigState = createGameConfigState(
          basePath: null,
          loadedGameConfig: createTestGameConfig(),
        );
        final savedGameState = createTestSavedGameState();

        // Act & Assert
        expect(
          () async => await useCase.saveSavedGameState(gameConfigState, savedGameState),
          throwsA(isA<Exception>()),
        );
      });
    });
  });
}

// Helper functions
GameConfigState createGameConfigState({
  String? basePath,
  GameConfig? loadedGameConfig,
}) {
  return GameConfigState(
    basePath: basePath,
    loadedGameConfig: loadedGameConfig,
    processingStatus: ProcessingStatus.loaded,
  );
}

GameConfig createTestGameConfig() {
  return const GameConfig(
    id: 'test-config',
    name: 'Test Game Config',
  );
}

SavedGameState createTestSavedGameState({int? turnCount}) {
  return SavedGameState(
    matchState: createTestGameMatchState(turnCount: turnCount),
  );
}

GameMatchState createTestGameMatchState({int? turnCount}) {
  return GameMatchState(
    matchConfig: const MatchConfig(
      gameId: 'test-game-id',
      selectedGameMode: GameMode.hotSeat,
      hostId: 'test-host-id',
      matchId: 'test-match-id',
    ),
    userPlayerId: 'test-player-id',
    turnCount: turnCount ?? 1,
    hands: {},
    resources: {},
    loadedCardClasses: {},
    currentTurnActions: [],
    processingStatus: ProcessingStatus.start,
  );
}
