import 'package:test/test.dart';
import 'package:dauntless/use_cases/card_children_use_case.dart';
import 'package:dauntless/models/base/game_card.dart';

void main() {
  group('CardChildrenUseCase', () {
    late CardChildrenUseCase useCase;

    // Test data - Instantiated GameCard objects with location relationships
    late GameCard parentLocationCard;
    late GameCard childVehicleCard1;
    late GameCard childVehicleCard2;
    late GameCard childBuildingCard;
    late GameCard grandchildCard;
    late GameCard unrelatedCard;
    late GameCard orphanCard;
    late List<GameCard> allTestCards;

    setUp(() {
      useCase = CardChildrenUseCase();

      // Create test data with hierarchical relationships
      parentLocationCard = const GameCard(
        id: 'location-1',
        name: 'Tatooine',
        type: CardType.location,
        classId: 'tatooine-class',
        // No locationId - this is a root location
      );

      childVehicleCard1 = const GameCard(
        id: 'vehicle-1',
        name: 'X-<PERSON>',
        type: CardType.vehicle,
        classId: 'x-wing-class',
        locationId: 'location-1', // Child of parentLocationCard
      );

      childVehicleCard2 = const GameCard(
        id: 'vehicle-2',
        name: 'Y-Wing',
        type: CardType.vehicle,
        classId: 'y-wing-class',
        locationId: 'location-1', // Child of parentLocationCard
      );

      childBuildingCard = const GameCard(
        id: 'building-1',
        name: 'Cantina',
        type: CardType.building,
        classId: 'cantina-class',
        locationId: 'location-1', // Child of parentLocationCard
      );

      grandchildCard = const GameCard(
        id: 'character-1',
        name: 'Luke Skywalker',
        type: CardType.character,
        classId: 'luke-class',
        locationId: 'vehicle-1', // Child of childVehicleCard1 (grandchild of location-1)
      );

      unrelatedCard = const GameCard(
        id: 'vehicle-3',
        name: 'TIE Fighter',
        type: CardType.vehicle,
        classId: 'tie-fighter-class',
        locationId: 'location-2', // Different parent
      );

      orphanCard = const GameCard(
        id: 'vehicle-4',
        name: 'Millennium Falcon',
        type: CardType.vehicle,
        classId: 'falcon-class',
        // No locationId - orphan card
      );

      allTestCards = [
        parentLocationCard,
        childVehicleCard1,
        childVehicleCard2,
        childBuildingCard,
        grandchildCard,
        unrelatedCard,
        orphanCard,
      ];
    });

    group('Constructor and Initialization', () {
      test('should create instance successfully', () {
        expect(useCase, isNotNull);
        expect(useCase, isA<CardChildrenUseCase>());
      });
    });

    group('getAllCardChildren', () {
      test('should return children grouped by card type', () {
        // Act
        final result = useCase.getAllCardChildren('location-1', allTestCards);

        // Assert
        expect(result, isA<Map<CardType, List<GameCard>>>());
        expect(result.keys, containsAll(CardType.values));

        // Check specific children
        expect(result[CardType.vehicle], hasLength(2));
        expect(result[CardType.vehicle]!.map((c) => c.id), containsAll(['vehicle-1', 'vehicle-2']));

        expect(result[CardType.building], hasLength(1));
        expect(result[CardType.building]!.first.id, equals('building-1'));

        expect(result[CardType.character], isEmpty); // No direct character children
        expect(result[CardType.location], isEmpty); // No location children
      });

      test('should filter cards by parent location ID correctly', () {
        // Act
        final result = useCase.getAllCardChildren('location-1', allTestCards);

        // Assert - Only cards with locationId = 'location-1' should be included
        expect(result[CardType.vehicle], hasLength(2));
        expect(result[CardType.building], hasLength(1));

        // Verify specific cards are included
        final allChildren = result.values.expand((list) => list).toList();
        expect(allChildren.map((c) => c.id), containsAll(['vehicle-1', 'vehicle-2', 'building-1']));
        expect(allChildren.map((c) => c.id), isNot(contains('vehicle-3'))); // Different parent
        expect(allChildren.map((c) => c.id), isNot(contains('character-1'))); // Grandchild, not direct child
      });

      test('should handle empty results gracefully', () {
        // Act
        final result = useCase.getAllCardChildren('nonexistent-location', allTestCards);

        // Assert
        expect(result, isA<Map<CardType, List<GameCard>>>());
        for (final cardType in CardType.values) {
          expect(result[cardType], isEmpty);
        }
      });

      test('should handle empty card list', () {
        // Act
        final result = useCase.getAllCardChildren('location-1', []);

        // Assert
        expect(result, isA<Map<CardType, List<GameCard>>>());
        for (final cardType in CardType.values) {
          expect(result[cardType], isEmpty);
        }
      });

      test('should initialize all card types even when no children exist', () {
        // Act
        final result = useCase.getAllCardChildren('nonexistent-location', allTestCards);

        // Assert - All card types should have entries, even if empty
        for (final cardType in CardType.values) {
          expect(result.containsKey(cardType), isTrue);
          expect(result[cardType], isA<List<GameCard>>());
        }
      });
    });

    group('getCardChildren', () {
      test('should return children of specific card type', () {
        // Act
        final result = useCase.getCardChildren('location-1', CardType.vehicle, allTestCards);

        // Assert
        expect(result, hasLength(2));
        expect(result.map((c) => c.id), containsAll(['vehicle-1', 'vehicle-2']));
        expect(result.every((c) => c.type == CardType.vehicle), isTrue);
        expect(result.every((c) => c.locationId == 'location-1'), isTrue);
      });

      test('should return empty list for card type with no children', () {
        // Act
        final result = useCase.getCardChildren('location-1', CardType.character, allTestCards);

        // Assert
        expect(result, isEmpty);
      });

      test('should return empty list when parent has no children', () {
        // Act
        final result = useCase.getCardChildren('nonexistent-location', CardType.vehicle, allTestCards);

        // Assert
        expect(result, isEmpty);
      });

      test('should filter by both parent and card type', () {
        // Act - Get vehicles in vehicle-1 (should find character-1)
        final result = useCase.getCardChildren('vehicle-1', CardType.character, allTestCards);

        // Assert
        expect(result, hasLength(1));
        expect(result.first.id, equals('character-1'));
        expect(result.first.type, equals(CardType.character));
        expect(result.first.locationId, equals('vehicle-1'));
      });

      test('should handle empty card list', () {
        // Act
        final result = useCase.getCardChildren('location-1', CardType.vehicle, []);

        // Assert
        expect(result, isEmpty);
      });
    });

    group('getAllCardRelationships', () {
      test('should return all parent-child relationships', () {
        // Act
        final result = useCase.getAllCardRelationships(allTestCards);

        // Assert
        expect(result, isA<Map<GameCardId, Map<CardType, List<GameCard>>>>());
        expect(result.keys, containsAll(['location-1', 'vehicle-1', 'location-2']));

        // Check location-1 relationships
        final location1Children = result['location-1']!;
        expect(location1Children[CardType.vehicle], hasLength(2));
        expect(location1Children[CardType.building], hasLength(1));
        expect(location1Children[CardType.character] ?? [], isEmpty);

        // Check vehicle-1 relationships (has character child)
        final vehicle1Children = result['vehicle-1']!;
        expect(vehicle1Children[CardType.character], hasLength(1));
        expect(vehicle1Children[CardType.character]!.first.id, equals('character-1'));

        // Check location-2 relationships
        final location2Children = result['location-2']!;
        expect(location2Children[CardType.vehicle], hasLength(1));
        expect(location2Children[CardType.vehicle]!.first.id, equals('vehicle-3'));
      });

      test('should handle cards without location relationships', () {
        // Arrange - Cards without locationId should not appear in relationships
        final cardsWithoutLocation = [orphanCard]; // orphanCard has no locationId

        // Act
        final result = useCase.getAllCardRelationships(cardsWithoutLocation);

        // Assert
        expect(result, isEmpty); // No relationships since no cards have locationId
      });

      test('should handle empty card list', () {
        // Act
        final result = useCase.getAllCardRelationships([]);

        // Assert
        expect(result, isEmpty);
      });

      test('should group multiple children by type correctly', () {
        // Act
        final result = useCase.getAllCardRelationships(allTestCards);

        // Assert - location-1 should have multiple vehicle children
        final location1Children = result['location-1']!;
        expect(location1Children[CardType.vehicle], hasLength(2));

        final vehicleIds = location1Children[CardType.vehicle]!.map((c) => c.id).toList();
        expect(vehicleIds, containsAll(['vehicle-1', 'vehicle-2']));
      });
    });

    group('getDirectChildren', () {
      test('should return all direct children regardless of type', () {
        // Act
        final result = useCase.getDirectChildren('location-1', allTestCards);

        // Assert
        expect(result, hasLength(3)); // 2 vehicles + 1 building
        expect(result.map((c) => c.id), containsAll(['vehicle-1', 'vehicle-2', 'building-1']));
        expect(result.every((c) => c.locationId == 'location-1'), isTrue);
      });

      test('should not include grandchildren', () {
        // Act
        final result = useCase.getDirectChildren('location-1', allTestCards);

        // Assert - character-1 is a grandchild (in vehicle-1), not direct child
        expect(result.map((c) => c.id), isNot(contains('character-1')));
      });

      test('should handle parent with no children', () {
        // Act
        final result = useCase.getDirectChildren('nonexistent-parent', allTestCards);

        // Assert
        expect(result, isEmpty);
      });
    });

    group('getAllDescendants', () {
      test('should return all descendants recursively', () {
        // Act
        final result = useCase.getAllDescendants('location-1', allTestCards);

        // Assert
        expect(result, hasLength(4)); // 2 vehicles + 1 building + 1 character (grandchild)
        expect(result.map((c) => c.id), containsAll(['vehicle-1', 'vehicle-2', 'building-1', 'character-1']));
      });

      test('should include grandchildren and deeper descendants', () {
        // Act
        final result = useCase.getAllDescendants('location-1', allTestCards);

        // Assert - character-1 is a grandchild (location-1 -> vehicle-1 -> character-1)
        expect(result.map((c) => c.id), contains('character-1'));
      });

      test('should handle parent with no descendants', () {
        // Act
        final result = useCase.getAllDescendants('building-1', allTestCards); // Building has no children

        // Assert
        expect(result, isEmpty);
      });

      test('should handle circular references gracefully', () {
        // Arrange - Create cards with circular reference
        final cardA = const GameCard(
          id: 'card-a',
          name: 'Card A',
          type: CardType.vehicle,
          classId: 'class-a',
          locationId: 'card-b',
        );
        final cardB = const GameCard(
          id: 'card-b',
          name: 'Card B',
          type: CardType.vehicle,
          classId: 'class-b',
          locationId: 'card-a',
        );
        final circularCards = [cardA, cardB];

        // Act & Assert - Should not hang or crash
        expect(() => useCase.getAllDescendants('card-a', circularCards), returnsNormally);
      });
    });

    group('getParent', () {
      test('should return parent card when it exists', () {
        // Act
        final result = useCase.getParent(childVehicleCard1, allTestCards);

        // Assert
        expect(result, isNotNull);
        expect(result!.id, equals('location-1'));
        expect(result.name, equals('Tatooine'));
      });

      test('should return null when card has no parent', () {
        // Act
        final result = useCase.getParent(parentLocationCard, allTestCards); // Root card

        // Assert
        expect(result, isNull);
      });

      test('should return null when parent not found in card list', () {
        // Arrange - Card list without the parent
        final cardsWithoutParent = [childVehicleCard1]; // Missing parent location-1

        // Act
        final result = useCase.getParent(childVehicleCard1, cardsWithoutParent);

        // Assert
        expect(result, isNull);
      });
    });

    group('getRootParent', () {
      test('should return root parent for nested hierarchy', () {
        // Act - character-1 is nested: location-1 -> vehicle-1 -> character-1
        final result = useCase.getRootParent(grandchildCard, allTestCards);

        // Assert
        expect(result.id, equals('location-1')); // Should traverse up to root
        expect(result.locationId, isNull); // Root has no parent
      });

      test('should return self when card is already root', () {
        // Act
        final result = useCase.getRootParent(parentLocationCard, allTestCards);

        // Assert
        expect(result.id, equals('location-1'));
        expect(result, equals(parentLocationCard));
      });

      test('should handle orphan cards', () {
        // Act
        final result = useCase.getRootParent(orphanCard, allTestCards);

        // Assert
        expect(result, equals(orphanCard)); // Orphan is its own root
      });
    });

    group('isAncestor', () {
      test('should return true for direct parent', () {
        // Act
        final result = useCase.isAncestor(parentLocationCard, childVehicleCard1, allTestCards);

        // Assert
        expect(result, isTrue);
      });

      test('should return true for grandparent', () {
        // Act - location-1 is grandparent of character-1
        final result = useCase.isAncestor(parentLocationCard, grandchildCard, allTestCards);

        // Assert
        expect(result, isTrue);
      });

      test('should return false for non-ancestor', () {
        // Act
        final result = useCase.isAncestor(childVehicleCard1, unrelatedCard, allTestCards);

        // Assert
        expect(result, isFalse);
      });

      test('should return false when comparing card to itself', () {
        // Act
        final result = useCase.isAncestor(parentLocationCard, parentLocationCard, allTestCards);

        // Assert
        expect(result, isFalse); // A card is not an ancestor of itself
      });
    });

    group('Edge Cases and Performance', () {
      test('should handle large datasets efficiently', () {
        // Arrange
        final largeCardList = List.generate(1000, (index) => GameCard(
          id: 'card-$index',
          name: 'Card $index',
          type: CardType.vehicle,
          classId: 'class-$index',
          locationId: index % 2 == 0 ? 'location-1' : 'location-2',
        ));

        // Act
        final result = useCase.getAllCardChildren('location-1', largeCardList);

        // Assert
        expect(result[CardType.vehicle], hasLength(500)); // Half of the cards
      });

      test('should maintain data consistency across multiple calls', () {
        // Act
        final result1 = useCase.getCardChildren('location-1', CardType.vehicle, allTestCards);
        final result2 = useCase.getCardChildren('location-1', CardType.vehicle, allTestCards);

        // Assert
        expect(result1, equals(result2));
        expect(result1.length, equals(result2.length));
      });

      test('should handle null location IDs gracefully', () {
        // Arrange - Mix of cards with and without locationId
        final mixedCards = [parentLocationCard, orphanCard, childVehicleCard1];

        // Act
        final result = useCase.getAllCardChildren('location-1', mixedCards);

        // Assert
        expect(result[CardType.vehicle], hasLength(1)); // Only childVehicleCard1
        expect(result[CardType.vehicle]!.first.id, equals('vehicle-1'));
      });
    });

    group('Integration Scenarios', () {
      test('should work with complex hierarchical structures', () {
        // Act - Test complete hierarchy traversal
        final allChildren = useCase.getAllCardChildren('location-1', allTestCards);
        final allDescendants = useCase.getAllDescendants('location-1', allTestCards);
        final relationships = useCase.getAllCardRelationships(allTestCards);

        // Assert - Verify consistency across methods
        final directChildrenCount = allChildren.values.map((list) => list.length).reduce((a, b) => a + b);
        expect(allDescendants.length, greaterThan(directChildrenCount)); // Descendants include grandchildren
        expect(relationships.containsKey('location-1'), isTrue);
        expect(relationships.containsKey('vehicle-1'), isTrue); // vehicle-1 has character child
      });

      test('should handle real-world game state scenarios', () {
        // Arrange - Simulate a typical game state with multiple players and locations
        final gameStateCards = [
          // Locations
          const GameCard(id: 'tatooine', name: 'Tatooine', type: CardType.location, classId: 'tatooine-class'),
          const GameCard(id: 'hoth', name: 'Hoth', type: CardType.location, classId: 'hoth-class'),

          // Vehicles in locations
          const GameCard(id: 'falcon', name: 'Millennium Falcon', type: CardType.vehicle, classId: 'falcon-class', locationId: 'tatooine'),
          const GameCard(id: 'xwing', name: 'X-Wing', type: CardType.vehicle, classId: 'xwing-class', locationId: 'hoth'),

          // Characters in vehicles
          const GameCard(id: 'han', name: 'Han Solo', type: CardType.character, classId: 'han-class', locationId: 'falcon'),
          const GameCard(id: 'luke', name: 'Luke Skywalker', type: CardType.character, classId: 'luke-class', locationId: 'xwing'),
        ];

        // Act
        final tatooineChildren = useCase.getAllCardChildren('tatooine', gameStateCards);
        final hothChildren = useCase.getAllCardChildren('hoth', gameStateCards);
        final allRelationships = useCase.getAllCardRelationships(gameStateCards);

        // Assert
        expect(tatooineChildren[CardType.vehicle], hasLength(1));
        expect(tatooineChildren[CardType.vehicle]!.first.id, equals('falcon'));

        expect(hothChildren[CardType.vehicle], hasLength(1));
        expect(hothChildren[CardType.vehicle]!.first.id, equals('xwing'));

        expect(allRelationships.keys, containsAll(['tatooine', 'hoth', 'falcon', 'xwing']));
      });
    });
  });
}
