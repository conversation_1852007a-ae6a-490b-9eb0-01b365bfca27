import 'package:test/test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:dauntless/use_cases/game_config_use_case.dart';
import 'package:dauntless/repositories/game_config_repository.dart';
import 'package:dauntless/models/base/game_config.dart';

// Mock classes
class MockGameConfigRepository extends Mock implements GameConfigRepository {}

void main() {
  group('GameConfigUseCase', () {
    late MockGameConfigRepository mockRepository;
    late GameConfigUseCase useCase;

    setUp(() {
      mockRepository = MockGameConfigRepository();
      useCase = GameConfigUseCase(mockRepository);
    });

    group('Constructor and Initialization', () {
      test('should create instance with repository dependency', () {
        // Act & Assert
        expect(useCase, isA<GameConfigUseCase>());
        expect(useCase, isNotNull);
      });

      test('should accept repository in constructor', () {
        // Arrange
        final repository = MockGameConfigRepository();
        
        // Act
        final instance = GameConfigUseCase(repository);
        
        // Assert
        expect(instance, isA<GameConfigUseCase>());
      });

      test('should handle multiple instances independently', () {
        // Arrange
        final repo1 = MockGameConfigRepository();
        final repo2 = MockGameConfigRepository();
        
        // Act
        final useCase1 = GameConfigUseCase(repo1);
        final useCase2 = GameConfigUseCase(repo2);
        
        // Assert
        expect(useCase1, isNot(same(useCase2)));
        expect(useCase1, isA<GameConfigUseCase>());
        expect(useCase2, isA<GameConfigUseCase>());
      });
    });

    group('loadGameConfig', () {
      test('should load game config with correct path construction', () async {
        // Arrange
        const basePath = '/games/liberator';
        const expectedPath = '/games/liberator/data/config.json';
        final expectedConfig = GameConfig(
          id: 'liberator',
          name: 'Liberator Game',
        );
        
        when(() => mockRepository.loadGameConfig(expectedPath))
            .thenAnswer((_) async => expectedConfig);
        
        // Act
        final result = await useCase.loadGameConfig(basePath);
        
        // Assert
        expect(result, equals(expectedConfig));
        verify(() => mockRepository.loadGameConfig(expectedPath)).called(1);
      });

      test('should handle empty base path', () async {
        // Arrange
        const basePath = '';
        const expectedPath = '/data/config.json';
        final expectedConfig = GameConfig(
          id: 'test',
          name: 'Test Config',
        );
        
        when(() => mockRepository.loadGameConfig(expectedPath))
            .thenAnswer((_) async => expectedConfig);
        
        // Act
        final result = await useCase.loadGameConfig(basePath);
        
        // Assert
        expect(result, equals(expectedConfig));
        verify(() => mockRepository.loadGameConfig(expectedPath)).called(1);
      });

      test('should handle base path with trailing slash', () async {
        // Arrange
        const basePath = '/games/liberator/';
        const expectedPath = '/games/liberator//data/config.json';
        final expectedConfig = GameConfig(
          id: 'liberator',
          name: 'Liberator Game',
        );
        
        when(() => mockRepository.loadGameConfig(expectedPath))
            .thenAnswer((_) async => expectedConfig);
        
        // Act
        final result = await useCase.loadGameConfig(basePath);
        
        // Assert
        expect(result, equals(expectedConfig));
        verify(() => mockRepository.loadGameConfig(expectedPath)).called(1);
      });

      test('should propagate repository errors', () async {
        // Arrange
        const basePath = '/invalid/path';
        const expectedPath = '/invalid/path/data/config.json';
        
        when(() => mockRepository.loadGameConfig(expectedPath))
            .thenThrow(Exception('Config file not found'));
        
        // Act & Assert
        expect(
          () => useCase.loadGameConfig(basePath),
          throwsA(isA<Exception>()),
        );
        verify(() => mockRepository.loadGameConfig(expectedPath)).called(1);
      });
    });

    group('loadLocalGameConfigs', () {
      test('should return list of available game configs', () async {
        // Arrange
        final expectedConfigs = <GameConfig>[
          GameConfig(
            id: 'liberator',
            name: 'Liberator',
          ),
          GameConfig(
            id: 'conquest',
            name: 'Conquest',
          ),
        ];
        
        when(() => mockRepository.loadAvailableGameConfigs())
            .thenAnswer((_) async => expectedConfigs);
        
        // Act
        final result = await useCase.loadLocalGameConfigs();
        
        // Assert
        expect(result, equals(expectedConfigs));
        expect(result.length, equals(2));
        verify(() => mockRepository.loadAvailableGameConfigs()).called(1);
      });

      test('should return empty list when no configs available', () async {
        // Arrange
        when(() => mockRepository.loadAvailableGameConfigs())
            .thenAnswer((_) async => <GameConfig>[]);
        
        // Act
        final result = await useCase.loadLocalGameConfigs();
        
        // Assert
        expect(result, isEmpty);
        verify(() => mockRepository.loadAvailableGameConfigs()).called(1);
      });

      test('should propagate repository errors', () async {
        // Arrange
        when(() => mockRepository.loadAvailableGameConfigs())
            .thenThrow(Exception('Failed to load configs'));

        // Act & Assert
        expect(
          () => useCase.loadLocalGameConfigs(),
          throwsA(isA<Exception>()),
        );
        verify(() => mockRepository.loadAvailableGameConfigs()).called(1);
      });
    });

    group('getGameConfigById', () {
      test('should return config when ID exists', () async {
        // Arrange
        const targetId = 'liberator';
        final availableConfigs = <GameConfig>[
          GameConfig(
            id: 'conquest',
            name: 'Conquest',
          ),
          GameConfig(
            id: 'liberator',
            name: 'Liberator',
          ),
          GameConfig(
            id: 'empire',
            name: 'Empire',
          ),
        ];

        when(() => mockRepository.loadAvailableGameConfigs())
            .thenAnswer((_) async => availableConfigs);

        // Act
        final result = await useCase.getGameConfigById(targetId);

        // Assert
        expect(result, isNotNull);
        expect(result!.id, equals(targetId));
        expect(result.name, equals('Liberator'));
        verify(() => mockRepository.loadAvailableGameConfigs()).called(1);
      });

      test('should return null when ID does not exist', () async {
        // Arrange
        const targetId = 'nonexistent';
        final availableConfigs = <GameConfig>[
          GameConfig(
            id: 'liberator',
            name: 'Liberator',
          ),
          GameConfig(
            id: 'conquest',
            name: 'Conquest',
          ),
        ];

        when(() => mockRepository.loadAvailableGameConfigs())
            .thenAnswer((_) async => availableConfigs);

        // Act & Assert
        expect(
          () => useCase.getGameConfigById(targetId),
          throwsA(isA<Exception>()),
        );
        verify(() => mockRepository.loadAvailableGameConfigs()).called(1);
      });

      test('should handle empty config list', () async {
        // Arrange
        const targetId = 'any_id';

        when(() => mockRepository.loadAvailableGameConfigs())
            .thenAnswer((_) async => <GameConfig>[]);

        // Act & Assert
        expect(
          () => useCase.getGameConfigById(targetId),
          throwsA(isA<Exception>()),
        );
        verify(() => mockRepository.loadAvailableGameConfigs()).called(1);
      });

      test('should handle case-sensitive ID matching', () async {
        // Arrange
        const targetId = 'Liberator'; // Different case
        final availableConfigs = <GameConfig>[
          GameConfig(
            id: 'liberator', // lowercase
            name: 'Liberator',
          ),
        ];

        when(() => mockRepository.loadAvailableGameConfigs())
            .thenAnswer((_) async => availableConfigs);

        // Act & Assert
        expect(
          () => useCase.getGameConfigById(targetId),
          throwsA(isA<Exception>()),
        );
        verify(() => mockRepository.loadAvailableGameConfigs()).called(1);
      });

      test('should return first match when duplicate IDs exist', () async {
        // Arrange
        const targetId = 'liberator';
        final availableConfigs = <GameConfig>[
          GameConfig(
            id: 'liberator',
            name: 'Liberator First',
          ),
          GameConfig(
            id: 'liberator',
            name: 'Liberator Second',
          ),
        ];

        when(() => mockRepository.loadAvailableGameConfigs())
            .thenAnswer((_) async => availableConfigs);

        // Act
        final result = await useCase.getGameConfigById(targetId);

        // Assert
        expect(result, isNotNull);
        expect(result!.name, equals('Liberator First'));
        verify(() => mockRepository.loadAvailableGameConfigs()).called(1);
      });

      test('should propagate repository errors', () async {
        // Arrange
        const targetId = 'any_id';

        when(() => mockRepository.loadAvailableGameConfigs())
            .thenThrow(Exception('Repository error'));

        // Act & Assert
        expect(
          () => useCase.getGameConfigById(targetId),
          throwsA(isA<Exception>()),
        );
        verify(() => mockRepository.loadAvailableGameConfigs()).called(1);
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle null or empty string IDs in getGameConfigById', () async {
        // Arrange
        final availableConfigs = <GameConfig>[
          GameConfig(
            id: 'liberator',
            name: 'Liberator',
          ),
        ];

        when(() => mockRepository.loadAvailableGameConfigs())
            .thenAnswer((_) async => availableConfigs);

        // Act & Assert - Empty string
        expect(
          () => useCase.getGameConfigById(''),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle special characters in base path', () async {
        // Arrange
        const basePath = '/games/test with spaces & symbols!';
        const expectedPath = '/games/test with spaces & symbols!/data/config.json';
        final expectedConfig = GameConfig(
          id: 'test',
          name: 'Test Config',
        );

        when(() => mockRepository.loadGameConfig(expectedPath))
            .thenAnswer((_) async => expectedConfig);

        // Act
        final result = await useCase.loadGameConfig(basePath);

        // Assert
        expect(result, equals(expectedConfig));
        verify(() => mockRepository.loadGameConfig(expectedPath)).called(1);
      });

      test('should handle very long base paths', () async {
        // Arrange
        final longPath = '/very/long/path' * 20; // Create a very long path
        final expectedPath = '$longPath/data/config.json';
        final expectedConfig = GameConfig(
          id: 'test',
          name: 'Long Path Test',
        );

        when(() => mockRepository.loadGameConfig(expectedPath))
            .thenAnswer((_) async => expectedConfig);

        // Act
        final result = await useCase.loadGameConfig(longPath);

        // Assert
        expect(result, equals(expectedConfig));
        verify(() => mockRepository.loadGameConfig(expectedPath)).called(1);
      });

      test('should handle concurrent calls to loadLocalGameConfigs', () async {
        // Arrange
        final expectedConfigs = <GameConfig>[
          GameConfig(
            id: 'liberator',
            name: 'Liberator',
          ),
        ];

        when(() => mockRepository.loadAvailableGameConfigs())
            .thenAnswer((_) async => expectedConfigs);

        // Act - Make multiple concurrent calls
        final futures = List.generate(5, (_) => useCase.loadLocalGameConfigs());
        final results = await Future.wait(futures);

        // Assert
        for (final result in results) {
          expect(result, equals(expectedConfigs));
        }
        verify(() => mockRepository.loadAvailableGameConfigs()).called(5);
      });
    });

    group('Integration Scenarios', () {
      test('should work in typical game config loading workflow', () async {
        // Arrange
        const basePath = '/games/liberator';
        const configId = 'liberator';

        final gameConfig = GameConfig(
          id: configId,
          name: 'Liberator',
        );

        final availableConfigs = <GameConfig>[gameConfig];

        when(() => mockRepository.loadGameConfig('$basePath/data/config.json'))
            .thenAnswer((_) async => gameConfig);
        when(() => mockRepository.loadAvailableGameConfigs())
            .thenAnswer((_) async => availableConfigs);

        // Act - Simulate typical workflow
        final loadedConfig = await useCase.loadGameConfig(basePath);
        final availableConfigsList = await useCase.loadLocalGameConfigs();
        final configById = await useCase.getGameConfigById(configId);

        // Assert
        expect(loadedConfig.id, equals(configId));
        expect(availableConfigsList.length, equals(1));
        expect(configById!.id, equals(configId));
        expect(loadedConfig, equals(configById));
      });

      test('should handle complete error recovery workflow', () async {
        // Arrange
        const basePath = '/invalid/path';
        const configId = 'nonexistent';

        when(() => mockRepository.loadGameConfig(any()))
            .thenThrow(Exception('Config not found'));
        when(() => mockRepository.loadAvailableGameConfigs())
            .thenAnswer((_) async => <GameConfig>[]);

        // Act & Assert - All operations should fail gracefully
        expect(
          () => useCase.loadGameConfig(basePath),
          throwsA(isA<Exception>()),
        );

        final emptyConfigs = await useCase.loadLocalGameConfigs();
        expect(emptyConfigs, isEmpty);

        expect(
          () => useCase.getGameConfigById(configId),
          throwsA(isA<Exception>()),
        );
      });

      test('should maintain consistency across multiple operations', () async {
        // Arrange
        final configs = <GameConfig>[
          GameConfig(id: 'game1', name: 'Game 1'),
          GameConfig(id: 'game2', name: 'Game 2'),
          GameConfig(id: 'game3', name: 'Game 3'),
        ];

        when(() => mockRepository.loadAvailableGameConfigs())
            .thenAnswer((_) async => configs);

        // Act
        final allConfigs = await useCase.loadLocalGameConfigs();
        final game1 = await useCase.getGameConfigById('game1');
        final game2 = await useCase.getGameConfigById('game2');

        // Assert - Verify consistency
        expect(allConfigs.length, equals(3));
        expect(game1!.id, equals('game1'));
        expect(game2!.id, equals('game2'));
        expect(allConfigs.contains(game1), isTrue);
        expect(allConfigs.contains(game2), isTrue);
      });
    });
  });
}
