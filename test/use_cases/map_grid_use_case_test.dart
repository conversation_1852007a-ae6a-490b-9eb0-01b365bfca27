import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/models/base/game_card_class.dart';
import 'package:dauntless/models/base/mapping/positioned_card_2d.dart';
import 'package:dauntless/models/base/mapping/positioned_grid_line_2d.dart';
import 'package:dauntless/models/base/mapping/positioned_label_2d.dart';
import 'package:dauntless/models/dto/grid_labels_dto.dart';
import 'package:dauntless/use_cases/map_grid_use_case.dart';
import 'package:flutter/material.dart';
import 'package:test/test.dart';
import 'package:vector_math/vector_math_64.dart';

void main() {
  group('MapGridUseCase', () {
    late MapGridUseCase useCase;

    setUp(() {
      useCase = MapGridUseCase();
    });

    group('Constructor', () {
      test('should initialize successfully', () {
        expect(useCase, isA<MapGridUseCase>());
      });
    });

    group('getPositionedCards', () {
      test('should return positioned cards with scale and offset', () {
        // Arrange
        final cards = [
          _createTestGameCardClass('card1', 0, 0),
          _createTestGameCardClass('card2', 10, 10),
        ];

        // Act
        final (positionedCards, scaleFactor, offset) = useCase.getPositionedCards(
          cards: cards,
          gridPxWidth: 800,
          gridPxHeight: 600,
          padding: const EdgeInsets.all(10),
        );

        // Assert
        expect(positionedCards, isA<List<PositionedCard2d>>());
        expect(positionedCards.length, equals(2));
        expect(scaleFactor, isA<double>());
        expect(scaleFactor, greaterThan(0));
        expect(offset, isA<Vector2>());
      });

      test('should handle empty card list', () {
        // Arrange
        final cards = <GameCardClass>[];

        // Act
        final (positionedCards, scaleFactor, offset) = useCase.getPositionedCards(
          cards: cards,
          gridPxWidth: 800,
          gridPxHeight: 600,
          padding: const EdgeInsets.all(10),
        );

        // Assert
        expect(positionedCards, isEmpty);
        expect(scaleFactor, isA<double>());
        expect(offset, isA<Vector2>());
      });

      test('should handle cards without grid coordinates', () {
        // Arrange
        final cards = [
          _createTestGameCardClass('card1', null, null),
          _createTestGameCardClass('card2', null, null),
        ];

        // Act
        final (positionedCards, scaleFactor, offset) = useCase.getPositionedCards(
          cards: cards,
          gridPxWidth: 800,
          gridPxHeight: 600,
          padding: const EdgeInsets.all(10),
        );

        // Assert
        expect(positionedCards, isEmpty); // Cards without coordinates are filtered out
        expect(scaleFactor, isA<double>());
        expect(offset, isA<Vector2>());
      });

      test('should calculate correct extents for multiple cards', () {
        // Arrange
        final cards = [
          _createTestGameCardClass('card1', -5, -3),
          _createTestGameCardClass('card2', 15, 12),
          _createTestGameCardClass('card3', 0, 0),
        ];

        // Act
        final (positionedCards, scaleFactor, offset) = useCase.getPositionedCards(
          cards: cards,
          gridPxWidth: 800,
          gridPxHeight: 600,
          padding: const EdgeInsets.all(20),
        );

        // Assert
        expect(positionedCards.length, equals(3));
        expect(scaleFactor, isA<double>());
        expect(scaleFactor, greaterThan(0));
        
        // Verify all positioned cards have screen coordinates
        for (final card in positionedCards) {
          expect(card.screenCoordinates, isNotNull);
          expect(card.mapCoordinates, isNotNull);
        }
      });

      test('should respect padding in calculations', () {
        // Arrange
        final cards = [
          _createTestGameCardClass('card1', 0, 0),
          _createTestGameCardClass('card2', 10, 10),
        ];
        const largePadding = EdgeInsets.all(100);
        const smallPadding = EdgeInsets.all(10);

        // Act
        final (_, scaleFactorLarge, _) = useCase.getPositionedCards(
          cards: cards,
          gridPxWidth: 800,
          gridPxHeight: 600,
          padding: largePadding,
        );

        final (_, scaleFactorSmall, _) = useCase.getPositionedCards(
          cards: cards,
          gridPxWidth: 800,
          gridPxHeight: 600,
          padding: smallPadding,
        );

        // Assert - Larger padding should result in smaller scale factor
        expect(scaleFactorLarge, lessThan(scaleFactorSmall));
      });

      test('should handle single card correctly', () {
        // Arrange
        final cards = [
          _createTestGameCardClass('card1', 5, 7),
        ];

        // Act
        final (positionedCards, scaleFactor, offset) = useCase.getPositionedCards(
          cards: cards,
          gridPxWidth: 800,
          gridPxHeight: 600,
          padding: const EdgeInsets.all(10),
        );

        // Assert
        expect(positionedCards.length, equals(1));
        expect(positionedCards.first.mapCoordinates, equals(Vector2(5, 7)));
        expect(positionedCards.first.screenCoordinates, isNotNull);
      });
    });

    group('getPositionedLabels', () {
      test('should create positioned labels from row and column labels', () {
        // Arrange
        final rowLabels = [
          (100.0, 'Row A'),
          (200.0, 'Row B'),
        ];
        final columnLabels = [
          (150.0, 'Col 1'),
          (250.0, 'Col 2'),
        ];

        // Act
        final labels = useCase.getPositionedLabels(
          rowLabels: rowLabels,
          columnLabels: columnLabels,
          scaleFactor: 2.0,
          offset: Vector2(10, 20),
        );

        // Assert
        expect(labels.length, equals(4)); // 2 row + 2 column labels
        
        // Verify we have the expected number of labels
        expect(labels.length, equals(4)); // 2 row + 2 column labels

        // Check that all labels have valid coordinates and labels
        for (final label in labels) {
          expect(label.label, isNotEmpty);
          expect(label.coordinates, isA<Vector2>());
        }

        // Check that row and column labels are present
        final labelTexts = labels.map((l) => l.label).toList();
        expect(labelTexts, contains('Row A'));
        expect(labelTexts, contains('Row B'));
        expect(labelTexts, contains('Col 1'));
        expect(labelTexts, contains('Col 2'));
      });

      test('should handle empty label lists', () {
        // Act
        final labels = useCase.getPositionedLabels(
          rowLabels: [],
          columnLabels: [],
        );

        // Assert
        expect(labels, isEmpty);
      });

      test('should handle default parameters', () {
        // Arrange
        final rowLabels = [(50.0, 'Test Row')];

        // Act
        final labels = useCase.getPositionedLabels(
          rowLabels: rowLabels,
        );

        // Assert
        expect(labels.length, equals(1));
        expect(labels.first.label, equals('Test Row'));
        expect(labels.first.coordinates.x, equals(100)); // Default fixed x
        expect(labels.first.coordinates.y, closeTo(50, 0.1)); // 50 * 1.0 (default scale) + 0 (default offset)
      });

      test('should apply scale factor correctly', () {
        // Arrange
        final rowLabels = [(100.0, 'Test')];
        const scaleFactor = 3.0;

        // Act
        final labels = useCase.getPositionedLabels(
          rowLabels: rowLabels,
          scaleFactor: scaleFactor,
        );

        // Assert
        expect(labels.first.coordinates.y, closeTo(100 * scaleFactor, 0.1));
      });

      test('should apply offset correctly', () {
        // Arrange
        final columnLabels = [(100.0, 'Test')];
        final offset = Vector2(25, 35);

        // Act
        final labels = useCase.getPositionedLabels(
          columnLabels: columnLabels,
          offset: offset,
        );

        // Assert
        expect(labels.first.coordinates.x, closeTo(100 + 25, 0.1));
        expect(labels.first.coordinates.y, closeTo(100 + 35, 0.1));
      });
    });

    group('getGeneratedGridLines', () {
      test('should generate vertical and horizontal grid lines', () {
        // Act
        final gridLines = useCase.getGeneratedGridLines(
          gridPxWidth: 800,
          gridPxHeight: 600,
          gridSize: 50,
        );

        // Assert
        expect(gridLines, isNotEmpty);
        
        // Should have both vertical and horizontal lines
        final verticalLines = gridLines.where((line) => line.axis == Axis.vertical);
        final horizontalLines = gridLines.where((line) => line.axis == Axis.horizontal);
        
        expect(verticalLines, isNotEmpty);
        expect(horizontalLines, isNotEmpty);
      });

      test('should handle zero grid size', () {
        // Act
        final gridLines = useCase.getGeneratedGridLines(
          gridPxWidth: 800,
          gridPxHeight: 600,
          gridSize: 0,
        );

        // Assert
        expect(gridLines, isEmpty);
      });

      test('should handle infinite grid size', () {
        // Act
        final gridLines = useCase.getGeneratedGridLines(
          gridPxWidth: 800,
          gridPxHeight: 600,
          gridSize: double.infinity,
        );

        // Assert
        expect(gridLines, isEmpty);
      });

      test('should handle NaN grid size', () {
        // Act
        final gridLines = useCase.getGeneratedGridLines(
          gridPxWidth: 800,
          gridPxHeight: 600,
          gridSize: double.nan,
        );

        // Assert
        expect(gridLines, isEmpty);
      });

      test('should create major lines at thick line intervals', () {
        // Act
        final gridLines = useCase.getGeneratedGridLines(
          gridPxWidth: 500,
          gridPxHeight: 500,
          gridSize: 50,
          thickLineInterval: 3,
        );

        // Assert
        final majorLines = gridLines.where((line) => line.type == GridLineType.major);
        final minorLines = gridLines.where((line) => line.type == GridLineType.minor);
        
        expect(majorLines, isNotEmpty);
        expect(minorLines, isNotEmpty);
        
        // Every 3rd line should be major
        final verticalMajorLines = gridLines
            .where((line) => line.axis == Axis.vertical && line.type == GridLineType.major);
        expect(verticalMajorLines, isNotEmpty);
      });

      test('should respect offset parameter', () {
        // Arrange
        final offset = Vector2(25, 35);

        // Act
        final gridLines = useCase.getGeneratedGridLines(
          gridPxWidth: 400,
          gridPxHeight: 300,
          gridSize: 50,
          offset: offset,
        );

        // Assert
        expect(gridLines, isNotEmpty);
        
        // First vertical line should account for offset
        final firstVerticalLine = gridLines
            .where((line) => line.axis == Axis.vertical)
            .first;
        expect(firstVerticalLine.position, equals(-offset.x));
        
        // First horizontal line should account for offset
        final firstHorizontalLine = gridLines
            .where((line) => line.axis == Axis.horizontal)
            .first;
        expect(firstHorizontalLine.position, equals(-offset.y));
      });
    });

    group('Integration Tests', () {
      test('should work with complete map grid workflow', () {
        // Arrange
        final cards = [
          _createTestGameCardClass('location1', 0, 0),
          _createTestGameCardClass('location2', 100, 50),
          _createTestGameCardClass('location3', -50, 75),
        ];
        final rowLabels = [(0.0, 'A'), (50.0, 'B')];
        final columnLabels = [(0.0, '1'), (100.0, '2')];

        // Act - Complete workflow
        final (positionedCards, scaleFactor, offset) = useCase.getPositionedCards(
          cards: cards,
          gridPxWidth: 800,
          gridPxHeight: 600,
          padding: const EdgeInsets.all(20),
        );

        final labels = useCase.getPositionedLabels(
          rowLabels: rowLabels,
          columnLabels: columnLabels,
          scaleFactor: scaleFactor,
          offset: offset,
        );

        final gridLines = useCase.getGeneratedGridLines(
          gridPxWidth: 800,
          gridPxHeight: 600,
          gridSize: 25 * scaleFactor,
          offset: offset,
        );

        // Assert - All components should be generated successfully
        expect(positionedCards.length, equals(3));
        expect(labels.length, equals(4));
        expect(gridLines, isNotEmpty);
        
        // Verify consistency
        expect(scaleFactor, greaterThan(0));
        expect(offset, isA<Vector2>());
        
        // All positioned cards should have valid coordinates
        for (final card in positionedCards) {
          expect(card.screenCoordinates, isNotNull);
          expect(card.mapCoordinates, isNotNull);
        }
      });
    });
  });
}

// Helper function to create test GameCardClass instances
GameCardClass _createTestGameCardClass(String id, double? x, double? y) {
  final attributes = <String, dynamic>{};
  if (x != null && y != null) {
    attributes['location'] = {
      'type': 'cartesian',
      'x': x,
      'y': y,
    };
  }
  
  return GameCardClass(
    id: id,
    name: 'Test Card $id',
    type: CardType.location,
    attributes: attributes,
  );
}
