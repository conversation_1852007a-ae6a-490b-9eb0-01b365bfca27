import 'dart:async';

import 'package:dauntless/models/base/card_grouping.dart';
import 'package:dauntless/models/base/game_card.dart';
import 'package:dauntless/repositories/actions_repository.dart';
import 'package:dauntless/repositories/game_card_repository.dart';
import 'package:dauntless/repositories/locations_repository.dart';
import 'package:dauntless/use_cases/groupings_use_case.dart';
import 'package:dauntless/use_cases/list_object_use_case.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

class MockBaseListRepository extends Mock implements BaseListRepository {}
class MockLocationsRepository extends Mock implements LocationsRepository {}
class MockActionsRepository extends Mock implements ActionsRepository {}

void main() {
  group('GroupingsUseCase', () {
    late GroupingsUseCase groupingsUseCase;
    late MockBaseListRepository mockRepository;

    setUp(() {
      mockRepository = MockBaseListRepository();
      groupingsUseCase = GroupingsUseCase(mockRepository);
    });

    group('Constructor and Initialization', () {
      test('should initialize with BaseListRepository dependency', () {
        expect(groupingsUseCase, isA<GroupingsUseCase>());
        expect(groupingsUseCase, isA<ListObjectUseCase<CardGrouping>>());
      });

      test('should start with initial status', () {
        expect(groupingsUseCase.status, equals(ProcessingStatus.start));
      });

      test('should start with empty objects list', () {
        expect(groupingsUseCase.objects, isEmpty);
      });

      test('should start with empty allGroupingIds', () {
        expect(groupingsUseCase.allGroupingIds, isEmpty);
      });

      test('should be instantiable multiple times', () {
        final useCase1 = GroupingsUseCase(mockRepository);
        final useCase2 = GroupingsUseCase(mockRepository);
        
        expect(useCase1, isNot(same(useCase2)));
        expect(useCase1, isA<GroupingsUseCase>());
        expect(useCase2, isA<GroupingsUseCase>());
      });
    });

    group('Initialization Process', () {
      test('should load groupings successfully', () async {
        // Arrange
        final testGroupings = [
          createTestCardGrouping(id: 'group1', groupingId: 'grouping1'),
          createTestCardGrouping(id: 'group2', groupingId: 'grouping2'),
        ];
        
        when(() => mockRepository.getGroupings())
            .thenAnswer((_) async => testGroupings);

        // Act
        await groupingsUseCase.init();

        // Assert
        expect(groupingsUseCase.status, equals(ProcessingStatus.loaded));
        expect(groupingsUseCase.objects, hasLength(2));
        expect(groupingsUseCase.objects.first.id, equals('group1'));
        expect(groupingsUseCase.objects.last.id, equals('group2'));
        verify(() => mockRepository.getGroupings()).called(1);
      });

      test('should handle empty groupings list', () async {
        // Arrange
        when(() => mockRepository.getGroupings())
            .thenAnswer((_) async => <CardGrouping>[]);

        // Act
        await groupingsUseCase.init();

        // Assert
        expect(groupingsUseCase.status, equals(ProcessingStatus.loaded));
        expect(groupingsUseCase.objects, isEmpty);
        expect(groupingsUseCase.allGroupingIds, isEmpty);
        verify(() => mockRepository.getGroupings()).called(1);
      });

      test('should handle repository errors gracefully', () async {
        // Arrange
        when(() => mockRepository.getGroupings())
            .thenThrow(Exception('Repository error'));

        // Act & Assert
        expect(() => groupingsUseCase.init(), throwsException);
        verify(() => mockRepository.getGroupings()).called(1);
      });

      test('should prevent multiple concurrent initializations', () async {
        // Arrange
        final completer = Completer<List<CardGrouping>>();
        when(() => mockRepository.getGroupings())
            .thenAnswer((_) => completer.future);

        // Act - Start multiple init calls
        final future1 = groupingsUseCase.init();
        final future2 = groupingsUseCase.init();
        final future3 = groupingsUseCase.init();

        // Complete the repository call
        completer.complete([createTestCardGrouping(id: 'test', groupingId: 'test-group')]);

        // Wait for all futures
        await Future.wait([future1, future2, future3]);

        // Assert - Repository should only be called once
        expect(groupingsUseCase.status, equals(ProcessingStatus.loaded));
        verify(() => mockRepository.getGroupings()).called(1);
      });
    });

    group('Grouping Filtering Methods', () {
      late List<CardGrouping> testGroupings;

      setUp(() async {
        testGroupings = [
          createTestCardGrouping(id: 'card1', groupingId: 'group1'),
          createTestCardGrouping(id: 'card2', groupingId: 'group1'),
          createTestCardGrouping(id: 'card3', groupingId: 'group2'),
          createTestCardGrouping(id: 'card4', groupingId: 'group3'),
          createTestCardGrouping(id: 'card5', groupingId: 'group2'),
        ];
        
        when(() => mockRepository.getGroupings())
            .thenAnswer((_) async => testGroupings);
        
        await groupingsUseCase.init();
      });

      test('getGroupingsByIds should filter by card IDs', () {
        // Act
        final result = groupingsUseCase.getGroupingsByIds(['card1', 'card3', 'card5']);

        // Assert
        expect(result, hasLength(3));
        expect(result.map((g) => g.id), containsAll(['card1', 'card3', 'card5']));
      });

      test('getGroupingsByIds should handle empty ID list', () {
        // Act
        final result = groupingsUseCase.getGroupingsByIds([]);

        // Assert
        expect(result, isEmpty);
      });

      test('getGroupingsByIds should handle non-existent IDs', () {
        // Act
        final result = groupingsUseCase.getGroupingsByIds(['nonexistent1', 'nonexistent2']);

        // Assert
        expect(result, isEmpty);
      });

      test('getGroupingsByGroupingIds should filter by grouping IDs', () {
        // Act
        final result = groupingsUseCase.getGroupingsByGroupingIds(['group1', 'group3']);

        // Assert
        expect(result, hasLength(3)); // card1, card2 (group1) + card4 (group3)
        expect(result.map((g) => g.groupingId), containsAll(['group1', 'group3']));
      });

      test('getGroupingsByGroupingIds should handle empty grouping ID list', () {
        // Act
        final result = groupingsUseCase.getGroupingsByGroupingIds([]);

        // Assert
        expect(result, isEmpty);
      });

      test('allGroupingIds should return all unique grouping IDs', () {
        // Act
        final result = groupingsUseCase.allGroupingIds;

        // Assert
        expect(result, hasLength(3));
        expect(result, containsAll(['group1', 'group2', 'group3']));
      });

      test('getGroupingIdsByIds should return grouping IDs for given card IDs', () {
        // Act
        final result = groupingsUseCase.getGroupingIdsByIds(['card1', 'card3', 'card4']);

        // Assert
        expect(result, hasLength(3));
        expect(result, containsAll(['group1', 'group2', 'group3']));
      });

      test('getIdsByGroupingIds should return card IDs for given grouping IDs', () {
        // Act
        final result = groupingsUseCase.getIdsByGroupingIds(['group1', 'group2']);

        // Assert
        expect(result, hasLength(4));
        expect(result, containsAll(['card1', 'card2', 'card3', 'card5']));
      });
    });

    group('Specialized Use Cases', () {
      test('LocationGroupingsUseCase should initialize with LocationsRepository', () {
        // Arrange
        final mockLocationsRepository = MockLocationsRepository();
        
        // Act
        final locationGroupingsUseCase = LocationGroupingsUseCase(mockLocationsRepository);

        // Assert
        expect(locationGroupingsUseCase, isA<LocationGroupingsUseCase>());
        expect(locationGroupingsUseCase, isA<GroupingsUseCase>());
        expect(locationGroupingsUseCase, isA<ListObjectUseCase<CardGrouping>>());
      });

      test('ActionsGroupingsUseCase should initialize with ActionsRepository', () {
        // Arrange
        final mockActionsRepository = MockActionsRepository();
        
        // Act
        final actionsGroupingsUseCase = ActionsGroupingsUseCase(mockActionsRepository);

        // Assert
        expect(actionsGroupingsUseCase, isA<ActionsGroupingsUseCase>());
        expect(actionsGroupingsUseCase, isA<GroupingsUseCase>());
        expect(actionsGroupingsUseCase, isA<ListObjectUseCase<CardGrouping>>());
      });

      test('LocationGroupingsUseCase should work with real repository', () async {
        // Arrange
        final mockLocationsRepository = MockLocationsRepository();
        final testGroupings = [
          createTestCardGrouping(id: 'location1', groupingId: 'planet1'),
          createTestCardGrouping(id: 'location2', groupingId: 'planet1'),
        ];
        
        when(() => mockLocationsRepository.getGroupings())
            .thenAnswer((_) async => testGroupings);

        final locationGroupingsUseCase = LocationGroupingsUseCase(mockLocationsRepository);

        // Act
        await locationGroupingsUseCase.init();

        // Assert
        expect(locationGroupingsUseCase.status, equals(ProcessingStatus.loaded));
        expect(locationGroupingsUseCase.objects, hasLength(2));
        verify(() => mockLocationsRepository.getGroupings()).called(1);
      });

      test('ActionsGroupingsUseCase should work with real repository', () async {
        // Arrange
        final mockActionsRepository = MockActionsRepository();
        final testGroupings = [
          createTestCardGrouping(id: 'action1', groupingId: 'combat'),
          createTestCardGrouping(id: 'action2', groupingId: 'movement'),
        ];
        
        when(() => mockActionsRepository.getGroupings())
            .thenAnswer((_) async => testGroupings);

        final actionsGroupingsUseCase = ActionsGroupingsUseCase(mockActionsRepository);

        // Act
        await actionsGroupingsUseCase.init();

        // Assert
        expect(actionsGroupingsUseCase.status, equals(ProcessingStatus.loaded));
        expect(actionsGroupingsUseCase.objects, hasLength(2));
        verify(() => mockActionsRepository.getGroupings()).called(1);
      });
    });

    group('selectObject Implementation', () {
      test('should handle selectObject call without errors', () {
        // Arrange
        final testGrouping = createTestCardGrouping(id: 'test', groupingId: 'test-group');

        // Act & Assert - Should not throw
        expect(() => groupingsUseCase.selectObject(testGrouping), returnsNormally);
      });
    });

    group('Edge Cases and Error Handling', () {
      test('should handle large number of groupings', () async {
        // Arrange
        final largeGroupingsList = List.generate(1000, (index) =>
          createTestCardGrouping(
            id: 'card$index',
            groupingId: 'group${index % 10}', // 10 different groups
          )
        );

        when(() => mockRepository.getGroupings())
            .thenAnswer((_) async => largeGroupingsList);

        // Act
        await groupingsUseCase.init();

        // Assert
        expect(groupingsUseCase.status, equals(ProcessingStatus.loaded));
        expect(groupingsUseCase.objects, hasLength(1000));
        expect(groupingsUseCase.allGroupingIds, hasLength(10));
      });

      test('should handle duplicate grouping IDs correctly', () async {
        // Arrange
        final duplicateGroupings = [
          createTestCardGrouping(id: 'card1', groupingId: 'duplicate'),
          createTestCardGrouping(id: 'card2', groupingId: 'duplicate'),
          createTestCardGrouping(id: 'card3', groupingId: 'duplicate'),
          createTestCardGrouping(id: 'card4', groupingId: 'unique'),
        ];

        when(() => mockRepository.getGroupings())
            .thenAnswer((_) async => duplicateGroupings);

        // Act
        await groupingsUseCase.init();

        // Assert
        expect(groupingsUseCase.objects, hasLength(4));
        expect(groupingsUseCase.allGroupingIds, hasLength(2));
        expect(groupingsUseCase.allGroupingIds, containsAll(['duplicate', 'unique']));

        final duplicateGroupingCards = groupingsUseCase.getGroupingsByGroupingIds(['duplicate']);
        expect(duplicateGroupingCards, hasLength(3));
      });

      test('should handle null and empty string IDs in filtering methods', () async {
        // Arrange
        final testGroupings = [
          createTestCardGrouping(id: 'valid1', groupingId: 'group1'),
          createTestCardGrouping(id: 'valid2', groupingId: 'group2'),
        ];

        when(() => mockRepository.getGroupings())
            .thenAnswer((_) async => testGroupings);

        await groupingsUseCase.init();

        // Act & Assert - Empty string handling
        expect(groupingsUseCase.getGroupingsByIds(['']), isEmpty);
        expect(groupingsUseCase.getGroupingsByGroupingIds(['']), isEmpty);
        expect(groupingsUseCase.getGroupingIdsByIds(['']), isEmpty);
        expect(groupingsUseCase.getIdsByGroupingIds(['']), isEmpty);
      });

      test('should handle mixed valid and invalid IDs', () async {
        // Arrange
        final testGroupings = [
          createTestCardGrouping(id: 'card1', groupingId: 'group1'),
          createTestCardGrouping(id: 'card2', groupingId: 'group2'),
        ];

        when(() => mockRepository.getGroupings())
            .thenAnswer((_) async => testGroupings);

        await groupingsUseCase.init();

        // Act
        final result = groupingsUseCase.getGroupingsByIds(['card1', 'nonexistent', 'card2']);

        // Assert
        expect(result, hasLength(2));
        expect(result.map((g) => g.id), containsAll(['card1', 'card2']));
      });

      test('should handle repository timeout', () async {
        // Arrange
        when(() => mockRepository.getGroupings())
            .thenThrow(TimeoutException('Repository timeout', Duration(seconds: 5)));

        // Act & Assert
        expect(() => groupingsUseCase.init(), throwsA(isA<TimeoutException>()));
        verify(() => mockRepository.getGroupings()).called(1);
      });

      test('should maintain status during failed initialization', () async {
        // Arrange
        when(() => mockRepository.getGroupings())
            .thenThrow(Exception('Initialization failed'));

        // Act
        try {
          await groupingsUseCase.init();
        } catch (e) {
          // Expected to throw
        }

        // Assert - Status should remain loading (since completer never completes)
        expect(groupingsUseCase.status, equals(ProcessingStatus.loading));
        expect(groupingsUseCase.objects, isEmpty);
      });
    });

    group('Integration Scenarios', () {
      test('should handle complete workflow with multiple operations', () async {
        // Arrange
        final testGroupings = [
          createTestCardGrouping(id: 'weapon1', groupingId: 'weapons'),
          createTestCardGrouping(id: 'weapon2', groupingId: 'weapons'),
          createTestCardGrouping(id: 'armor1', groupingId: 'armor'),
          createTestCardGrouping(id: 'armor2', groupingId: 'armor'),
          createTestCardGrouping(id: 'misc1', groupingId: 'miscellaneous'),
        ];

        when(() => mockRepository.getGroupings())
            .thenAnswer((_) async => testGroupings);

        // Act - Initialize
        await groupingsUseCase.init();

        // Act - Perform various operations
        final weaponCards = groupingsUseCase.getGroupingsByGroupingIds(['weapons']);
        final armorAndMiscIds = groupingsUseCase.getIdsByGroupingIds(['armor', 'miscellaneous']);
        final weaponGroupingIds = groupingsUseCase.getGroupingIdsByIds(['weapon1', 'weapon2']);
        final allGroupings = groupingsUseCase.allGroupingIds;

        // Assert
        expect(groupingsUseCase.status, equals(ProcessingStatus.loaded));
        expect(weaponCards, hasLength(2));
        expect(armorAndMiscIds, hasLength(3));
        expect(weaponGroupingIds, equals({'weapons'}));
        expect(allGroupings, hasLength(3));
        expect(allGroupings, containsAll(['weapons', 'armor', 'miscellaneous']));
      });

      test('should handle specialized use cases with different repositories', () async {
        // Arrange
        final mockLocationsRepository = MockLocationsRepository();
        final mockActionsRepository = MockActionsRepository();

        final locationGroupings = [
          createTestCardGrouping(id: 'loc1', groupingId: 'planet1'),
          createTestCardGrouping(id: 'loc2', groupingId: 'planet2'),
        ];

        final actionGroupings = [
          createTestCardGrouping(id: 'act1', groupingId: 'combat'),
          createTestCardGrouping(id: 'act2', groupingId: 'movement'),
        ];

        when(() => mockLocationsRepository.getGroupings())
            .thenAnswer((_) async => locationGroupings);
        when(() => mockActionsRepository.getGroupings())
            .thenAnswer((_) async => actionGroupings);

        final locationUseCase = LocationGroupingsUseCase(mockLocationsRepository);
        final actionsUseCase = ActionsGroupingsUseCase(mockActionsRepository);

        // Act
        await Future.wait([
          locationUseCase.init(),
          actionsUseCase.init(),
        ]);

        // Assert
        expect(locationUseCase.status, equals(ProcessingStatus.loaded));
        expect(actionsUseCase.status, equals(ProcessingStatus.loaded));
        expect(locationUseCase.objects, hasLength(2));
        expect(actionsUseCase.objects, hasLength(2));
        expect(locationUseCase.allGroupingIds, containsAll(['planet1', 'planet2']));
        expect(actionsUseCase.allGroupingIds, containsAll(['combat', 'movement']));
      });

      test('should handle concurrent access to filtering methods', () async {
        // Arrange
        final testGroupings = List.generate(100, (index) =>
          createTestCardGrouping(
            id: 'card$index',
            groupingId: 'group${index % 5}',
          )
        );

        when(() => mockRepository.getGroupings())
            .thenAnswer((_) async => testGroupings);

        await groupingsUseCase.init();

        // Act - Perform concurrent operations
        final futures = [
          Future(() => groupingsUseCase.getGroupingsByIds(['card1', 'card2', 'card3'])),
          Future(() => groupingsUseCase.getGroupingsByGroupingIds(['group1', 'group2'])),
          Future(() => groupingsUseCase.getGroupingIdsByIds(['card10', 'card20'])),
          Future(() => groupingsUseCase.getIdsByGroupingIds(['group3', 'group4'])),
          Future(() => groupingsUseCase.allGroupingIds),
        ];

        final results = await Future.wait(futures);

        // Assert - All operations should complete successfully
        expect(results, hasLength(5));
        expect(results[0], isA<List<CardGrouping>>());
        expect(results[1], isA<List<CardGrouping>>());
        expect(results[2], isA<Set<GameCardId>>());
        expect(results[3], isA<Set<GameCardId>>());
        expect(results[4], isA<Set<GameCardId>>());
      });

      test('should handle repository error recovery', () async {
        // Arrange - First call fails, second succeeds
        var callCount = 0;
        when(() => mockRepository.getGroupings()).thenAnswer((_) async {
          callCount++;
          if (callCount == 1) {
            throw Exception('First call failed');
          }
          return [createTestCardGrouping(id: 'recovery', groupingId: 'recovered')];
        });

        // Act - First attempt fails
        expect(() => groupingsUseCase.init(), throwsException);

        // Create new instance for retry (since status is stuck in loading)
        final retryUseCase = GroupingsUseCase(mockRepository);
        await retryUseCase.init();

        // Assert - Second attempt succeeds
        expect(retryUseCase.status, equals(ProcessingStatus.loaded));
        expect(retryUseCase.objects, hasLength(1));
        expect(retryUseCase.objects.first.id, equals('recovery'));
      });
    });
  });
}

/// Test helper to create CardGrouping instances
CardGrouping createTestCardGrouping({
  String? id,
  String? groupingId,
  String? name,
  Map<String, dynamic>? attributes,
}) {
  return CardGrouping(
    id: id ?? 'test-id',
    groupingId: groupingId ?? 'test-grouping-id',
    name_: name,
    attributes: attributes ?? {},
  );
}
