import 'package:test/test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:get_it/get_it.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/match_source_management_use_case.dart';

// Mock classes
class MockMatchRepository extends Mock implements MatchRepositoryInterface {}
class MockRemoteLogger extends Mock implements RemoteLogger {}

// Register fallback values for mocktail
void _registerFallbackValues() {
  registerFallbackValue(<String, MatchRepositoryInterface>{});
}

void main() {
  _registerFallbackValues();

  group('MatchSourceManagementUseCase', () {
    late MatchSourceManagementUseCase useCase;
    late MockMatchRepository mockLocalRepository;
    late MockMatchRepository mockNetworkRepository;
    late MockRemoteLogger mockLogger;
    late Map<String, MatchRepositoryInterface> repositories;

    setUp(() {
      mockLocalRepository = MockMatchRepository();
      mockNetworkRepository = MockMatchRepository();
      mockLogger = MockRemoteLogger();

      repositories = <String, MatchRepositoryInterface>{};

      // Reset GetIt
      GetIt.instance.reset();

      useCase = MatchSourceManagementUseCase(
        repositories,
        mockLogger,
      );
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    group('initializeRepositories', () {
      test('should initialize with empty repositories when GetIt has no repositories', () async {
        // Arrange - GetIt has no registered repositories

        // Act
        final result = await useCase.initializeRepositories();

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value!.availableSources, isEmpty);
        expect(result.value!.hasNetworkCapability, isFalse);
      });

      test('should initialize with local repository from GetIt', () async {
        // Arrange
        when(() => mockLocalRepository.supportsRealTimeUpdates).thenReturn(false);
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        // Act
        final result = await useCase.initializeRepositories();

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value!.availableSources, contains('local'));
        expect(result.value!.hasNetworkCapability, isFalse);
      });

      test('should initialize with network repository when in server scope', () async {
        // Arrange
        when(() => mockNetworkRepository.supportsRealTimeUpdates).thenReturn(true);

        // Set up server-connected scope
        GetIt.instance.pushNewScope(scopeName: 'serverConnected');
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockNetworkRepository,
          instanceName: 'network',
        );

        // Act
        final result = await useCase.initializeRepositories();

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value!.availableSources, contains('network'));
        expect(result.value!.hasNetworkCapability, isTrue);
      });

      test('should initialize with both repositories', () async {
        // Arrange
        when(() => mockLocalRepository.supportsRealTimeUpdates).thenReturn(false);
        when(() => mockNetworkRepository.supportsRealTimeUpdates).thenReturn(true);

        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        GetIt.instance.pushNewScope(scopeName: 'serverConnected');
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockNetworkRepository,
          instanceName: 'network',
        );

        // Act
        final result = await useCase.initializeRepositories();

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value!.availableSources, contains('local'));
        expect(result.value!.availableSources, contains('network'));
        expect(result.value!.hasNetworkCapability, isTrue);
      });
    });

    group('cleanupRepositories', () {
      test('should cleanup repositories successfully', () {
        // Arrange
        repositories['local'] = mockLocalRepository;
        repositories['network'] = mockNetworkRepository;
        expect(repositories.length, equals(2));

        // Act
        useCase.cleanupRepositories();

        // Assert
        expect(repositories.length, equals(0));
      });

      test('should handle cleanup when no repositories exist', () {
        // Arrange - repositories already empty

        // Act & Assert - should not throw
        expect(() => useCase.cleanupRepositories(), returnsNormally);
      });
    });

    group('getAvailableSources', () {
      test('should return empty list when no repositories', () {
        // Arrange - repositories already empty

        // Act
        final result = useCase.getAvailableSources();

        // Assert
        expect(result, isEmpty);
      });

      test('should return local source when only local repository exists', () {
        // Arrange
        repositories['local'] = mockLocalRepository;

        // Act
        final result = useCase.getAvailableSources();

        // Assert
        expect(result, contains('local'));
        expect(result, isNot(contains('network')));
        expect(result.length, equals(1));
      });

      test('should return both sources when both repositories exist', () {
        // Arrange
        repositories['local'] = mockLocalRepository;
        repositories['network'] = mockNetworkRepository;

        // Act
        final result = useCase.getAvailableSources();

        // Assert
        expect(result, contains('local'));
        expect(result, contains('network'));
        expect(result.length, equals(2));
      });
    });

    group('hasNetworkCapability', () {
      test('should return false when no repositories', () {
        // Arrange - repositories already empty

        // Act
        final result = useCase.hasNetworkCapability();

        // Assert
        expect(result, isFalse);
      });

      test('should return false when only local repository without real-time updates', () {
        // Arrange
        when(() => mockLocalRepository.supportsRealTimeUpdates).thenReturn(false);
        repositories['local'] = mockLocalRepository;

        // Act
        final result = useCase.hasNetworkCapability();

        // Assert
        expect(result, isFalse);
      });

      test('should return true when repository supports real-time updates', () {
        // Arrange
        when(() => mockNetworkRepository.supportsRealTimeUpdates).thenReturn(true);
        repositories['network'] = mockNetworkRepository;

        // Act
        final result = useCase.hasNetworkCapability();

        // Assert
        expect(result, isTrue);
      });

      test('should return true when any repository supports real-time updates', () {
        // Arrange
        when(() => mockLocalRepository.supportsRealTimeUpdates).thenReturn(false);
        when(() => mockNetworkRepository.supportsRealTimeUpdates).thenReturn(true);
        repositories['local'] = mockLocalRepository;
        repositories['network'] = mockNetworkRepository;

        // Act
        final result = useCase.hasNetworkCapability();

        // Assert
        expect(result, isTrue);
      });
    });

    group('handleServerScopeAvailable', () {
      test('should handle server scope becoming available', () async {
        // Arrange
        when(() => mockNetworkRepository.supportsRealTimeUpdates).thenReturn(true);

        // Set up server-connected scope with network repository
        GetIt.instance.pushNewScope(scopeName: 'serverConnected');
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockNetworkRepository,
          instanceName: 'network',
        );

        // Act
        final result = await useCase.handleServerScopeAvailable();

        // Assert
        expect(result.isSuccess, isTrue);
      });

      test('should fail when network repository not available', () async {
        // Arrange - no network repository registered

        // Act
        final result = await useCase.handleServerScopeAvailable();

        // Assert
        expect(result.isFailure, isTrue);
      });
    });

    group('handleServerScopeLost', () {
      test('should handle server scope being lost', () async {
        // Arrange
        repositories['network'] = mockNetworkRepository;
        expect(repositories.containsKey('network'), isTrue);

        // Act
        final result = await useCase.handleServerScopeLost();

        // Assert
        expect(result.isSuccess, isTrue);
        expect(repositories.containsKey('network'), isFalse);
      });

      test('should handle server scope lost when no network repository', () async {
        // Arrange - no network repository

        // Act
        final result = await useCase.handleServerScopeLost();

        // Assert
        expect(result.isSuccess, isTrue);
      });
    });

    group('getSourceCapabilities', () {
      test('should return empty map when no repositories', () {
        // Arrange - repositories already empty

        // Act
        final result = useCase.getSourceCapabilities();

        // Assert
        expect(result, isEmpty);
      });

      test('should return correct capabilities for local source', () {
        // Arrange
        when(() => mockLocalRepository.supportsRealTimeUpdates).thenReturn(false);
        repositories['local'] = mockLocalRepository;

        // Act
        final result = useCase.getSourceCapabilities();

        // Assert
        expect(result, containsPair('local', false));
      });

      test('should return correct capabilities for network source', () {
        // Arrange
        when(() => mockNetworkRepository.supportsRealTimeUpdates).thenReturn(true);
        repositories['network'] = mockNetworkRepository;

        // Act
        final result = useCase.getSourceCapabilities();

        // Assert
        expect(result, containsPair('network', true));
      });

      test('should return capabilities for multiple sources', () {
        // Arrange
        when(() => mockLocalRepository.supportsRealTimeUpdates).thenReturn(false);
        when(() => mockNetworkRepository.supportsRealTimeUpdates).thenReturn(true);

        repositories['local'] = mockLocalRepository;
        repositories['network'] = mockNetworkRepository;

        // Act
        final result = useCase.getSourceCapabilities();

        // Assert
        expect(result.length, equals(2));
        expect(result, containsPair('local', false));
        expect(result, containsPair('network', true));
      });
    });

    group('checkAndAddNetworkSources', () {
      test('should return false when no network capability', () async {
        // Arrange
        when(() => mockLocalRepository.supportsRealTimeUpdates).thenReturn(false);
        repositories['local'] = mockLocalRepository;

        // Act
        final result = await useCase.checkAndAddNetworkSources();

        // Assert
        expect(result, isFalse);
      });

      test('should return true when network capability exists', () async {
        // Arrange
        when(() => mockNetworkRepository.supportsRealTimeUpdates).thenReturn(true);
        repositories['network'] = mockNetworkRepository;

        // Act
        final result = await useCase.checkAndAddNetworkSources();

        // Assert
        expect(result, isTrue);
      });
    });

    group('refreshAvailableSources', () {
      test('should refresh and return available sources', () async {
        // Arrange
        when(() => mockLocalRepository.supportsRealTimeUpdates).thenReturn(false);
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockLocalRepository,
          instanceName: 'local',
        );

        // Act
        final result = await useCase.refreshAvailableSources();

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value, contains('local'));
      });

      test('should handle refresh when no sources available', () async {
        // Arrange - no repositories registered in GetIt

        // Act
        final result = await useCase.refreshAvailableSources();

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value, isEmpty);
      });
    });

    group('isSourceAvailable', () {
      test('should return true for available source', () {
        // Arrange
        repositories['local'] = mockLocalRepository;

        // Act
        final result = useCase.isSourceAvailable('local');

        // Assert
        expect(result, isTrue);
      });

      test('should return false for unavailable source', () {
        // Arrange - repositories already empty

        // Act
        final result = useCase.isSourceAvailable('network');

        // Assert
        expect(result, isFalse);
      });
    });


  });
}
