import 'package:test/test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:common/models/game_match.dart';
import 'package:common/models/match_status.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:common/models/user.dart';
import 'package:dauntless/models/base/game_config.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/frameworks/user/user_manager.dart';
import 'package:dauntless/frameworks/user/user_state.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/match_management_use_case.dart';

// Mock classes
class MockMatchRepository extends Mock implements MatchRepositoryInterface {}
class MockUserManager extends Mock implements UserManager {}
class MockRemoteLogger extends Mock implements RemoteLogger {}

// Register fallback values for mocktail
void _registerFallbackValues() {
  registerFallbackValue(const GameConfig(id: 'test', name: 'Test'));
  registerFallbackValue(const PlayerSlot(
    id: 'test',
    playerId: 'test',
    type: PlayerType.humanLocal,
    playerClassId: 'test',
    name: 'Test',
  ));
  registerFallbackValue(GameMatch(
    id: 'test',
    gameTypeId: 'test',
    creatorId: 'test',
    createdAt: 0,
    updatedAt: 0,
  ));
}

void main() {
  _registerFallbackValues();

  group('MatchManagementUseCase', () {
    late MatchManagementUseCase useCase;
    late MockMatchRepository mockLocalRepository;
    late MockMatchRepository mockNetworkRepository;
    late MockUserManager mockUserManager;
    late MockRemoteLogger mockLogger;
    late Map<String, MatchRepositoryInterface> repositories;

    setUp(() {
      mockLocalRepository = MockMatchRepository();
      mockNetworkRepository = MockMatchRepository();
      mockUserManager = MockUserManager();
      mockLogger = MockRemoteLogger();

      repositories = {
        'local': mockLocalRepository,
        'network': mockNetworkRepository,
      };

      useCase = MatchManagementUseCase(
        repositories,
        mockUserManager,
        mockLogger,
      );
    });

    group('createMatch', () {
      test('should create local match successfully with valid request', () async {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        final request = CreateMatchRequest(
          gameConfig: gameConfig,
          gameName: 'Test Match',
          playerSlots: [
            PlayerSlot(
              id: 'slot_0',
              playerId: 'player1',
              type: PlayerType.humanLocal,
              playerClassId: 'class1',
              name: 'Player 1',
            ),
          ],
        );

        final user = User(id: 'player1');
        final userState = UserState(user: user);
        when(() => mockUserManager.state).thenReturn(userState);

        final expectedMatch = GameMatch(
          id: 'match_123',
          gameTypeId: 'test_game',
          creatorId: 'player1',
          gameName: 'Test Match',
          createdAt: DateTime.now().millisecondsSinceEpoch,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
          status: MatchStatus.open,
          isOpenForJoining: true,
          playerSlots: request.playerSlots,
        );

        when(() => mockLocalRepository.createMatch(any(), any()))
            .thenAnswer((_) async => expectedMatch);

        // Act
        final result = await useCase.createMatch(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value, equals(expectedMatch));
        verify(() => mockLocalRepository.createMatch(any(), any())).called(1);
        verifyNever(() => mockNetworkRepository.createMatch(any(), any()));
      });

      test('should create network match for network players', () async {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        final request = CreateMatchRequest(
          gameConfig: gameConfig,
          gameName: 'Test Match',
          playerSlots: [
            PlayerSlot(
              id: 'slot_0',
              playerId: 'player1',
              type: PlayerType.humanNetwork,
              playerClassId: 'class1',
              name: 'Player 1',
            ),
          ],
        );

        final user = User(id: 'player1');
        final userState = UserState(user: user);
        when(() => mockUserManager.state).thenReturn(userState);

        final expectedMatch = GameMatch(
          id: 'match_123',
          gameTypeId: 'test_game',
          creatorId: 'player1',
          gameName: 'Test Match',
          createdAt: DateTime.now().millisecondsSinceEpoch,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
          status: MatchStatus.open,
          playerSlots: request.playerSlots,
        );

        when(() => mockNetworkRepository.createMatch(any(), any()))
            .thenAnswer((_) async => expectedMatch);

        // Act
        final result = await useCase.createMatch(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value, equals(expectedMatch));
        verify(() => mockNetworkRepository.createMatch(any(), any())).called(1);
        verifyNever(() => mockLocalRepository.createMatch(any(), any()));
      });

      test('should return failure when no user logged in', () async {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        final request = CreateMatchRequest(
          gameConfig: gameConfig,
          gameName: 'Test Match',
          playerSlots: [
            PlayerSlot(
              id: 'slot_0',
              playerId: 'player1',
              type: PlayerType.humanLocal,
              playerClassId: 'class1',
              name: 'Player 1',
            ),
          ],
        );

        final userState = UserState(user: null);
        when(() => mockUserManager.state).thenReturn(userState);

        // Act
        final result = await useCase.createMatch(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, equals('No user logged in'));
      });

      test('should return failure when repository not available', () async {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        final request = CreateMatchRequest(
          gameConfig: gameConfig,
          gameName: 'Test Match',
          playerSlots: [
            PlayerSlot(
              id: 'slot_0',
              playerId: 'player1',
              type: PlayerType.humanNetwork,
              playerClassId: 'class1',
              name: 'Player 1',
            ),
          ],
        );

        final user = User(id: 'player1');
        final userState = UserState(user: user);
        when(() => mockUserManager.state).thenReturn(userState);

        // Remove network repository to simulate unavailability
        final repositoriesWithoutNetwork = <String, MatchRepositoryInterface>{
          'local': mockLocalRepository,
        };

        final useCaseWithoutNetwork = MatchManagementUseCase(
          repositoriesWithoutNetwork,
          mockUserManager,
          mockLogger,
        );

        // Act
        final result = await useCaseWithoutNetwork.createMatch(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, equals('network repository not available'));
      });

      test('should handle repository errors gracefully', () async {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        final request = CreateMatchRequest(
          gameConfig: gameConfig,
          gameName: 'Test Match',
          playerSlots: [
            PlayerSlot(
              id: 'slot_0',
              playerId: 'player1',
              type: PlayerType.humanLocal,
              playerClassId: 'class1',
              name: 'Player 1',
            ),
          ],
        );

        final user = User(id: 'player1');
        final userState = UserState(user: user);
        when(() => mockUserManager.state).thenReturn(userState);

        when(() => mockLocalRepository.createMatch(any(), any()))
            .thenThrow(Exception('Repository error'));

        // Act
        final result = await useCase.createMatch(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Error creating match'));
      });

      test('should handle null repository response', () async {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        final request = CreateMatchRequest(
          gameConfig: gameConfig,
          gameName: 'Test Match',
          playerSlots: [
            PlayerSlot(
              id: 'slot_0',
              playerId: 'player1',
              type: PlayerType.humanLocal,
              playerClassId: 'class1',
              name: 'Player 1',
            ),
          ],
        );

        final user = User(id: 'player1');
        final userState = UserState(user: user);
        when(() => mockUserManager.state).thenReturn(userState);

        when(() => mockLocalRepository.createMatch(any(), any()))
            .thenAnswer((_) async => null);

        // Act
        final result = await useCase.createMatch(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, equals('Failed to create match'));
      });
    });

    group('deleteMatch', () {
      test('should delete match successfully when found', () async {
        // Arrange
        final request = DeleteMatchRequest(matchId: 'match_123');
        final testMatch = GameMatch(
          id: 'match_123',
          gameTypeId: 'test_game',
          creatorId: 'player1',
          createdAt: DateTime.now().millisecondsSinceEpoch,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
        );

        when(() => mockLocalRepository.fetchOpenMatches(any()))
            .thenAnswer((_) async => [testMatch]);
        when(() => mockLocalRepository.deleteMatch(any()))
            .thenAnswer((_) async => true);

        // Act
        final result = await useCase.deleteMatch(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value, isTrue);
        verify(() => mockLocalRepository.deleteMatch('match_123')).called(1);
      });

      test('should return failure when match not found', () async {
        // Arrange
        final request = DeleteMatchRequest(matchId: 'nonexistent_match');

        when(() => mockLocalRepository.fetchOpenMatches(any()))
            .thenAnswer((_) async => []);
        when(() => mockNetworkRepository.fetchOpenMatches(any()))
            .thenAnswer((_) async => []);

        // Act
        final result = await useCase.deleteMatch(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, equals('Match not found'));
      });

      test('should return failure when repository delete fails', () async {
        // Arrange
        final request = DeleteMatchRequest(matchId: 'match_123');
        final testMatch = GameMatch(
          id: 'match_123',
          gameTypeId: 'test_game',
          creatorId: 'player1',
          createdAt: DateTime.now().millisecondsSinceEpoch,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
        );

        when(() => mockLocalRepository.fetchOpenMatches(any()))
            .thenAnswer((_) async => [testMatch]);
        when(() => mockLocalRepository.deleteMatch(any()))
            .thenAnswer((_) async => false);

        // Act
        final result = await useCase.deleteMatch(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, equals('Failed to delete match'));
      });
    });

    group('joinMatch', () {
      test('should join match successfully when found', () async {
        // Arrange
        final request = JoinMatchRequest(matchId: 'match_123', playerId: 'player2');
        final testMatch = GameMatch(
          id: 'match_123',
          gameTypeId: 'test_game',
          creatorId: 'player1',
          createdAt: DateTime.now().millisecondsSinceEpoch,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
        );

        when(() => mockLocalRepository.fetchOpenMatches(any()))
            .thenAnswer((_) async => [testMatch]);
        when(() => mockLocalRepository.joinMatch(any(), playerId: any(named: 'playerId')))
            .thenAnswer((_) async => true);

        // Act
        final result = await useCase.joinMatch(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value, isTrue);
        verify(() => mockLocalRepository.joinMatch('match_123', playerId: 'player2')).called(1);
      });

      test('should return failure when match not found', () async {
        // Arrange
        final request = JoinMatchRequest(matchId: 'nonexistent_match', playerId: 'player2');

        when(() => mockLocalRepository.fetchOpenMatches(any()))
            .thenAnswer((_) async => []);
        when(() => mockNetworkRepository.fetchOpenMatches(any()))
            .thenAnswer((_) async => []);

        // Act
        final result = await useCase.joinMatch(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, equals('Match not found'));
      });
    });

    group('leaveMatch', () {
      test('should leave match successfully when found', () async {
        // Arrange
        final request = LeaveMatchRequest(matchId: 'match_123', playerId: 'player2');
        final testMatch = GameMatch(
          id: 'match_123',
          gameTypeId: 'test_game',
          creatorId: 'player1',
          createdAt: DateTime.now().millisecondsSinceEpoch,
          updatedAt: DateTime.now().millisecondsSinceEpoch,
        );

        when(() => mockLocalRepository.fetchOpenMatches(any()))
            .thenAnswer((_) async => [testMatch]);
        when(() => mockLocalRepository.leaveMatch(any(), playerId: any(named: 'playerId')))
            .thenAnswer((_) async => true);

        // Act
        final result = await useCase.leaveMatch(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value, isTrue);
        verify(() => mockLocalRepository.leaveMatch('match_123', playerId: 'player2')).called(1);
      });
    });

    group('validateCreateMatchRequest', () {
      test('should validate successfully with valid request', () {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        final request = CreateMatchRequest(
          gameConfig: gameConfig,
          gameName: 'Valid Match',
          playerSlots: [
            PlayerSlot(
              id: 'slot_0',
              playerId: 'player1',
              type: PlayerType.humanLocal,
              playerClassId: 'class1',
              name: 'Player 1',
            ),
          ],
        );

        // Act
        final result = useCase.validateCreateMatchRequest(request);

        // Assert
        expect(result.isSuccess, isTrue);
      });

      test('should fail validation with empty game name', () {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        final request = CreateMatchRequest(
          gameConfig: gameConfig,
          gameName: '',
          playerSlots: [
            PlayerSlot(
              id: 'slot_0',
              playerId: 'player1',
              type: PlayerType.humanLocal,
              playerClassId: 'class1',
              name: 'Player 1',
            ),
          ],
        );

        // Act
        final result = useCase.validateCreateMatchRequest(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Game name is required'));
      });

      test('should fail validation with empty player slots', () {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );

        final request = CreateMatchRequest(
          gameConfig: gameConfig,
          gameName: 'Valid Match',
          playerSlots: [],
        );

        // Act
        final result = useCase.validateCreateMatchRequest(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('At least one player slot required'));
      });
    });

    group('determineRepositoryType', () {
      test('should return local for local players only', () {
        // Arrange
        final playerSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
          PlayerSlot(
            id: 'slot_1',
            playerId: 'bot1',
            type: PlayerType.botLocal,
            playerClassId: 'class1',
            name: 'Bot 1',
          ),
        ];

        // Act
        final result = useCase.determineRepositoryType(playerSlots);

        // Assert
        expect(result, equals('local'));
      });

      test('should return network for network players', () {
        // Arrange
        final playerSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
          PlayerSlot(
            id: 'slot_1',
            playerId: 'player2',
            type: PlayerType.humanNetwork,
            playerClassId: 'class1',
            name: 'Player 2',
          ),
        ];

        // Act
        final result = useCase.determineRepositoryType(playerSlots);

        // Assert
        expect(result, equals('network'));
      });

      test('should return network for bot network players', () {
        // Arrange
        final playerSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
          PlayerSlot(
            id: 'slot_1',
            playerId: 'bot1',
            type: PlayerType.botNetwork,
            playerClassId: 'class1',
            name: 'Bot 1',
          ),
        ];

        // Act
        final result = useCase.determineRepositoryType(playerSlots);

        // Assert
        expect(result, equals('network'));
      });
    });

    group('isMatchConfigurationValid', () {
      test('should return true for valid configuration', () {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );
        final playerSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];
        const gameName = 'Test Match';

        // Act
        final result = useCase.isMatchConfigurationValid(gameConfig, playerSlots, gameName);

        // Assert
        expect(result, isTrue);
      });

      test('should return false for null config', () {
        // Arrange
        final playerSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];
        const gameName = 'Test Match';

        // Act
        final result = useCase.isMatchConfigurationValid(null, playerSlots, gameName);

        // Assert
        expect(result, isFalse);
      });

      test('should return false for empty player slots', () {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );
        const gameName = 'Test Match';

        // Act
        final result = useCase.isMatchConfigurationValid(gameConfig, [], gameName);

        // Assert
        expect(result, isFalse);
      });

      test('should return false for empty game name', () {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );
        final playerSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        // Act
        final result = useCase.isMatchConfigurationValid(gameConfig, playerSlots, '');

        // Assert
        expect(result, isFalse);
      });

      test('should return false for null game name', () {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );
        final playerSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];

        // Act
        final result = useCase.isMatchConfigurationValid(gameConfig, playerSlots, null);

        // Assert
        expect(result, isFalse);
      });
    });

    group('isReadyToCreateMatch', () {
      test('should delegate to isMatchConfigurationValid', () {
        // Arrange
        final gameConfig = GameConfig(
          id: 'test_game',
          name: 'Test Game',
        );
        final playerSlots = [
          PlayerSlot(
            id: 'slot_0',
            playerId: 'player1',
            type: PlayerType.humanLocal,
            playerClassId: 'class1',
            name: 'Player 1',
          ),
        ];
        const gameName = 'Test Match';

        // Act
        final result = useCase.isReadyToCreateMatch(gameConfig, playerSlots, gameName);

        // Assert
        expect(result, isTrue);
      });
    });
  });
}
