import 'dart:async';
import 'package:dauntless/repositories/logging/logging_repository.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockLoggingRepository extends Mock implements LoggingRepository {}

void main() {
  group('LoggingUseCase', () {
    late MockLoggingRepository mockRepository;
    late LoggingUseCase loggingUseCase;

    setUp(() {
      mockRepository = MockLoggingRepository();
      loggingUseCase = LoggingUseCase(mockRepository);
    });

    tearDown(() {
      // Clean up any active timers - using reflection to access private method
      try {
        if (loggingUseCase.isTimerActive()) {
          // Timer cleanup will be handled by the use case itself
        }
      } catch (e) {
        // Ignore cleanup errors in tests
      }
    });

    group('Constructor and Initialization', () {
      test('should initialize with correct repository', () {
        expect(loggingUseCase, isNotNull);
        // Can't test private fields directly, but we can test behavior
      });

      test('should initialize without errors', () {
        expect(() => LoggingUseCase(mockRepository), returnsNormally);
      });

      test('should create use case with repository dependency', () {
        final useCase = LoggingUseCase(mockRepository);
        expect(useCase, isNotNull);
      });
    });

    group('Initialization', () {
      test('should complete init without errors', () async {
        await expectLater(loggingUseCase.init(), completes);
      });

      test('should start timer during init', () async {
        await loggingUseCase.init();
        expect(loggingUseCase.isTimerActive(), isTrue);
      });

      test('should call logAppVersion during init', () async {
        // This test verifies that logAppVersion is called
        // Since it's commented out, it should complete without errors
        await expectLater(loggingUseCase.init(), completes);
      });
    });

    group('RemoteLogger Factory', () {
      test('should create RemoteLogger with correct package name', () {
        const packageName = 'TestPackage';
        final remoteLogger = loggingUseCase.getRemoteLogger(packageName);

        expect(remoteLogger, isNotNull);
        expect(remoteLogger.packageName, equals(packageName));
        // Can't test private field directly, but we can test behavior
      });

      test('should create different RemoteLogger instances for different packages', () {
        final logger1 = loggingUseCase.getRemoteLogger('Package1');
        final logger2 = loggingUseCase.getRemoteLogger('Package2');

        expect(logger1, isNot(equals(logger2)));
        expect(logger1.packageName, equals('Package1'));
        expect(logger2.packageName, equals('Package2'));
      });
    });

    group('Timer Management', () {
      test('should have timer active after init', () async {
        await loggingUseCase.init();
        expect(loggingUseCase.isTimerActive(), isTrue);
      });

      test('should report timer status correctly', () {
        // Initially no timer should be active
        expect(loggingUseCase.isTimerActive(), isFalse);
      });

      test('should handle timer state queries', () {
        // Test that isTimerActive method works
        expect(() => loggingUseCase.isTimerActive(), returnsNormally);
      });
    });

    group('App Lifecycle Integration', () {
      test('should handle lifecycle state changes', () {
        // Test that the use case can handle lifecycle states
        // Since _onAppLifeCycleUpdate is private, we test through public interface
        expect(() => loggingUseCase.init(), returnsNormally);
      });

      test('should initialize properly for lifecycle monitoring', () async {
        await loggingUseCase.init();
        // Timer should be active after init, indicating lifecycle monitoring is set up
        expect(loggingUseCase.isTimerActive(), isTrue);
      });
    });

    group('Remote Logger Functionality', () {
      test('should create and use remote logger for debug messages', () {
        final remoteLogger = loggingUseCase.getRemoteLogger('TestPackage');

        // Should complete without errors
        expect(() => remoteLogger.debug('test message'), returnsNormally);
      });

      test('should create and use remote logger for info messages', () {
        final remoteLogger = loggingUseCase.getRemoteLogger('TestPackage');

        expect(() => remoteLogger.info('test message'), returnsNormally);
      });

      test('should create and use remote logger for warn messages', () {
        final remoteLogger = loggingUseCase.getRemoteLogger('TestPackage');

        expect(() => remoteLogger.warn('test message'), returnsNormally);
      });

      test('should create and use remote logger for error messages', () {
        final remoteLogger = loggingUseCase.getRemoteLogger('TestPackage');

        expect(() => remoteLogger.error('test message'), returnsNormally);
      });

      test('should create and use remote logger for critical messages', () {
        final remoteLogger = loggingUseCase.getRemoteLogger('TestPackage');

        expect(() => remoteLogger.critical('test message'), returnsNormally);
      });
    });

    group('Logging Behavior', () {
      test('should handle multiple log messages without errors', () {
        final remoteLogger = loggingUseCase.getRemoteLogger('TestPackage');

        // Should handle multiple messages
        expect(() {
          remoteLogger.debug('debug message');
          remoteLogger.info('info message');
          remoteLogger.warn('warn message');
          remoteLogger.error('error message');
          remoteLogger.critical('critical message');
        }, returnsNormally);
      });

      test('should handle empty messages', () {
        final remoteLogger = loggingUseCase.getRemoteLogger('TestPackage');

        expect(() => remoteLogger.info(''), returnsNormally);
      });

      test('should handle long messages', () {
        final remoteLogger = loggingUseCase.getRemoteLogger('TestPackage');
        final longMessage = 'A' * 1000;

        expect(() => remoteLogger.info(longMessage), returnsNormally);
      });

      test('should handle special characters in messages', () {
        final remoteLogger = loggingUseCase.getRemoteLogger('TestPackage');

        expect(() => remoteLogger.info('Message with 特殊字符 and émojis 🎉'), returnsNormally);
      });
    });

    group('Package Name Handling', () {
      test('should handle different package names', () {
        final logger1 = loggingUseCase.getRemoteLogger('com.example.app');
        final logger2 = loggingUseCase.getRemoteLogger('TestPackage');
        final logger3 = loggingUseCase.getRemoteLogger('');

        expect(() {
          logger1.info('Message from app package');
          logger2.info('Message from test package');
          logger3.info('Message from empty package');
        }, returnsNormally);
      });

      test('should handle special characters in package names', () {
        final logger = loggingUseCase.getRemoteLogger('package.with-special_chars.123');

        expect(() => logger.info('Test message'), returnsNormally);
      });
    });

    group('Error Handling', () {
      test('should handle repository errors gracefully', () {
        final remoteLogger = loggingUseCase.getRemoteLogger('TestPackage');

        // Even if repository throws, logging methods should not propagate errors
        expect(() => remoteLogger.debug('test message'), returnsNormally);
      });

      test('should handle initialization errors gracefully', () async {
        // Should not throw even if some initialization steps fail
        await expectLater(loggingUseCase.init(), completes);
      });

      test('should handle concurrent logging calls', () {
        final remoteLogger = loggingUseCase.getRemoteLogger('TestPackage');

        // Should handle multiple concurrent calls
        expect(() {
          for (int i = 0; i < 10; i++) {
            remoteLogger.info('Concurrent message $i');
          }
        }, returnsNormally);
      });
    });

    group('Integration Scenarios', () {
      test('should handle complete logging workflow', () async {
        await loggingUseCase.init();

        final remoteLogger = loggingUseCase.getRemoteLogger('IntegrationTest');

        // Should complete all logging operations
        expect(() {
          remoteLogger.debug('Debug message');
          remoteLogger.info('Info message');
          remoteLogger.warn('Warning message');
          remoteLogger.error('Error message');
          remoteLogger.critical('Critical message');
        }, returnsNormally);

        expect(loggingUseCase.isTimerActive(), isTrue);
      });

      test('should handle multiple loggers simultaneously', () async {
        await loggingUseCase.init();

        final logger1 = loggingUseCase.getRemoteLogger('Package1');
        final logger2 = loggingUseCase.getRemoteLogger('Package2');
        final logger3 = loggingUseCase.getRemoteLogger('Package3');

        expect(() {
          logger1.info('Message from package 1');
          logger2.warn('Warning from package 2');
          logger3.error('Error from package 3');
        }, returnsNormally);
      });

      test('should handle rapid successive logging calls', () async {
        await loggingUseCase.init();
        final remoteLogger = loggingUseCase.getRemoteLogger('RapidTest');

        expect(() {
          for (int i = 0; i < 100; i++) {
            remoteLogger.info('Rapid message $i');
          }
        }, returnsNormally);
      });
    });
  });

  group('RemoteLogger', () {
    late MockLoggingRepository mockRepository;
    late LoggingUseCase loggingUseCase;
    late RemoteLogger remoteLogger;

    setUp(() {
      mockRepository = MockLoggingRepository();
      loggingUseCase = LoggingUseCase(mockRepository);
      remoteLogger = RemoteLogger(loggingUseCase, 'TestPackage');
    });

    test('should delegate debug calls to LoggingUseCase', () async {
      // Should complete without errors
      expect(() => remoteLogger.debug('debug message'), returnsNormally);
    });

    test('should delegate info calls to LoggingUseCase', () async {
      expect(() => remoteLogger.info('info message'), returnsNormally);
    });

    test('should delegate warn calls to LoggingUseCase', () async {
      expect(() => remoteLogger.warn('warn message'), returnsNormally);
    });

    test('should delegate error calls to LoggingUseCase', () async {
      expect(() => remoteLogger.error('error message'), returnsNormally);
    });

    test('should delegate critical calls to LoggingUseCase', () async {
      expect(() => remoteLogger.critical('critical message'), returnsNormally);
    });
  });

  group('LogLevel', () {
    test('should have correct string representations', () {
      expect(LogLevel.debug.toString(), equals('DEBUG'));
      expect(LogLevel.info.toString(), equals('INFO'));
      expect(LogLevel.warn.toString(), equals('WARN'));
      expect(LogLevel.error.toString(), equals('ERROR'));
      expect(LogLevel.critical.toString(), equals('CRITICAL'));
    });

    test('should parse from string correctly', () {
      expect(LogLevel.fromString('DEBUG'), equals(LogLevel.debug));
      expect(LogLevel.fromString('INFO'), equals(LogLevel.info));
      expect(LogLevel.fromString('WARN'), equals(LogLevel.warn));
      expect(LogLevel.fromString('ERROR'), equals(LogLevel.error));
      expect(LogLevel.fromString('CRITICAL'), equals(LogLevel.critical));
    });

    test('should return null for invalid strings', () {
      expect(LogLevel.fromString('INVALID'), isNull);
      expect(LogLevel.fromString(''), isNull);
      expect(LogLevel.fromString('debug'), isNull); // case sensitive
    });

    test('should have correct level values', () {
      expect(LogLevel.debug.level, equals('DEBUG'));
      expect(LogLevel.info.level, equals('INFO'));
      expect(LogLevel.warn.level, equals('WARN'));
      expect(LogLevel.error.level, equals('ERROR'));
      expect(LogLevel.critical.level, equals('CRITICAL'));
    });
  });
}
