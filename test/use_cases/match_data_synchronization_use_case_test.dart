import 'package:test/test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:get_it/get_it.dart';
import 'package:common/models/game_match.dart';
import 'package:common/models/match_status.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:dauntless/models/common/result.dart';
import 'package:dauntless/models/requests/match_management_requests.dart';
import 'package:dauntless/models/results/match_management_results.dart';
import 'package:dauntless/repositories/interfaces/match_repository_interface.dart';
import 'package:dauntless/use_cases/logging_use_case.dart';
import 'package:dauntless/use_cases/match_data_synchronization_use_case.dart';

// Mock classes
class MockMatchRepository extends Mock implements MatchRepositoryInterface {}
class MockRemoteLogger extends Mock implements RemoteLogger {}

// Register fallback values for mocktail
void _registerFallbackValues() {
  registerFallbackValue(const LoadMatchDataRequest());
  registerFallbackValue(const RefreshMatchesRequest(sourceName: 'test'));
  registerFallbackValue(<GameMatch>[]);
  registerFallbackValue(<String, List<GameMatch>>{});
}

void main() {
  _registerFallbackValues();

  group('MatchDataSynchronizationUseCase', () {
    late MatchDataSynchronizationUseCase useCase;
    late MockMatchRepository mockLocalRepository;
    late MockMatchRepository mockNetworkRepository;
    late MockRemoteLogger mockLogger;

    setUp(() {
      mockLocalRepository = MockMatchRepository();
      mockNetworkRepository = MockMatchRepository();
      mockLogger = MockRemoteLogger();

      // Setup logger mocks
      when(() => mockLogger.info(any())).thenReturn(null);
      when(() => mockLogger.warn(any())).thenReturn(null);
      when(() => mockLogger.error(any())).thenReturn(null);

      // Reset GetIt
      GetIt.instance.reset();

      useCase = MatchDataSynchronizationUseCase(
        mockLogger,
      );
    });

    tearDown(() {
      GetIt.instance.reset();
    });

    group('loadMatchData', () {
      test('should handle empty repositories gracefully', () async {
        // Arrange - no repositories registered
        final request = LoadMatchDataRequest(gameName: 'test_game');

        // Act
        final result = await useCase.loadMatchData(request);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value!.allMatches, isEmpty);
        expect(result.value!.availableSources, isEmpty);
        expect(result.value!.hasNetworkCapability, isFalse);
      });


    });

    group('refreshMatchesFromSource', () {


      test('should fail when source is not available', () async {
        // Arrange
        final request = RefreshMatchesRequest(sourceName: 'nonexistent');

        // Act
        final result = await useCase.refreshMatchesFromSource(request);

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('Source nonexistent not available'));
      });
    });

    group('subscribeToMatchUpdates', () {
      test('should fail when no network repository available', () async {
        // Arrange - no repositories registered

        // Act
        final result = await useCase.subscribeToMatchUpdates();

        // Assert
        expect(result.isFailure, isTrue);
        expect(result.error, contains('No network repository available'));
      });

      test('should succeed when network repository is available', () async {
        // Arrange
        when(() => mockNetworkRepository.supportsRealTimeUpdates).thenReturn(true);
        GetIt.instance.pushNewScope(scopeName: 'serverConnected');
        GetIt.instance.registerSingleton<MatchRepositoryInterface>(
          mockNetworkRepository,
          instanceName: 'network',
        );

        // Act
        final result = await useCase.subscribeToMatchUpdates();

        // Assert
        expect(result.isSuccess, isTrue);
      });
    });

    group('unsubscribeFromMatchUpdates', () {
      test('should unsubscribe from match updates successfully', () async {
        // Act
        final result = await useCase.unsubscribeFromMatchUpdates();

        // Assert
        expect(result.isSuccess, isTrue);
      });
    });

    group('handleMatchUpdate', () {
      test('should handle match update successfully', () {
        // Arrange
        final matches = [_createTestMatch('match_1', 'Match 1')];
        const sourceName = 'local';

        // Act
        final result = useCase.handleMatchUpdate(matches, sourceName);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value!.updatedMatches, hasLength(1));
        expect(result.value!.sourceName, equals('local'));
        expect(result.value!.requiresStateUpdate, isTrue);
      });
    });

    group('handleWebSocketMatchUpdate', () {
      test('should handle WebSocket match update successfully', () {
        // Arrange
        final matches = [_createTestMatch('match_1', 'Match 1')];

        // Act
        final result = useCase.handleWebSocketMatchUpdate(matches);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value!.updatedMatches, hasLength(1));
        expect(result.value!.sourceName, equals('network'));
        expect(result.value!.requiresStateUpdate, isTrue);
      });
    });

    group('clearNetworkMatches', () {
      test('should clear network matches successfully', () {
        // Arrange
        final currentData = MatchDataResult(
          allMatches: [
            _createTestMatch('local_1', 'Local 1'),
            _createTestMatch('network_1', 'Network 1'),
          ],
          matchesBySource: {
            'local': [_createTestMatch('local_1', 'Local 1')],
            'network': [_createTestMatch('network_1', 'Network 1')],
          },
          availableSources: ['local', 'network'],
          hasNetworkCapability: true,
        );

        // Act
        final result = useCase.clearNetworkMatches(currentData);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.value!.matchesBySource.containsKey('network'), isFalse);
        expect(result.value!.availableSources, isNot(contains('network')));
        expect(result.value!.hasNetworkCapability, isFalse);
      });
    });

    group('prioritizeSourceMatches', () {
      test('should return all matches from all sources', () {
        // Arrange
        final localMatch = _createTestMatch('match_1', 'Local Match');
        final networkMatch = _createTestMatch('match_2', 'Network Match');

        final matchesBySource = {
          'local': [localMatch],
          'network': [networkMatch],
        };

        // Act
        final result = useCase.prioritizeSourceMatches(matchesBySource);

        // Assert
        expect(result, hasLength(2)); // Returns all matches, doesn't deduplicate
        expect(result.map((m) => m.id), containsAll(['match_1', 'match_2']));
      });

      test('should handle duplicate IDs by returning all matches', () {
        // Arrange
        final localMatch = _createTestMatch('match_1', 'Local Match');
        final networkMatch = _createTestMatch('match_1', 'Network Match');

        final matchesBySource = {
          'local': [localMatch],
          'network': [networkMatch],
        };

        // Act
        final result = useCase.prioritizeSourceMatches(matchesBySource);

        // Assert
        expect(result, hasLength(2)); // Returns both matches even with same ID
        expect(result.map((m) => m.gameName), containsAll(['Local Match', 'Network Match']));
      });
    });

    group('deduplicateMatches', () {
      test('should deduplicate matches correctly', () {
        // Arrange
        final match1 = _createTestMatch('match_1', 'Match 1');
        final match2 = _createTestMatch('match_2', 'Match 2');
        final match1Duplicate = _createTestMatch('match_1', 'Match 1 Duplicate');

        final matchesBySource = {
          'local': [match1, match2],
          'network': [match1Duplicate], // Duplicate of match1
        };

        // Act
        final result = useCase.deduplicateMatches(matchesBySource);

        // Assert
        expect(result, hasLength(2)); // Should have only 2 unique matches
        expect(result.map((m) => m.id), containsAll(['match_1', 'match_2']));
      });

      test('should handle empty matches', () {
        // Arrange
        final matchesBySource = <String, List<GameMatch>>{};

        // Act
        final result = useCase.deduplicateMatches(matchesBySource);

        // Assert
        expect(result, isEmpty);
      });
    });
  });
}

// Helper function to create test matches
GameMatch _createTestMatch(String id, String name) {
  return GameMatch(
    id: id,
    gameTypeId: 'test_game',
    creatorId: 'test_creator',
    gameName: name,
    createdAt: DateTime.now().millisecondsSinceEpoch,
    updatedAt: DateTime.now().millisecondsSinceEpoch,
    status: MatchStatus.open,
    isOpenForJoining: true,
    playerSlots: [
      PlayerSlot(
        id: 'slot_0',
        playerId: 'player1',
        type: PlayerType.humanLocal,
        playerClassId: 'class1',
        name: 'Player 1',
      ),
    ],
  );
}
