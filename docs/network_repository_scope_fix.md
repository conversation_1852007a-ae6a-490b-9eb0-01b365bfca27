# Network Repository Scope Fix

## Issue Description
On app launch, the `MatchManagementBloc` was showing a warning about the network match repository not being available:

```
WARNING: MatchManagementBloc—Network match repository not available: Bad state: GetIt: Object/factory with with name network and type MatchRepositoryInterface is not registered inside GetIt.
```

## Root Cause Analysis

### **Scoped Dependency Issue**
The network match repository is registered with a **scope** in the dependency injection container:

```dart
@Named('network')
@preResolve
@Singleton()
@Scope(staticServerConnected)  // ← This is the issue!
Future<MatchRepositoryInterface> networkMatchRepository(...)
```

### **The Problem Flow**
1. **App launches** → `MatchManagementBloc` initializes
2. **Tries to check network sources** → Calls `_checkAndAddNetworkSources()`
3. **Attempts to access network repository** → `GetIt.I<MatchRepositoryInterface>(instanceName: 'network')`
4. **Repository not available** → Not in `staticServerConnected` scope yet
5. **Warning logged** → Network repository not registered

### **Scope Lifecycle Issue**
- **Network repository** only available in `staticServerConnected` scope
- **App initialization** happens before any server connection
- **Scope not active** during initial repository detection
- **Repository access fails** with GetIt registration error

## Solution Applied

### **Graceful Scope Detection ✅**
```dart
// BEFORE (Problematic)
try {
  final networkUseCase = GetIt.I<MatchSelectionUseCase>();  // ❌ Wrong type
  if (networkUseCase is NetworkMatchSelectionUseCase) {
    // ...
  }
} catch (e) {
  _logger.warn('Network match use case not available: $e');
}

// AFTER (Fixed)
if (GetIt.I.currentScopeName == 'staticServerConnected') {
  try {
    final networkRepository = GetIt.I<MatchRepositoryInterface>(instanceName: 'network');  // ✅ Correct type and name
    if (networkRepository != null) {
      final adapter = _NetworkRepositoryAdapter.fromRepository(networkRepository);
      _matchRepositories['Network'] = adapter;
      return true;
    }
  } catch (e) {
    _logger.warn('Network match repository not available: $e');
  }
} else {
  _logger.info('Not in server-connected scope, network sources unavailable');  // ✅ Clear explanation
}
```

### **Enhanced Network Repository Adapter ✅**
```dart
class _NetworkRepositoryAdapter implements MatchRepositoryInterface {
  final MatchSelectionUseCase? _useCase;
  final MatchRepositoryInterface? _repository;

  // Original constructor for use case compatibility
  _NetworkRepositoryAdapter(this._useCase) : _repository = null;
  
  // New constructor for direct repository access
  _NetworkRepositoryAdapter.fromRepository(this._repository) : _useCase = null;

  @override
  Future<List<GameMatch>> fetchOpenMatches(String gameName) async {
    if (_useCase != null) {
      return await _useCase!.fetchOpenMatches(gameName);
    } else if (_repository != null) {
      return await _repository!.fetchOpenMatches(gameName);  // ✅ Direct repository access
    }
    return [];
  }
  
  // ... other methods handle both cases
}
```

### **Better Error Handling ✅**
```dart
} else {
  _logger.info('Not in server-connected scope, network sources unavailable');
}

return false;
} catch (e) {
  _logger.warn('Failed to check network sources: $e');
  return false;
}
```

## Benefits Achieved

### **✅ No More Startup Warnings**
- **Graceful scope detection** → No attempts to access unavailable dependencies
- **Clear logging** → Explains why network sources aren't available
- **Clean startup** → No error messages during normal initialization

### **✅ Proper Scope Handling**
- **Respects dependency scopes** → Only accesses network repository when available
- **Dynamic network detection** → Adds network sources when server connects
- **Fallback behavior** → Works without network sources initially

### **✅ Enhanced Adapter Flexibility**
- **Dual constructor support** → Works with use cases or repositories
- **Backward compatibility** → Existing use case integration still works
- **Forward compatibility** → Direct repository access for new architecture

## Expected Behavior Now

### **App Launch (No Server Connection)**
1. **MatchManagementBloc initializes** → Checks for network sources
2. **Detects no server scope** → Logs "Not in server-connected scope"
3. **Continues with local sources** → No warnings or errors
4. **UI shows local matches only** → Network sources unavailable

### **Server Connection Established**
1. **User connects to server** → `staticServerConnected` scope activated
2. **Network repository registered** → Available in GetIt container
3. **Network sources detected** → `_checkAndAddNetworkSources()` succeeds
4. **UI shows network matches** → Server matches become available

### **Debug Logging**
Expected console output when working correctly:

#### **App Launch (No Connection)**
```
MatchManagementBloc: Not in server-connected scope, network sources unavailable
MatchManagementBloc: MatchManagementBloc initialized with 1 configs and 1 sources
```

#### **After Server Connection**
```
MatchManagementBloc: Added network match repository source
MatchManagementBloc: Subscribed to WebSocket open matches updates
```

## Files Modified

### **Core Logic**
- `lib/ui/liberator/blocs/match_management/match_management_bloc.dart`
  - Enhanced `_checkAndAddNetworkSources` with proper scope detection
  - Updated network repository access to use correct type and name
  - Added `_NetworkRepositoryAdapter.fromRepository` constructor
  - Updated adapter methods to handle both use cases and repositories
  - Improved error handling and logging

## Dependency Injection Architecture

### **Scope-Based Registration**
```dart
// Network repository only available in server-connected scope
@Named('network')
@Scope(staticServerConnected)
Future<MatchRepositoryInterface> networkMatchRepository(...)

// Local repository always available
@Named('local')
MatchRepositoryInterface localMatchRepository(...)
```

### **Scope Lifecycle**
```
App Launch → Main Scope (local repositories available)
     ↓
Server Connect → staticServerConnected Scope (network repositories available)
     ↓
Server Disconnect → Main Scope (network repositories unavailable)
```

### **Repository Access Pattern**
```dart
// Check scope before accessing scoped dependencies
if (GetIt.I.currentScopeName == 'staticServerConnected') {
  // Safe to access network repository
  final networkRepo = GetIt.I<MatchRepositoryInterface>(instanceName: 'network');
} else {
  // Network repository not available
}
```

## Long-Term Benefits

### **✅ Clean Startup Experience**
- **No warning messages** during normal app launch
- **Clear status indication** about network availability
- **Graceful degradation** when network unavailable

### **✅ Proper Architecture**
- **Respects dependency scopes** and lifecycle
- **Dynamic capability detection** based on available services
- **Separation of concerns** between local and network operations

### **✅ Maintainable Code**
- **Clear error handling** for scope-related issues
- **Flexible adapter pattern** supporting multiple backend types
- **Consistent logging** for troubleshooting

---

*Fixed on: 2025-08-04*
*Issue: Network repository access during app initialization before server connection*
*Status: ✅ RESOLVED*
*Solution: Graceful scope detection and enhanced repository adapter*
