# Phase 2 Migration Guide: From Old BLoCs to MatchManagementBloc

## Overview

This guide provides a step-by-step approach for migrating from the deprecated BLoCs to the new consolidated `MatchManagementBloc`. The migration is designed to be gradual and safe, allowing for thorough testing at each step.

## Deprecated Components

The following components are now deprecated and will be removed in Phase 3:

### 🚫 **Deprecated BLoCs**
- `MatchSelectionBloc` → Use `MatchManagementBloc`
- `CreateMatchBloc` → Use `MatchManagementBloc`
- `MatchSelectionEnvironmentManager` → Use `MatchManagementBloc`

### 🚫 **Deprecated Events/States**
- All events from the old BLoCs have equivalents in `MatchManagementEvent`
- All state properties are available in `MatchManagementState`

## Migration Strategy

### **Phase 2.1: Preparation (Current)**
- ✅ Old BLoCs marked as `@deprecated`
- ✅ New `MatchManagementBloc` available
- ✅ Both systems coexist safely

### **Phase 2.2: UI Component Migration**
- Migrate UI components one by one
- Test thoroughly after each migration
- Keep rollback capability

### **Phase 2.3: Cleanup**
- Remove deprecated BLoCs
- Clean up unused imports
- Update documentation

## Event Migration Map

### MatchSelectionBloc → MatchManagementBloc

| Old Event | New Event | Notes |
|-----------|-----------|-------|
| `LoadMatchSelectionDataEvent` | `LoadMatchDataEvent` | Same functionality |
| `JoinSelectedMatchEvent` | `JoinSelectedMatchEvent` | Same functionality |
| `JoinMatchEvent` | `JoinMatchEvent` | Same functionality |
| `LeaveMatchEvent` | `LeaveMatchEvent` | Same functionality |
| `SubscribeToOpenMatchesEvent` | `SubscribeToMatchUpdatesEvent` | Enhanced with multi-source support |
| `UnsubscribeFromOpenMatchesEvent` | `UnsubscribeFromMatchUpdatesEvent` | Enhanced with multi-source support |

### CreateMatchBloc → MatchManagementBloc

| Old Event | New Event | Notes |
|-----------|-----------|-------|
| `InitializeCreateMatchEvent` | `StartMatchCreationEvent` | Switches to creation mode |
| `SelectMatchConfigEvent` | `SelectMatchConfigEvent` | Same functionality |
| `AddPlayerSlotEvent` | `AddPlayerSlotEvent` | Same functionality |
| `RemovePlayerSlotEvent` | `RemovePlayerSlotEvent` | Same functionality |
| `UpdatePlayerTypeEvent` | `UpdatePlayerTypeEvent` | Same functionality |
| `UpdatePlayerNameEvent` | `UpdatePlayerNameEvent` | Same functionality |
| `SetHostPlayerEvent` | `SetHostPlayerEvent` | Enhanced implementation |
| `StartMatchEvent` | `CreateAndStartMatchEvent` | Enhanced with options |
| `JoinPlayerEvent` | `JoinPlayerSlotEvent` | Same functionality |
| `UpdateGameNameEvent` | `UpdateGameNameEvent` | Same functionality |

## State Migration Map

### MatchSelectionState → MatchManagementState

| Old Property | New Property | Notes |
|--------------|--------------|-------|
| `processingStatus` | `processingStatus` | Same |
| `openMatches` | `openMatches` | Same |
| `isSubscribedToUpdates` | `isSubscribedToUpdates` | Same |
| `serverProfileSelectorExpanded` | `serverProfileSelectorExpanded` | Same |
| `errorMessage` | `errorMessage` | Same |

### CreateMatchState → MatchManagementState

| Old Property | New Property | Notes |
|--------------|--------------|-------|
| `processingStatus` | `processingStatus` | Same |
| `availableConfigs` | `availableConfigs` | Same |
| `selectedConfigId` | `selectedConfigId` | Same |
| `gameName` | `gameName` | Same |
| `matchId` | `matchId` | Same |
| `playerSlots` | `playerSlots` | Same |
| `hasNetworkCapability` | `hasNetworkCapability` | Same |

## Migration Examples

### Example 1: Simple BlocProvider Migration

**Before:**
```dart
BlocProvider(
  create: (_) => CreateMatchBloc(
    GetIt.I<GameConfigUseCase>(),
    GetIt.I<MatchSelectionEnvironmentManager>(),
    GetIt.I<LoggingUseCase>().getRemoteLogger('CreateMatchBloc')
  )..add(const InitializeCreateMatchEvent()),
  child: MyWidget(),
)
```

**After:**
```dart
BlocProvider.value(
  value: GetIt.I<MatchManagementBloc>()
    ..add(const InitializeMatchManagementEvent())
    ..add(const StartMatchCreationEvent()),
  child: MyWidget(),
)
```

### Example 2: Event Migration

**Before:**
```dart
context.read<CreateMatchBloc>().add(const AddPlayerSlotEvent());
```

**After:**
```dart
context.read<MatchManagementBloc>().add(const AddPlayerSlotEvent());
```

### Example 3: State Access Migration

**Before:**
```dart
BlocBuilder<CreateMatchBloc, CreateMatchState>(
  builder: (context, state) {
    if (state.isCreatingMatch) {
      return CreateMatchView();
    }
    return Container();
  },
)
```

**After:**
```dart
BlocBuilder<MatchManagementBloc, MatchManagementState>(
  builder: (context, state) {
    if (state.isCreatingMatch) {
      return CreateMatchView();
    }
    return Container();
  },
)
```

### Example 4: Multi-BLoC to Single BLoC Migration

**Before:**
```dart
MultiBlocProvider(
  providers: [
    BlocProvider<MatchSelectionBloc>(/* ... */),
    BlocProvider<CreateMatchBloc>(/* ... */),
  ],
  child: MyWidget(),
)
```

**After:**
```dart
BlocProvider.value(
  value: GetIt.I<MatchManagementBloc>(),
  child: MyWidget(),
)
```

## Component-Specific Migration

### MatchManagementScreen Migration

**Priority: High** - This is the main screen using all deprecated BLoCs

**Current Issues:**
- Uses `MatchSelectionEnvironmentManager`
- Creates `CreateMatchBloc` instances
- Complex multi-BLoC coordination

**Migration Steps:**
1. Replace `MatchSelectionEnvironmentManager` with `MatchManagementBloc`
2. Remove `CreateMatchBloc` instantiation
3. Update event dispatching
4. Test match creation and selection flows

### OpenMatchesPanel Migration

**Priority: Medium** - Uses `MatchSelectionBloc`

**Migration Steps:**
1. Change `BlocBuilder<MatchSelectionBloc, MatchSelectionState>` to `BlocBuilder<MatchManagementBloc, MatchManagementState>`
2. Update event dispatching to use new events
3. Test match selection and joining

### Player Management Components Migration

**Priority: Low** - Uses `CreateMatchBloc` for player slot management

**Migration Steps:**
1. Update BlocBuilder types
2. Change event dispatching
3. Test player slot operations

## Testing Strategy

### 1. **Unit Tests**
- Test event mapping functions
- Test state conversion helpers
- Test new BLoC functionality

### 2. **Integration Tests**
- Test complete match creation flow
- Test match selection and joining
- Test multi-source coordination

### 3. **UI Tests**
- Test each migrated component
- Test user workflows end-to-end
- Test error scenarios

## Rollback Plan

If issues are discovered during migration:

1. **Immediate Rollback**
   - Revert to using deprecated BLoCs
   - Old BLoCs remain functional during transition

2. **Partial Rollback**
   - Keep successfully migrated components on new BLoC
   - Revert problematic components to old BLoCs

3. **Debug and Fix**
   - Use both systems in parallel for comparison
   - Fix issues in new BLoC implementation
   - Re-attempt migration

## Timeline

### **Week 1: Preparation**
- ✅ Mark old BLoCs as deprecated
- ✅ Create migration documentation
- ✅ Set up testing framework

### **Week 2: Core Component Migration**
- Migrate `MatchManagementScreen`
- Migrate `OpenMatchesPanel`
- Test critical user flows

### **Week 3: Remaining Components**
- Migrate player management components
- Migrate utility components
- Comprehensive testing

### **Week 4: Cleanup**
- Remove deprecated BLoCs
- Clean up imports and dependencies
- Update documentation

## Benefits After Migration

### **Simplified Architecture**
- Single BLoC instead of three
- Unified event system
- Consistent state management

### **Enhanced Features**
- Multi-source match coordination
- Real-time updates
- Better error handling

### **Improved Maintainability**
- Single point of control
- Repository-based data access
- Cleaner dependency injection

### **Better Performance**
- Reduced memory usage
- Fewer BLoC instances
- Optimized state updates

---

*Last Updated: 2025-08-04*
*Phase: 2 - BLoC Consolidation & Migration*
