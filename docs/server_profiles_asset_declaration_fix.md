# Server Profiles Asset Declaration Fix

## Issue Identified from Debug Logs

The debug logs revealed the final issue:

```
SERVER_ENVIRONMENT_USE_CASE: Falling back to asset config at assets/config/server_config.json
SERVER_ENVIRONMENT_USE_CASE: Error loading asset config: Unable to load asset: "assets/config/server_config.json".
The asset does not exist or has empty data.
SERVER_ENVIRONMENT_USE_CASE: Returning empty default config
```

## Root Cause

The `assets/config/server_config.json` file exists in the project directory, but **Flutter couldn't find it** because it wasn't declared in the `pubspec.yaml` file.

### **Missing Asset Declaration**
The `pubspec.yaml` file only included:
```yaml
assets:
  - assets/themes/  # ✅ Declared
  # - assets/config/  # ❌ Missing!
```

Flutter only bundles assets that are explicitly declared in `pubspec.yaml`. Since `assets/config/` wasn't listed, the `server_config.json` file wasn't included in the app bundle.

## Solution Applied

### **Added Asset Declaration ✅**
```yaml
# Updated pubspec.yaml
assets:
  - assets/themes/
  - assets/config/  # ✅ Added config directory
```

### **Refreshed Dependencies ✅**
```bash
flutter pub get  # ✅ Updates asset bundle
```

## Expected Result

After adding the asset declaration and running `flutter pub get`, the app needs to be **rebuilt** to include the new asset.

### **Expected Debug Logs**
```
SERVER_ENVIRONMENT_USE_CASE: Falling back to asset config at assets/config/server_config.json
SERVER_ENVIRONMENT_USE_CASE: Successfully loaded asset config with 2 profiles
ServerEnvironmentManager: Loaded config with 2 profiles
ServerEnvironmentManager: Selected profile: local
ServerEnvironmentManager: State updated with 2 profiles
```

### **Expected UI Behavior**
- **✅ "production" profile** - dauntless.servegame.com:2187
- **✅ "local" profile** - localhost:2187 (pre-selected)
- **✅ No "No server profiles available" message**

## Files Modified

### **Configuration**
- `pubspec.yaml`
  - Added `- assets/config/` to assets section
  - Ensures `server_config.json` is bundled with app

### **Asset Structure**
```
assets/
├── config/
│   └── server_config.json  # ✅ Now properly bundled
└── themes/
    └── ...
```

## Flutter Asset System

### **How Flutter Assets Work**
1. **Declaration Required**: Assets must be listed in `pubspec.yaml`
2. **Bundle Creation**: `flutter pub get` updates the asset bundle
3. **Runtime Access**: `rootBundle.loadString()` can access bundled assets
4. **Build Inclusion**: Assets are included in app builds

### **Asset Loading Hierarchy**
```dart
// 1. Try writable file (user data)
File('server_config.json').exists()

// 2. Fall back to bundled asset (app bundle)
rootBundle.loadString('assets/config/server_config.json')

// 3. Fall back to default (empty config)
ServerEnvironmentConfig()
```

## Next Steps

### **Rebuild Required**
The app must be **rebuilt** (not just hot reloaded) to include the new asset:

```bash
# Stop the current debug session
# Then restart with:
flutter run
```

### **Verification**
After rebuilding, check the debug logs for:
- ✅ Successful asset loading
- ✅ 2 profiles loaded
- ✅ "local" profile selected

## Prevention

### **Asset Checklist**
When adding new assets:
1. **Create the asset file** in appropriate directory
2. **Declare in pubspec.yaml** under assets section
3. **Run `flutter pub get`** to update bundle
4. **Rebuild the app** to include new assets
5. **Test asset loading** in debug mode

### **Common Asset Issues**
- **File exists but not declared** → Asset not found error
- **Declared but file missing** → Asset not found error
- **Wrong path in declaration** → Asset not found error
- **Forgot `flutter pub get`** → Old bundle without new assets

---

*Final Asset Fix Applied: 2025-08-04*
*Issue: Asset not declared in pubspec.yaml*
*Status: ✅ RESOLVED*
*Action: Added assets/config/ to pubspec.yaml and ran flutter pub get*
*Next: Rebuild app to include new asset bundle*
