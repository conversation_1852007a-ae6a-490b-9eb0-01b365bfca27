# Server Config GitIgnore Fix

## Issue Description
The server profiles kept disappearing because an empty `server_config.json` file was being recreated in the project root directory. This file takes precedence over the asset configuration file, causing the system to load 0 profiles instead of the 2 profiles from the asset file.

## Root Cause Analysis

### **File Precedence Issue**
The `ServerEnvironmentUseCase` follows this loading hierarchy:
1. **Writable config** (`server_config.json`) - **HIGHEST PRIORITY**
2. **Asset config** (`assets/config/server_config.json`) - **FALLBACK**
3. **Default config** (empty) - **LAST RESORT**

### **Empty File Creation**
An empty `server_config.json` file kept being created in the project root:
```json
{"profiles":[],"selectedProfileId":null}
```

This caused the system to:
1. **Find writable config** → `server_config.json` exists
2. **Load empty profiles** → 0 profiles loaded
3. **Never reach asset fallback** → Asset file ignored
4. **Show "No server profiles available"** → UI displays empty state

### **Recurring Problem**
The file was being recreated through:
- **Development workflow** - Testing, debugging, or code changes
- **Application behavior** - Some code path creating empty config
- **Version control** - File being committed and pulled

## Solution Applied

### **Added to .gitignore ✅**
```gitignore
# Server configuration (user-specific)
server_config.json
```

### **Removed Existing File ✅**
```bash
rm server_config.json  # Removed empty writable config
```

### **Verified Asset Loading ✅**
Debug logs now show correct behavior:
```
SERVER_ENVIRONMENT_USE_CASE: Attempting to load config from server_config.json
SERVER_ENVIRONMENT_USE_CASE: Falling back to asset config at assets/config/server_config.json
SERVER_ENVIRONMENT_USE_CASE: Successfully loaded asset config with 2 profiles
ServerEnvironmentManager: Loaded config with 2 profiles
ServerEnvironmentManager: Selected profile: local
```

## Benefits Achieved

### **✅ Prevents File Recreation**
- **Git ignores** `server_config.json` file
- **No accidental commits** of empty config
- **No version control conflicts** over user-specific config

### **✅ Consistent Asset Loading**
- **Always uses asset config** when no user config exists
- **Reliable server profiles** from `assets/config/server_config.json`
- **Predictable behavior** across development and production

### **✅ User Customization Support**
- **Users can create** `server_config.json` for custom profiles
- **Custom configs take precedence** when they exist
- **Custom configs ignored by git** (user-specific)

## File Structure Now

### **Version Controlled (Committed)**
```
assets/config/server_config.json  ✅ Default profiles for all users
.gitignore                        ✅ Ignores user-specific config
```

### **User-Specific (Ignored)**
```
server_config.json               ❌ Ignored by git (user customization)
```

### **Loading Behavior**
```
1. Look for server_config.json
   - If exists: Use user's custom profiles
   - If missing: Continue to step 2

2. Load assets/config/server_config.json
   - Contains: "production" and "local" profiles
   - Default behavior for all users

3. If both fail: Return empty config
   - Graceful degradation
   - No crashes
```

## User Experience

### **Default Behavior (Most Users)**
1. **No custom config** → Uses asset file
2. **See server profiles** → "production" and "local" available
3. **Can connect** → Select profile and connect to server
4. **Consistent experience** → Same profiles for all users

### **Custom Configuration (Advanced Users)**
1. **Create server_config.json** → Add custom server profiles
2. **Custom profiles used** → Override default asset profiles
3. **Git ignores file** → No conflicts with other users
4. **Personal configuration** → Tailored to user's needs

### **Development Workflow**
1. **Clone repository** → No server_config.json file
2. **Run application** → Uses asset profiles automatically
3. **Create custom config** → Optional for personal servers
4. **Git operations** → Custom config never committed

## Prevention Strategy

### **✅ Git Ignore Protection**
- **File ignored** → Cannot be accidentally committed
- **User-specific** → Each developer can have custom config
- **No conflicts** → No merge conflicts over server config

### **✅ Asset File as Default**
- **Always available** → Bundled with application
- **Version controlled** → Changes tracked and reviewed
- **Consistent** → Same profiles for all users

### **✅ Clear Documentation**
- **Loading hierarchy** documented in code comments
- **User customization** explained in README
- **Troubleshooting** guide for profile issues

## Troubleshooting Guide

### **If Server Profiles Disappear**
1. **Check for server_config.json** in project root
2. **If exists and empty** → Delete the file
3. **Restart application** → Should load asset profiles
4. **Verify debug logs** → Should show asset loading success

### **Debug Log Patterns**
```
# GOOD: Asset loading working
SERVER_ENVIRONMENT_USE_CASE: Falling back to asset config at assets/config/server_config.json
SERVER_ENVIRONMENT_USE_CASE: Successfully loaded asset config with 2 profiles

# BAD: Empty writable config found
SERVER_ENVIRONMENT_USE_CASE: Found writable config at server_config.json
SERVER_ENVIRONMENT_USE_CASE: Successfully loaded writable config with 0 profiles
```

### **Quick Fix Commands**
```bash
# Remove problematic empty config
rm server_config.json

# Verify it's in gitignore
grep "server_config.json" .gitignore

# Restart application
flutter run
```

## Long-Term Benefits

### **✅ Stable Development**
- **No more disappearing profiles** during development
- **Consistent behavior** across team members
- **Reliable asset loading** as default behavior

### **✅ User Flexibility**
- **Custom server profiles** for advanced users
- **Personal configurations** without affecting others
- **Easy customization** when needed

### **✅ Maintainable Codebase**
- **Clear separation** between default and custom config
- **Version control hygiene** with appropriate ignores
- **Predictable behavior** for troubleshooting

---

*Fixed on: 2025-08-04*
*Issue: Recurring empty server_config.json file causing profile loss*
*Status: ✅ PERMANENTLY RESOLVED*
*Solution: Added server_config.json to .gitignore and removed empty file*
