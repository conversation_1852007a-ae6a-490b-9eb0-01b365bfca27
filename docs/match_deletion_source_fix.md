# Match Deletion Source Fix

## Issue Description
When trying to delete a server match, the system was incorrectly attempting to delete it as a local match. The logs showed that a server match (ID: `*************-81d04c6c`) was being processed by the `LocalMatchRepository` instead of the appropriate server repository.

## Root Cause Analysis

### **Incorrect Repository Selection**
The `_onDeleteMatch` method was using a "try all repositories" approach:

```dart
// BEFORE (Problematic)
for (final repository in _matchRepositories.values) {
  try {
    final success = await repository.deleteMatch(event.matchId);
    if (success) {
      deleted = true;
      break;  // ❌ Stops at first "success", even if wrong repository
    }
  } catch (e) {
    // Continue to next repository
  }
}
```

### **The Problem Flow**
1. **User deletes server match** → `DeleteMatchEvent` triggered
2. **System tries local repository first** → `LocalMatchRepository.deleteMatch()`
3. **Local repository "succeeds"** → Returns true (even if match doesn't exist locally)
4. **System stops trying** → Never reaches server repository
5. **Match not actually deleted** → Still exists on server

### **Missing Source Tracking**
The system has `matchesBySource` state that tracks which matches came from which source, but the deletion logic wasn't using this information to route to the correct repository.

## Solution Applied

### **Source-Aware Deletion Logic ✅**
```dart
// AFTER (Fixed)
// Find which source the match belongs to
String? matchSource;
for (final entry in state.matchesBySource.entries) {
  final sourceName = entry.key;
  final matches = entry.value;
  if (matches.any((match) => match.id == event.matchId)) {
    matchSource = sourceName;
    break;
  }
}

if (matchSource == null) {
  _logger.warn('Could not find source for match ${event.matchId}');
  emit(state.setError('Match not found in any source'));
  return;
}

// Get the repository for the match source
final repository = _matchRepositories[matchSource];
if (repository == null) {
  _logger.warn('No repository found for source: $matchSource');
  emit(state.setError('No repository available for match source'));
  return;
}

_logger.info('Deleting match ${event.matchId} from source: $matchSource');

// Delete from the correct repository
final success = await repository.deleteMatch(event.matchId);
```

### **Enhanced Logging ✅**
```dart
_logger.info('Deleting match ${event.matchId} from source: $matchSource');
_logger.info('Successfully deleted match: ${event.matchId} from $matchSource');
```

### **Better Error Handling ✅**
```dart
if (matchSource == null) {
  _logger.warn('Could not find source for match ${event.matchId}');
  emit(state.setError('Match not found in any source'));
  return;
}

if (repository == null) {
  _logger.warn('No repository found for source: $matchSource');
  emit(state.setError('No repository available for match source'));
  return;
}
```

## Benefits Achieved

### **✅ Correct Repository Routing**
- **Local matches** → Deleted via `LocalMatchRepository`
- **Server matches** → Deleted via network repository
- **No cross-contamination** → Each match type handled correctly

### **✅ Reliable Deletion**
- **Server matches** actually deleted from server
- **Local matches** actually deleted from local storage
- **No false positives** → Only reports success when actually deleted

### **✅ Better Debugging**
- **Source identification** logged for troubleshooting
- **Repository selection** visible in logs
- **Clear error messages** when source not found

## Expected Behavior Now

### **Server Match Deletion**
1. **User deletes server match** → `DeleteMatchEvent` triggered
2. **System identifies source** → Finds match in network source
3. **Routes to server repository** → Uses network repository
4. **Deletes from server** → Actually removes match from server
5. **Updates UI** → Match disappears from all clients

### **Local Match Deletion**
1. **User deletes local match** → `DeleteMatchEvent` triggered
2. **System identifies source** → Finds match in local source
3. **Routes to local repository** → Uses `LocalMatchRepository`
4. **Deletes from local storage** → Removes match file
5. **Updates UI** → Match disappears from local list

### **Debug Logging**
Expected console output when working correctly:
```
# Server match deletion
MatchManagementBloc: Deleting match *************-81d04c6c from source: NetworkSource
MatchManagementBloc: Successfully deleted match: *************-81d04c6c from NetworkSource

# Local match deletion
MatchManagementBloc: Deleting match local-match-123 from source: LocalSource
MatchManagementBloc: Successfully deleted match: local-match-123 from LocalSource
```

## Files Modified

### **Core Logic**
- `lib/ui/liberator/blocs/match_management/match_management_bloc.dart`
  - Enhanced `_onDeleteMatch` method with source-aware routing
  - Added source identification logic
  - Improved error handling and logging
  - Removed "try all repositories" approach

## Match Source Tracking

### **How Sources Are Tracked**
The `MatchManagementState` maintains `matchesBySource`:
```dart
Map<String, List<GameMatch>> matchesBySource = {
  'LocalSource': [localMatch1, localMatch2, ...],
  'NetworkSource': [serverMatch1, serverMatch2, ...],
  // ... other sources
}
```

### **Source Identification Process**
1. **Iterate through sources** → Check each source's match list
2. **Find matching ID** → Locate which source contains the match
3. **Get repository** → Use source name to get correct repository
4. **Route deletion** → Call deleteMatch on correct repository

### **Repository Mapping**
```dart
Map<String, MatchRepositoryInterface> _matchRepositories = {
  'LocalSource': LocalMatchRepository(),
  'NetworkSource': NetworkMatchRepository(),
  // ... other repositories
}
```

## Error Scenarios Handled

### **✅ Match Not Found**
```dart
if (matchSource == null) {
  emit(state.setError('Match not found in any source'));
  return;
}
```

### **✅ Repository Not Available**
```dart
if (repository == null) {
  emit(state.setError('No repository available for match source'));
  return;
}
```

### **✅ Deletion Failure**
```dart
if (!success) {
  emit(state.setError('Failed to delete match from $matchSource'));
}
```

## Testing Strategy

### **Manual Testing**
1. **Create server match** → Verify appears in server match list
2. **Delete server match** → Check logs show network repository used
3. **Verify server deletion** → Match should disappear from all clients
4. **Create local match** → Verify appears in local match list
5. **Delete local match** → Check logs show local repository used
6. **Verify local deletion** → Match file should be removed

### **Expected Results**
- ✅ **Server matches** deleted from server (visible to all clients)
- ✅ **Local matches** deleted from local storage (files removed)
- ✅ **Correct logging** shows appropriate repository used
- ✅ **No cross-contamination** between local and server operations

## Long-Term Benefits

### **✅ Reliable Match Management**
- **Correct deletion** for all match types
- **No orphaned matches** on server or locally
- **Consistent behavior** across different match sources

### **✅ Better User Experience**
- **Matches actually deleted** when user requests deletion
- **Real-time updates** work correctly for server matches
- **No confusion** between local and server operations

### **✅ Maintainable Code**
- **Clear separation** of concerns between repositories
- **Source-aware logic** that's easy to understand
- **Proper error handling** for edge cases

---

*Fixed on: 2025-08-04*
*Issue: Server matches being deleted via local repository*
*Status: ✅ RESOLVED*
*Solution: Source-aware deletion routing to correct repository*
