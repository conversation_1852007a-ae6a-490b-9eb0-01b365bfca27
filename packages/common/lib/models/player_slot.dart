import 'package:common/models/player_class.dart';
import 'package:common/models/player_type.dart';
import 'package:freezed_annotation/freezed_annotation.dart';


part 'player_slot.freezed.dart';
part 'player_slot.g.dart';

/// Represents a player slot in a match
@freezed
abstract class PlayerSlot with _$PlayerSlot {
  const factory PlayerSlot({
    required String id,
    String? playerId,
    @JsonKey(fromJson: _playerTypeFromJson, toJson: _playerTypeToJson)
    required PlayerType type,
    required String playerClassId,
    String? name,
  }) = _PlayerSlot;

  /// Create from JSON
  factory PlayerSlot.fromJson(Map<String, dynamic> json) =>
      _$PlayerSlotFromJson(json);

  factory PlayerSlot.fromPlayerClass(PlayerClass playerClass) => PlayerSlot(
        id: playerClass.id,
        name: playerClass.defaultPlayerName ?? playerClass.name,
        type: PlayerType.humanLocal,
        playerClassId: playerClass.id,
      );
}

// Helper function to convert string player type to enum
PlayerType _playerTypeFromJson(String type) {
  switch (type) {
    case 'humanLocal':
      return PlayerType.humanLocal;
    case 'humanNetwork':
      return PlayerType.humanNetwork;
    case 'botLocal':
      return PlayerType.botLocal;
    case 'botNetwork':
      return PlayerType.botNetwork;
    default:
      return PlayerType.humanLocal;
  }
}

// Helper function to convert enum player type to string
String _playerTypeToJson(PlayerType type) {
  switch (type) {
    case PlayerType.humanLocal:
      return 'humanLocal';
    case PlayerType.humanNetwork:
      return 'humanNetwork';
    case PlayerType.botLocal:
      return 'botLocal';
    case PlayerType.botNetwork:
      return 'botNetwork';
  }
}
