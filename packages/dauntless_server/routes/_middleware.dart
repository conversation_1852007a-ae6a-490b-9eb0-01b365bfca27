import 'package:dart_frog/dart_frog.dart';

import 'package:dauntless_server/controllers/game_match_controller.dart';
import 'package:dauntless_server/websockets/websocket_manager.dart';

/// Provider middleware for making controllers available throughout the app
Handler middleware(Handler handler) {
  // Initialize WebSocketManager singleton to ensure it subscribes to EventBus events
  WebSocketManager();

  return handler.use(provider<GameMatchController>((context) => GameMatchController()));
}
