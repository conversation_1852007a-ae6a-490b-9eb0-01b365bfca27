import 'dart:convert';
import 'dart:io';

import 'package:dart_frog/dart_frog.dart';
import 'package:dauntless_server/controllers/game_match_controller.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

import '../../../routes/api/turns.dart' as route;

class _MockRequestContext extends Mock implements RequestContext {}
class _MockRequest extends Mock implements Request {}

void main() {
  group('POST /api/turns', () {
    late _MockRequestContext mockContext;
    late _MockRequest mockRequest;

    setUp(() {
      mockContext = _MockRequestContext();
      mockRequest = _MockRequest();

      when(() => mockContext.request).thenReturn(mockRequest);
      when(() => mockRequest.method).thenReturn(HttpMethod.post);
    });

    group('Request Structure Validation', () {
      test('should accept valid turn submission request structure', () async {
        // Arrange
        final requestBody = jsonEncode({
          'type': 'turn_submission',
          'playerId': 'player-123',
          'matchId': 'match-456',
          'actions': [
            {'type': 'move', 'target': 'location-1'},
            {'type': 'attack', 'target': 'enemy-1'},
          ],
        });

        when(() => mockRequest.body()).thenAnswer((_) async => requestBody);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        // Since we can't mock the controller easily, we expect either success or 404 (match not found)
        // Both indicate the request was properly parsed and validated
        expect(response.statusCode, anyOf(equals(HttpStatus.ok), equals(HttpStatus.notFound)));
      });

      test('should accept turn submission with empty actions', () async {
        // Arrange
        final requestBody = jsonEncode({
          'type': 'turn_submission',
          'playerId': 'player-123',
          'matchId': 'match-456',
          'actions': [],
        });

        when(() => mockRequest.body()).thenAnswer((_) async => requestBody);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, anyOf(equals(HttpStatus.ok), equals(HttpStatus.notFound)));
      });

      test('should accept complex action data structures', () async {
        // Arrange
        final requestBody = jsonEncode({
          'type': 'turn_submission',
          'playerId': 'player-123',
          'matchId': 'match-456',
          'actions': [
            {
              'type': 'complex_action',
              'target': 'location-1',
              'parameters': {
                'duration': 5,
                'resources': ['energy', 'materials'],
                'conditions': {
                  'weather': 'clear',
                  'visibility': 'high',
                },
              },
            },
          ],
        });

        when(() => mockRequest.body()).thenAnswer((_) async => requestBody);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, anyOf(equals(HttpStatus.ok), equals(HttpStatus.notFound)));
      });
    });

    group('Request Validation', () {
      test('should reject non-POST requests', () async {
        // Arrange
        when(() => mockRequest.method).thenReturn(HttpMethod.get);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(405));
        
        final responseBody = await response.body();
        expect(responseBody, equals('Method not allowed'));
      });

      test('should reject request missing type field', () async {
        // Arrange
        final requestBody = jsonEncode({
          'playerId': 'player-123',
          'matchId': 'match-456',
          'actions': [],
        });

        when(() => mockRequest.body()).thenAnswer((_) async => requestBody);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(HttpStatus.badRequest));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        
        expect(responseData['error'], equals('Invalid request format'));
        expect(responseData['message'], contains('type'));
      });

      test('should reject request with wrong type', () async {
        // Arrange
        final requestBody = jsonEncode({
          'type': 'wrong_type',
          'playerId': 'player-123',
          'matchId': 'match-456',
          'actions': [],
        });

        when(() => mockRequest.body()).thenAnswer((_) async => requestBody);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(HttpStatus.badRequest));

        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;

        expect(responseData['error'], equals('Invalid request format'));
      });

      test('should reject request missing playerId field', () async {
        // Arrange
        final requestBody = jsonEncode({
          'type': 'turn_submission',
          'matchId': 'match-456',
          'actions': [],
        });

        when(() => mockRequest.body()).thenAnswer((_) async => requestBody);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(HttpStatus.badRequest));

        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;

        expect(responseData['error'], equals('Invalid request format'));
        expect(responseData['message'], contains('playerId'));
      });

      test('should reject request missing matchId field', () async {
        // Arrange
        final requestBody = jsonEncode({
          'type': 'turn_submission',
          'playerId': 'player-123',
          'actions': [],
        });

        when(() => mockRequest.body()).thenAnswer((_) async => requestBody);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(HttpStatus.badRequest));

        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;

        expect(responseData['error'], equals('Invalid request format'));
        expect(responseData['message'], contains('matchId'));
      });

      test('should reject request missing actions field', () async {
        // Arrange
        final requestBody = jsonEncode({
          'type': 'turn_submission',
          'playerId': 'player-123',
          'matchId': 'match-456',
        });

        when(() => mockRequest.body()).thenAnswer((_) async => requestBody);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(HttpStatus.badRequest));

        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;

        expect(responseData['error'], equals('Invalid request format'));
        expect(responseData['message'], contains('actions'));
      });

      test('should handle malformed JSON request', () async {
        // Arrange
        const malformedJson = '{"type": "turn_submission", "playerId": "player-123"';

        when(() => mockRequest.body()).thenAnswer((_) async => malformedJson);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(HttpStatus.internalServerError));

        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;

        expect(responseData['error'], equals('Server error'));
        expect(responseData['message'], isNotNull);
      });
    });

    group('Error Handling', () {
      test('should handle nonexistent match gracefully', () async {
        // Arrange
        final requestBody = jsonEncode({
          'type': 'turn_submission',
          'playerId': 'player-123',
          'matchId': 'nonexistent-match',
          'actions': [],
        });

        when(() => mockRequest.body()).thenAnswer((_) async => requestBody);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        // Should return 404 for nonexistent match or handle gracefully
        expect(response.statusCode, anyOf(
          equals(HttpStatus.notFound),
          equals(HttpStatus.internalServerError),
          equals(HttpStatus.ok)
        ));
      });
    });

    group('Edge Cases', () {
      test('should handle very large action payloads', () async {
        // Arrange
        final largeActions = List.generate(100, (index) => {
          'type': 'action_$index',
          'target': 'target_$index',
          'data': List.generate(50, (i) => 'data_item_$i'),
        });

        final requestBody = jsonEncode({
          'type': 'turn_submission',
          'playerId': 'player-123',
          'matchId': 'match-456',
          'actions': largeActions,
        });

        when(() => mockRequest.body()).thenAnswer((_) async => requestBody);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, anyOf(
          equals(HttpStatus.ok),
          equals(HttpStatus.notFound),
          equals(HttpStatus.internalServerError)
        ));
      });

      test('should handle special characters in player and match IDs', () async {
        // Arrange
        final requestBody = jsonEncode({
          'type': 'turn_submission',
          'playerId': 'player-123-äöü-@#\$',
          'matchId': 'match-456-éñç-!%&',
          'actions': [],
        });

        when(() => mockRequest.body()).thenAnswer((_) async => requestBody);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, anyOf(
          equals(HttpStatus.ok),
          equals(HttpStatus.notFound),
          equals(HttpStatus.internalServerError)
        ));
      });
    });
  });
}
