import 'dart:convert';
import 'dart:async';

import 'package:dauntless_server/websockets/websocket_manager.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class _MockWebSocketChannel extends Mock implements WebSocketChannel {}
class _MockWebSocketSink extends Mock implements WebSocketSink {}

void main() {
  group('WebSocketManager', () {
    late WebSocketManager manager;
    late _MockWebSocketChannel mockChannel1;
    late _MockWebSocketChannel mockChannel2;
    late _MockWebSocketSink mockSink1;
    late _MockWebSocketSink mockSink2;

    setUp(() {
      manager = WebSocketManager();
      mockChannel1 = _MockWebSocketChannel();
      mockChannel2 = _MockWebSocketChannel();
      mockSink1 = _MockWebSocketSink();
      mockSink2 = _MockWebSocketSink();

      when(() => mockChannel1.sink).thenReturn(mockSink1);
      when(() => mockChannel2.sink).thenReturn(mockSink2);

      // Mock the close method to return a completed Future
      when(() => mockSink1.close()).thenAnswer((_) async {});
      when(() => mockSink2.close()).thenAnswer((_) async {});

      // Mock the add method to avoid issues
      when(() => mockSink1.add(any())).thenReturn(null);
      when(() => mockSink2.add(any())).thenReturn(null);
    });

    tearDown(() {
      // Don't dispose the singleton manager in tests as it affects other tests
      // manager.dispose();
    });

    group('Singleton Pattern', () {
      test('should return same instance for multiple calls', () {
        // Arrange & Act
        final instance1 = WebSocketManager();
        final instance2 = WebSocketManager();
        
        // Assert
        expect(identical(instance1, instance2), isTrue);
      });

      test('should maintain state across instance calls', () {
        // Arrange
        final instance1 = WebSocketManager();
        instance1.addClient('client-1', mockChannel1);
        
        // Act
        final instance2 = WebSocketManager();
        
        // Assert
        expect(identical(instance1, instance2), isTrue);
        // State should be maintained (tested indirectly through singleton behavior)
      });
    });

    group('Client Management', () {
      test('should add client successfully', () {
        // Arrange
        const clientId = 'client-123';
        
        // Act
        manager.addClient(clientId, mockChannel1);
        
        // Assert
        // Verify client was added (tested indirectly through other operations)
        expect(() => manager.addClient(clientId, mockChannel1), returnsNormally);
      });

      test('should remove client successfully', () {
        // Arrange
        const clientId = 'client-123';
        manager.addClient(clientId, mockChannel1);
        
        // Act
        manager.removeClient(clientId);
        
        // Assert
        verify(() => mockSink1.close()).called(1);
      });

      test('should handle removing non-existent client', () {
        // Arrange
        const nonExistentClientId = 'non-existent-client';
        
        // Act & Assert
        expect(() => manager.removeClient(nonExistentClientId), returnsNormally);
      });

      test('should handle multiple clients', () {
        // Arrange
        const clientId1 = 'client-1';
        const clientId2 = 'client-2';
        
        // Act
        manager.addClient(clientId1, mockChannel1);
        manager.addClient(clientId2, mockChannel2);
        
        // Assert
        expect(() => manager.addClient(clientId1, mockChannel1), returnsNormally);
        expect(() => manager.addClient(clientId2, mockChannel2), returnsNormally);
      });

      test('should replace client with same ID', () {
        // Arrange
        const clientId = 'client-123';
        manager.addClient(clientId, mockChannel1);
        
        // Act
        manager.addClient(clientId, mockChannel2); // Replace with new channel
        
        // Assert
        // Old channel should be replaced with new one
        expect(() => manager.addClient(clientId, mockChannel2), returnsNormally);
      });
    });

    group('Open Matches Subscription', () {
      test('should subscribe client to open matches', () {
        // Arrange
        const clientId = 'client-123';
        manager.addClient(clientId, mockChannel1);

        // Act
        manager.subscribeClientToOpenMatches(clientId);

        // Assert
        expect(() => manager.subscribeClientToOpenMatches(clientId), returnsNormally);
      });

      test('should unsubscribe client from open matches', () {
        // Arrange
        const clientId = 'client-123';
        manager.addClient(clientId, mockChannel1);
        manager.subscribeClientToOpenMatches(clientId);

        // Act
        manager.unsubscribeClientFromOpenMatches(clientId);

        // Assert
        expect(() => manager.unsubscribeClientFromOpenMatches(clientId), returnsNormally);
      });

      test('should handle subscribing non-existent client', () {
        // Arrange
        const nonExistentClientId = 'non-existent-client';

        // Act & Assert
        expect(() => manager.subscribeClientToOpenMatches(nonExistentClientId), returnsNormally);
      });

      test('should handle unsubscribing non-subscribed client', () {
        // Arrange
        const clientId = 'client-123';
        manager.addClient(clientId, mockChannel1);

        // Act & Assert
        expect(() => manager.unsubscribeClientFromOpenMatches(clientId), returnsNormally);
      });
    });

    group('Match Subscription', () {
      test('should subscribe client to specific match', () {
        // Arrange
        const clientId = 'client-123';
        const matchId = 'match-456';
        manager.addClient(clientId, mockChannel1);
        
        // Act
        manager.subscribeClientToMatch(clientId, matchId);
        
        // Assert
        expect(() => manager.subscribeClientToMatch(clientId, matchId), returnsNormally);
      });

      test('should unsubscribe client from specific match', () {
        // Arrange
        const clientId = 'client-123';
        const matchId = 'match-456';
        manager.addClient(clientId, mockChannel1);
        manager.subscribeClientToMatch(clientId, matchId);
        
        // Act
        manager.unsubscribeClientFromMatch(clientId, matchId);
        
        // Assert
        expect(() => manager.unsubscribeClientFromMatch(clientId, matchId), returnsNormally);
      });

      test('should handle multiple clients subscribing to same match', () {
        // Arrange
        const clientId1 = 'client-1';
        const clientId2 = 'client-2';
        const matchId = 'match-456';
        
        manager.addClient(clientId1, mockChannel1);
        manager.addClient(clientId2, mockChannel2);
        
        // Act
        manager.subscribeClientToMatch(clientId1, matchId);
        manager.subscribeClientToMatch(clientId2, matchId);
        
        // Assert
        expect(() => manager.subscribeClientToMatch(clientId1, matchId), returnsNormally);
        expect(() => manager.subscribeClientToMatch(clientId2, matchId), returnsNormally);
      });

      test('should handle client subscribing to multiple matches', () {
        // Arrange
        const clientId = 'client-123';
        const matchId1 = 'match-1';
        const matchId2 = 'match-2';
        
        manager.addClient(clientId, mockChannel1);
        
        // Act
        manager.subscribeClientToMatch(clientId, matchId1);
        manager.subscribeClientToMatch(clientId, matchId2);
        
        // Assert
        expect(() => manager.subscribeClientToMatch(clientId, matchId1), returnsNormally);
        expect(() => manager.subscribeClientToMatch(clientId, matchId2), returnsNormally);
      });
    });

    group('Message Broadcasting', () {
      test('should broadcast match update to subscribed clients', () {
        // Arrange
        const clientId = 'client-123';
        const matchId = 'match-456';
        final matchData = {'status': 'updated', 'turn': 5};

        // Use a unique client ID to avoid state conflicts
        final uniqueClientId = 'client-broadcast-${DateTime.now().millisecondsSinceEpoch}';
        final uniqueMatchId = 'match-broadcast-${DateTime.now().millisecondsSinceEpoch}';

        manager.addClient(uniqueClientId, mockChannel1);
        manager.subscribeClientToMatch(uniqueClientId, uniqueMatchId);

        // Reset mock after setup to count only the broadcast call
        reset(mockSink1);
        when(() => mockSink1.add(any())).thenReturn(null);

        // Act
        manager.broadcastMatchUpdate(uniqueMatchId, matchData);

        // Assert
        verify(() => mockSink1.add(any())).called(1);
      });

      test('should not broadcast to unsubscribed clients', () {
        // Arrange
        final uniqueClientId = 'client-unsubscribed-${DateTime.now().millisecondsSinceEpoch}';
        final uniqueMatchId = 'match-unsubscribed-${DateTime.now().millisecondsSinceEpoch}';
        final matchData = {'status': 'updated'};

        manager.addClient(uniqueClientId, mockChannel1);
        // Note: Not subscribing client to match

        // Reset mock after setup to count only the broadcast call
        reset(mockSink1);
        when(() => mockSink1.add(any())).thenReturn(null);

        // Act
        manager.broadcastMatchUpdate(uniqueMatchId, matchData);

        // Assert
        verifyNever(() => mockSink1.add(any()));
      });

      test('should broadcast open matches update to subscribed clients', () {
        // Arrange
        const clientId = 'client-123';
        final matchesData = [
          {'id': 'match-1', 'status': 'open'},
          {'id': 'match-2', 'status': 'open'},
        ];

        // Use a unique client ID to avoid state conflicts
        final uniqueClientId = 'client-open-${DateTime.now().millisecondsSinceEpoch}';

        manager.addClient(uniqueClientId, mockChannel1);
        manager.subscribeClientToOpenMatches(uniqueClientId);

        // Reset mock after setup to count only the broadcast call
        reset(mockSink1);
        when(() => mockSink1.add(any())).thenReturn(null);

        // Act
        manager.broadcastOpenMatchesUpdate(matchesData);

        // Assert
        verify(() => mockSink1.add(any())).called(1);
      });

      test('should handle broadcasting with no subscribers', () {
        // Arrange
        const matchId = 'match-456';
        final matchData = {'status': 'updated'};

        // Act & Assert
        expect(() => manager.broadcastMatchUpdate(matchId, matchData), returnsNormally);
      });
    });

    group('Stream Management', () {
      test('should provide match stream', () {
        // Arrange & Act
        final stream = manager.matchStream;

        // Assert
        expect(stream, isA<Stream<Map<String, dynamic>>>());
      });

      test('should emit match updates to stream', () async {
        // Arrange
        const matchId = 'match-456';
        final matchData = {'status': 'updated'};
        final streamEvents = <Map<String, dynamic>>[];

        final subscription = manager.matchStream.listen(streamEvents.add);

        // Act
        manager.broadcastMatchUpdate(matchId, matchData);

        // Wait for stream event
        await Future.delayed(const Duration(milliseconds: 10));

        // Assert
        expect(streamEvents, hasLength(1));
        expect(streamEvents.first['match_id'], equals(matchId));
        expect(streamEvents.first['data'], equals(matchData));

        await subscription.cancel();
      });
    });

    group('Resource Management', () {
      test('should dispose all resources', () {
        // Arrange
        final uniqueClientId1 = 'client-dispose-1-${DateTime.now().millisecondsSinceEpoch}';
        final uniqueClientId2 = 'client-dispose-2-${DateTime.now().millisecondsSinceEpoch}';

        // Reset mocks and set up fresh
        reset(mockSink1);
        reset(mockSink2);
        reset(mockChannel1);
        reset(mockChannel2);

        // Re-setup all mocks after reset
        when(() => mockChannel1.sink).thenReturn(mockSink1);
        when(() => mockChannel2.sink).thenReturn(mockSink2);
        when(() => mockSink1.close()).thenAnswer((_) async {});
        when(() => mockSink2.close()).thenAnswer((_) async {});
        when(() => mockSink1.add(any())).thenReturn(null);
        when(() => mockSink2.add(any())).thenReturn(null);

        manager.addClient(uniqueClientId1, mockChannel1);
        manager.addClient(uniqueClientId2, mockChannel2);

        // Act
        manager.dispose();

        // Assert
        verify(() => mockSink1.close()).called(1);
        verify(() => mockSink2.close()).called(1);
      });

      test('should handle dispose with no clients', () {
        // Arrange - ensure no clients are added
        // (WebSocketManager singleton may have clients from other tests)

        // Act & Assert - dispose should not throw even if there are existing clients
        expect(() => manager.dispose(), returnsNormally);
      });

      test('should clean up subscriptions when removing client', () {
        // Arrange
        final uniqueClientId = 'client-cleanup-${DateTime.now().millisecondsSinceEpoch}';
        final uniqueMatchId = 'match-cleanup-${DateTime.now().millisecondsSinceEpoch}';

        // Reset and setup mocks
        reset(mockSink1);
        reset(mockChannel1);
        when(() => mockChannel1.sink).thenReturn(mockSink1);
        when(() => mockSink1.close()).thenAnswer((_) async {});
        when(() => mockSink1.add(any())).thenReturn(null);

        manager.addClient(uniqueClientId, mockChannel1);
        manager.subscribeClientToMatch(uniqueClientId, uniqueMatchId);
        manager.subscribeClientToOpenMatches(uniqueClientId);

        // Act
        manager.removeClient(uniqueClientId);

        // Assert
        verify(() => mockSink1.close()).called(1);

        // Verify client is removed from all subscriptions
        // (tested indirectly - no messages should be sent after removal)
        manager.broadcastMatchUpdate(uniqueMatchId, {'test': 'data'});
        manager.broadcastOpenMatchesUpdate([{'test': 'data'}]);

        // The close should have been called exactly once during removeClient
        // No additional verification needed
      });
    });
  });
}
